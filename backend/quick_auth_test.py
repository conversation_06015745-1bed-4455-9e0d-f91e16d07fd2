#!/usr/bin/env python3
"""
Quick authentication test without git operations.
"""
import requests
import json


def test_login():
    """Test login functionality."""
    print("🔐 Testing Login...")
    
    # Test login with existing user
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            data=login_data
        )
        
        if response.status_code == 200:
            token_data = response.json()
            print("✅ Login successful")
            print(f"   Token received: {len(token_data['access_token'])} characters")
            return token_data["access_token"]
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None


def test_protected_endpoint(token):
    """Test protected endpoint access."""
    print("\n🔒 Testing Protected Endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/users/me",
            headers=headers
        )
        
        if response.status_code == 200:
            user_data = response.json()
            print("✅ Protected endpoint access successful")
            print(f"   User: {user_data.get('email', 'Unknown')}")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False


def test_topic_search(token):
    """Test topic search with authentication."""
    print("\n🔍 Testing Topic Search...")
    
    headers = {"Authorization": f"Bearer {token}"}
    search_data = {
        "query": "diabetes diagnosis",
        "limit": 5,
        "score_threshold": 0.6
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/search/topic",
            json=search_data,
            headers=headers
        )
        
        if response.status_code == 200:
            results = response.json()
            print("✅ Topic search successful")
            print(f"   Results: {results.get('total_results', 0)}")
            print(f"   Sections: {list(results.get('grouped_results', {}).keys())}")
            return True
        else:
            print(f"❌ Topic search failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Topic search error: {e}")
        return False


def test_cors():
    """Test CORS headers."""
    print("\n🌐 Testing CORS...")
    
    headers = {"Origin": "http://localhost:3001"}
    
    try:
        response = requests.options(
            "http://localhost:8000/api/v1/auth/login",
            headers=headers
        )
        
        cors_headers = response.headers
        if "Access-Control-Allow-Origin" in cors_headers:
            print("✅ CORS headers present")
            print(f"   Allow-Origin: {cors_headers.get('Access-Control-Allow-Origin')}")
            return True
        else:
            print("❌ CORS headers missing")
            return False
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Quick Authentication Test\n")
    
    # Test login
    token = test_login()
    if not token:
        print("\n❌ Login failed, cannot continue with other tests")
        exit(1)
    
    # Test protected endpoint
    protected_success = test_protected_endpoint(token)
    
    # Test topic search
    search_success = test_topic_search(token)
    
    # Test CORS
    cors_success = test_cors()
    
    print("\n" + "=" * 50)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 50)
    print(f"Login: {'✅ PASS' if token else '❌ FAIL'}")
    print(f"Protected Endpoint: {'✅ PASS' if protected_success else '❌ FAIL'}")
    print(f"Topic Search: {'✅ PASS' if search_success else '❌ FAIL'}")
    print(f"CORS: {'✅ PASS' if cors_success else '❌ FAIL'}")
    
    all_passed = token and protected_success and search_success and cors_success
    
    if all_passed:
        print("\n🎉 All tests passed! Frontend should work now.")
        print("\n💡 Try logging in at: http://localhost:3001")
        print("   Username: <EMAIL>")
        print("   Password: testpass123")
    else:
        print("\n❌ Some tests failed.")
    
    exit(0 if all_passed else 1)
