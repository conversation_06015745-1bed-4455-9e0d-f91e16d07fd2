"""
API router for version 1 of the Medical Preparation Platform API.
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, search, admin, users, health, qa, books

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(books.router, prefix="/books", tags=["books"])
api_router.include_router(search.router, prefix="/search", tags=["search"])
api_router.include_router(qa.router, prefix="/qa", tags=["qa"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
