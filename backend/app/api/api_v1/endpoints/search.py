"""
Search endpoints for semantic search and retrieval.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.dependencies import get_current_active_user, get_optional_current_user
from app.db.session import get_db
from app.db.models import User
from app.schemas.search import (
    SearchRequest, SearchResponse, SearchResult, SimilarChunksRequest,
    AutocompleteRequest, AutocompleteResponse, SearchHistoryResponse,
    TopicSearchRequest, TopicSearchResponse
)
from app.services.search_service import SearchService
from app.services.enhanced_qdrant_service import get_qdrant_service
from app.core.logging import get_logger

logger = get_logger("api.search")
router = APIRouter()


@router.post("/", response_model=SearchResponse)
async def search(
    search_request: SearchRequest,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
) -> Dict[str, Any]:
    """
    Perform semantic search across medical content.
    """
    try:
        search_service = SearchService(db)

        # Perform search
        results, search_time = await search_service.semantic_search(
            query=search_request.query,
            limit=search_request.limit,
            score_threshold=search_request.score_threshold,
            filters=search_request.filters,
            user_id=current_user.id if current_user else None
        )

        logger.info(f"Search performed: '{search_request.query}' - {len(results)} results")

        return {
            "query": search_request.query,
            "total_results": len(results),
            "results": results,
            "search_time_ms": search_time,
            "filters_applied": search_request.filters
        }

    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )


@router.post("/topic", response_model=TopicSearchResponse)
async def search_topic(
    request: TopicSearchRequest,
    current_user: Optional[User] = Depends(get_optional_current_user)
) -> Dict[str, Any]:
    """
    Search for medical topics with results grouped by section type.

    This endpoint provides offline semantic search using local embeddings
    and returns results organized by medical section types (Diagnosis, Treatment, etc.).

    Args:
        request: Topic search request containing query and parameters
        current_user: Authenticated user (optional)

    Returns:
        Grouped search results by section type with citations
    """
    try:
        # Validate query
        if not request.query or not request.query.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Query cannot be empty"
            )

        # Get Qdrant service
        qdrant_service = get_qdrant_service()

        # Perform topic search
        grouped_results = qdrant_service.search_topic(
            query=request.query.strip(),
            limit=request.limit,
            score_threshold=request.score_threshold
        )

        # Handle empty results
        if not grouped_results or all(len(results) == 0 for results in grouped_results.values()):
            user_id = current_user.id if current_user else "anonymous"
            logger.info(f"No results found for topic search: '{request.query}' by user {user_id}")
            return {
                "query": request.query,
                "total_results": 0,
                "grouped_results": {},
                "message": "No relevant content found for your search query. Try using different keywords or broader terms.",
                "suggestions": [
                    "Try broader medical terms",
                    "Check spelling of medical terminology",
                    "Use common disease or condition names",
                    "Search for symptoms or treatments"
                ]
            }

        # Calculate total results
        total_results = sum(len(results) for results in grouped_results.values())

        # Log successful search
        user_id = current_user.id if current_user else "anonymous"
        logger.info(f"Topic search completed for user {user_id}: '{request.query}' - {total_results} results in {len(grouped_results)} sections")

        return {
            "query": request.query,
            "total_results": total_results,
            "grouped_results": grouped_results,
            "message": f"Found {total_results} relevant results across {len(grouped_results)} medical sections.",
            "suggestions": []
        }

    except HTTPException:
        raise
    except Exception as e:
        user_id = current_user.id if current_user else "anonymous"
        logger.error(f"Topic search error for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Topic search failed. Please try again."
        )


@router.get("/service-info")
async def get_search_service_info(
    current_user: Optional[User] = Depends(get_optional_current_user)
) -> Dict[str, Any]:
    """
    Get information about the search service configuration.

    Args:
        current_user: Authenticated user (optional)

    Returns:
        Service configuration information
    """
    try:
        qdrant_service = get_qdrant_service()
        service_info = qdrant_service.get_service_info()

        user_id = current_user.id if current_user else "anonymous"
        logger.info(f"Search service info requested by user {user_id}")

        return {
            "status": "success",
            "service_info": service_info,
            "features": {
                "offline_search": True,
                "local_embeddings": True,
                "section_grouping": True,
                "citation_support": True
            }
        }

    except Exception as e:
        logger.error(f"Error getting service info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get service information"
        )


@router.get("/suggestions", response_model=AutocompleteResponse)
async def get_search_suggestions(
    q: str = Query(..., min_length=1, max_length=100, description="Query for suggestions"),
    limit: int = Query(default=10, ge=1, le=20, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get search suggestions for autocomplete.
    """
    try:
        search_service = SearchService(db)
        suggestions = search_service.get_search_suggestions(q, limit)

        return {"suggestions": suggestions}

    except Exception as e:
        logger.error(f"Error getting suggestions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get suggestions"
        )


@router.post("/similar", response_model=List[SearchResult])
async def find_similar_chunks(
    request: SimilarChunksRequest,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
) -> List[SearchResult]:
    """
    Find chunks similar to a given chunk.
    """
    try:
        search_service = SearchService(db)

        results = await search_service.find_similar_chunks(
            chunk_id=request.chunk_id,
            limit=request.limit,
            score_threshold=request.score_threshold,
            exclude_same_book=request.exclude_same_book
        )

        logger.info(f"Similar chunks found for {request.chunk_id}: {len(results)} results")
        return results

    except Exception as e:
        logger.error(f"Error finding similar chunks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to find similar chunks"
        )


@router.get("/history", response_model=SearchHistoryResponse)
async def get_search_history(
    limit: int = Query(default=50, ge=1, le=100, description="Maximum number of history items"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get user's search history.
    """
    try:
        search_service = SearchService(db)
        history_items = search_service.get_search_history(current_user.id, limit)

        return {
            "items": history_items,
            "total": len(history_items)
        }

    except Exception as e:
        logger.error(f"Error getting search history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get search history"
        )


@router.get("/analytics")
async def get_search_analytics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get search analytics (admin or user's own analytics).
    """
    try:
        search_service = SearchService(db)

        # For now, return general analytics
        # In production, you might want to restrict this to admins
        analytics = search_service.get_search_analytics()

        return analytics

    except Exception as e:
        logger.error(f"Error getting search analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get analytics"
        )
