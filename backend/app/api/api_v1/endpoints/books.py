"""
Book management endpoints for the medical preparation platform.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID
import os

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.core.dependencies import get_current_user, get_current_admin_user
from app.db.session import get_db
from app.db.models import User, Book
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger("api.books")
router = APIRouter()


@router.get("/")
async def get_books(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    Get list of books.
    
    Args:
        skip: Number of books to skip
        limit: Maximum number of books to return
        current_user: Authenticated user
        db: Database session
        
    Returns:
        List of books with metadata
    """
    try:
        # Get books from database
        books = db.query(Book).offset(skip).limit(limit).all()
        
        # Convert to response format
        book_list = []
        for book in books:
            book_data = {
                "id": str(book.id),
                "title": book.title,
                "authors": book.authors if book.authors else [],
                "isbn": book.isbn,
                "publication_year": book.publication_year,
                "publisher": getattr(book, 'publisher', None),
                "edition": getattr(book, 'edition', None),
                "status": book.processing_status.value if hasattr(book.processing_status, 'value') else str(book.processing_status),
                "file_path": book.file_path,
                "file_size": book.file_size,
                "upload_date": book.created_at.isoformat() if book.created_at else None,
                "processed": book.processing_status == "completed" if hasattr(book, 'processing_status') else False,
                "chunk_count": len(book.chunks) if hasattr(book, 'chunks') and book.chunks else 0,
            }
            book_list.append(book_data)
        
        logger.info(f"Retrieved {len(book_list)} books for user {current_user.id}")
        return book_list
        
    except Exception as e:
        logger.error(f"Error retrieving books: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve books"
        )


@router.get("/{book_id}")
async def get_book(
    book_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get a specific book by ID.
    
    Args:
        book_id: Book UUID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Book details
    """
    try:
        book = db.query(Book).filter(Book.id == book_id).first()
        
        if not book:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )
        
        book_data = {
            "id": str(book.id),
            "title": book.title,
            "authors": book.authors if book.authors else [],
            "isbn": book.isbn,
            "publication_year": book.publication_year,
            "publisher": getattr(book, 'publisher', None),
            "edition": getattr(book, 'edition', None),
            "status": book.processing_status.value if hasattr(book.processing_status, 'value') else str(book.processing_status),
            "file_path": book.file_path,
            "file_size": book.file_size,
            "upload_date": book.created_at.isoformat() if book.created_at else None,
            "processed": book.processing_status == "completed" if hasattr(book, 'processing_status') else False,
            "chunk_count": len(book.chunks) if hasattr(book, 'chunks') and book.chunks else 0,
            "description": getattr(book, 'description', None),
        }
        
        logger.info(f"Retrieved book {book_id} for user {current_user.id}")
        return book_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving book {book_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve book"
        )


@router.post("/upload")
async def upload_book(
    file: UploadFile = File(...),
    title: str = Form(...),
    authors: str = Form(...),  # Will be split into array
    isbn: Optional[str] = Form(None),
    publication_year: Optional[int] = Form(None),
    publisher: Optional[str] = Form(None),
    edition: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Upload a new book.
    
    Args:
        file: PDF file to upload
        title: Book title
        author: Book author
        isbn: Book ISBN (optional)
        publication_year: Publication year (optional)
        description: Book description (optional)
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Upload result with book ID
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF files are supported"
            )
        
        # Create upload directory if it doesn't exist
        upload_dir = settings.UPLOAD_DIR
        os.makedirs(upload_dir, exist_ok=True)
        
        # Generate unique filename
        import uuid
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"{file_id}{file_extension}"
        file_path = os.path.join(upload_dir, filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
            file_size = len(content)
        
        # Parse authors (split by comma if multiple)
        authors_list = [author.strip() for author in authors.split(',') if author.strip()]

        # Generate file hash
        import hashlib
        file_hash = hashlib.sha256(content).hexdigest()

        # Create book record
        book = Book(
            title=title,
            authors=authors_list,
            file_path=file_path,
            file_size=file_size,
            file_hash=file_hash,
            uploaded_by=current_user.id
        )

        # Add optional fields if provided
        if isbn:
            book.isbn = isbn
        if publication_year:
            book.publication_year = publication_year
        if publisher:
            book.publisher = publisher
        if edition:
            book.edition = edition
        
        db.add(book)
        db.commit()
        db.refresh(book)
        
        logger.info(f"Book uploaded successfully: {title} by {author} (ID: {book.id})")
        
        return {
            "id": str(book.id),
            "title": title,
            "authors": authors_list,
            "filename": file.filename,
            "file_size": file_size,
            "status": "uploaded",
            "message": "Book uploaded successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload book"
        )


@router.delete("/{book_id}")
async def delete_book(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Delete a book (admin only).
    
    Args:
        book_id: Book UUID
        current_user: Authenticated admin user
        db: Database session
        
    Returns:
        Deletion result
    """
    try:
        book = db.query(Book).filter(Book.id == book_id).first()
        
        if not book:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )
        
        # Delete file if it exists
        if book.file_path and os.path.exists(book.file_path):
            try:
                os.remove(book.file_path)
            except Exception as e:
                logger.warning(f"Could not delete file {book.file_path}: {e}")
        
        # Delete book record
        db.delete(book)
        db.commit()
        
        logger.info(f"Book deleted: {book.title} (ID: {book_id}) by admin {current_user.id}")
        
        return {
            "id": str(book_id),
            "message": "Book deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting book {book_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete book"
        )


@router.get("/{book_id}/stats")
async def get_book_stats(
    book_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get statistics for a specific book.
    
    Args:
        book_id: Book UUID
        current_user: Authenticated user
        db: Database session
        
    Returns:
        Book statistics
    """
    try:
        book = db.query(Book).filter(Book.id == book_id).first()
        
        if not book:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )
        
        # Get basic stats
        stats = {
            "id": str(book.id),
            "title": book.title,
            "authors": book.authors if book.authors else [],
            "file_size": book.file_size,
            "chunk_count": len(book.chunks) if hasattr(book, 'chunks') and book.chunks else 0,
            "processed": book.processing_status == "completed" if hasattr(book, 'processing_status') else False,
            "upload_date": book.created_at.isoformat() if book.created_at else None,
            "search_count": 0,  # TODO: Implement search tracking
            "popular_sections": [],  # TODO: Implement section popularity tracking
        }
        
        logger.info(f"Retrieved stats for book {book_id}")
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving book stats {book_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve book statistics"
        )
