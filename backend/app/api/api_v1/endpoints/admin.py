"""
Admin endpoints for book management and system administration.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from sqlalchemy.orm import Session

from app.core.dependencies import get_current_admin_user
from app.db.session import get_db
from app.db.models import User, Book, ProcessingStatus
from app.schemas.admin import (
    BookUploadRequest, BookUploadResponse, BookResponse, BookListResponse,
    BookUpdateRequest, ProcessingStatusResponse, SystemStatsResponse,
    BulkActionRequest, BulkActionResponse, SystemHealthResponse
)
from app.services.book_service import BookService
from app.services.indexing_service import IndexingService
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("api.admin")
router = APIRouter()


@router.post("/books/upload", response_model=BookUploadResponse)
async def upload_book(
    file: UploadFile = File(..., description="PDF file to upload"),
    title: str = Form(..., description="Book title"),
    authors: str = Form(..., description="Comma-separated list of authors"),
    isbn: Optional[str] = Form(default=None, description="ISBN"),
    publisher: Optional[str] = Form(default=None, description="Publisher"),
    publication_year: Optional[int] = Form(default=None, description="Publication year"),
    edition: Optional[str] = Form(default=None, description="Edition"),
    description: Optional[str] = Form(default=None, description="Book description"),
    tags: Optional[str] = Form(default=None, description="Comma-separated tags"),
    language: str = Form(default="en", description="Language code"),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> BookUploadResponse:
    """
    Upload a new medical book PDF (admin only).
    """
    try:
        # Validate file
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only PDF files are allowed"
            )

        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File size exceeds maximum allowed size ({settings.MAX_FILE_SIZE} bytes)"
            )

        # Parse authors and tags
        authors_list = [author.strip() for author in authors.split(',') if author.strip()]
        tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()] if tags else []

        if not authors_list:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one author is required"
            )

        # Save uploaded file
        book_service = BookService(db)
        file_content = await file.read()
        file_path = book_service.save_uploaded_file(file_content, file.filename)

        # Create book record
        book = book_service.create_book(
            title=title,
            authors=authors_list,
            file_path=file_path,
            uploaded_by=current_user.id,
            isbn=isbn,
            publisher=publisher,
            publication_year=publication_year,
            edition=edition,
            description=description,
            tags=tags_list,
            language=language
        )

        logger.info(f"Book uploaded by admin {current_user.email}: {title}")

        return BookUploadResponse(
            book_id=book.id,
            title=book.title,
            processing_status=book.processing_status,
            message="Book uploaded successfully. Processing will begin shortly."
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload book"
        )


@router.post("/books/{book_id}/process")
async def process_book(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Start processing a book (admin only).
    """
    try:
        indexing_service = IndexingService(db)
        success = await indexing_service.index_book(book_id)

        if success:
            logger.info(f"Book processing started by admin {current_user.email}: {book_id}")
            return {"message": "Book processing started successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to start book processing"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process book"
        )


@router.get("/books", response_model=BookListResponse)
async def get_books(
    page: int = Query(default=1, ge=1, description="Page number"),
    page_size: int = Query(default=20, ge=1, le=100, description="Items per page"),
    status_filter: Optional[ProcessingStatus] = Query(default=None, description="Filter by processing status"),
    search: Optional[str] = Query(default=None, description="Search in title, authors, or description"),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> BookListResponse:
    """
    Get list of books (admin only).
    """
    try:
        book_service = BookService(db)

        skip = (page - 1) * page_size
        books = book_service.get_books(
            skip=skip,
            limit=page_size,
            status=status_filter,
            search=search
        )

        # Get total count for pagination
        total_query = db.query(Book)
        if status_filter:
            total_query = total_query.filter(Book.processing_status == status_filter)
        if search:
            search_term = f"%{search}%"
            total_query = total_query.filter(
                Book.title.ilike(search_term) |
                Book.description.ilike(search_term)
            )
        total = total_query.count()

        return BookListResponse(
            books=books,
            total=total,
            page=page,
            page_size=page_size
        )

    except Exception as e:
        logger.error(f"Error getting books: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get books"
        )


@router.get("/books/{book_id}", response_model=BookResponse)
async def get_book(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> BookResponse:
    """
    Get book details (admin only).
    """
    try:
        book_service = BookService(db)
        book = book_service.get_book_by_id(book_id)

        if not book:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )

        return book

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get book"
        )


@router.put("/books/{book_id}", response_model=BookResponse)
async def update_book(
    book_id: UUID,
    book_update: BookUpdateRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> BookResponse:
    """
    Update book metadata (admin only).
    """
    try:
        book_service = BookService(db)

        updated_book = book_service.update_book(
            book_id=book_id,
            **book_update.dict(exclude_unset=True)
        )

        if not updated_book:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )

        logger.info(f"Book updated by admin {current_user.email}: {book_id}")
        return updated_book

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update book"
        )


@router.delete("/books/{book_id}")
async def delete_book(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Delete a book and all associated data (admin only).
    """
    try:
        book_service = BookService(db)
        indexing_service = IndexingService(db)

        # Delete from vector database first
        await indexing_service.delete_book_index(book_id)

        # Delete book record and file
        success = book_service.delete_book(book_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Book not found"
            )

        logger.info(f"Book deleted by admin {current_user.email}: {book_id}")
        return {"message": "Book deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete book"
        )


@router.get("/books/{book_id}/status", response_model=ProcessingStatusResponse)
async def get_processing_status(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> ProcessingStatusResponse:
    """
    Get book processing status (admin only).
    """
    try:
        indexing_service = IndexingService(db)
        status_info = await indexing_service.get_indexing_status(book_id)

        if "error" in status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=status_info["error"]
            )

        return ProcessingStatusResponse(**status_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting processing status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get processing status"
        )


@router.post("/books/{book_id}/reindex")
async def reindex_book(
    book_id: UUID,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Reindex a book (admin only).
    """
    try:
        indexing_service = IndexingService(db)
        success = await indexing_service.reindex_book(book_id)

        if success:
            logger.info(f"Book reindexing started by admin {current_user.email}: {book_id}")
            return {"message": "Book reindexing started successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to start book reindexing"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reindexing book: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reindex book"
        )


@router.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> SystemStatsResponse:
    """
    Get system statistics (admin only).
    """
    try:
        book_service = BookService(db)
        stats = book_service.get_book_statistics()

        # Add storage stats
        import os
        upload_dir_size = 0
        if os.path.exists(settings.UPLOAD_DIR):
            for dirpath, dirnames, filenames in os.walk(settings.UPLOAD_DIR):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    upload_dir_size += os.path.getsize(filepath)

        storage_stats = {
            "upload_directory_size_bytes": upload_dir_size,
            "upload_directory_size_mb": round(upload_dir_size / (1024 * 1024), 2)
        }

        # Get recent activity (simplified)
        recent_books = (
            db.query(Book)
            .order_by(Book.created_at.desc())
            .limit(10)
            .all()
        )

        recent_activity = [
            {
                "type": "book_upload",
                "title": book.title,
                "status": book.processing_status.value,
                "timestamp": book.created_at
            }
            for book in recent_books
        ]

        return SystemStatsResponse(
            total_books=stats["total_books"],
            total_chunks=stats["total_chunks"],
            total_pages=stats["total_pages"],
            processing_stats=stats["status_counts"],
            storage_stats=storage_stats,
            recent_activity=recent_activity
        )

    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system statistics"
        )


@router.post("/bulk-actions", response_model=BulkActionResponse)
async def perform_bulk_action(
    bulk_request: BulkActionRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> BulkActionResponse:
    """
    Perform bulk actions on books or users (admin only).
    """
    try:
        results = BulkActionResponse(
            action=bulk_request.action,
            total_items=len(bulk_request.item_ids),
            successful=0,
            failed=0,
            errors=[]
        )

        if bulk_request.action == "reindex":
            indexing_service = IndexingService(db)
            for book_id in bulk_request.item_ids:
                try:
                    success = await indexing_service.reindex_book(book_id)
                    if success:
                        results.successful += 1
                    else:
                        results.failed += 1
                        results.errors.append(f"Failed to reindex book {book_id}")
                except Exception as e:
                    results.failed += 1
                    results.errors.append(f"Error reindexing book {book_id}: {str(e)}")

        elif bulk_request.action == "delete" and bulk_request.confirm:
            book_service = BookService(db)
            indexing_service = IndexingService(db)
            for book_id in bulk_request.item_ids:
                try:
                    await indexing_service.delete_book_index(book_id)
                    success = book_service.delete_book(book_id)
                    if success:
                        results.successful += 1
                    else:
                        results.failed += 1
                        results.errors.append(f"Failed to delete book {book_id}")
                except Exception as e:
                    results.failed += 1
                    results.errors.append(f"Error deleting book {book_id}: {str(e)}")

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported action: {bulk_request.action}"
            )

        logger.info(f"Bulk action {bulk_request.action} performed by admin {current_user.email}: {results.successful}/{results.total_items} successful")
        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk action: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform bulk action"
        )


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
) -> SystemHealthResponse:
    """
    Get comprehensive system health status (admin only).
    """
    try:
        from datetime import datetime

        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow(),
            "services": {},
            "performance_metrics": {},
            "alerts": []
        }

        # Check indexing service
        indexing_service = IndexingService(db)
        indexing_health = await indexing_service.health_check()
        health_status["services"]["indexing"] = indexing_health

        if indexing_health["status"] != "healthy":
            health_status["status"] = "degraded"
            health_status["alerts"].append({
                "type": "warning",
                "message": "Indexing service is not healthy",
                "service": "indexing"
            })

        # Check database
        try:
            import time
            start_time = time.time()
            result = db.execute("SELECT 1").fetchone()
            response_time = int((time.time() - start_time) * 1000)

            if result:
                health_status["services"]["database"] = {
                    "status": "connected",
                    "response_time_ms": response_time
                }
            else:
                health_status["services"]["database"] = {
                    "status": "unhealthy",
                    "error": "No response from database"
                }
                health_status["status"] = "unhealthy"
        except Exception as e:
            health_status["services"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "unhealthy"
            health_status["alerts"].append({
                "type": "error",
                "message": "Database connection failed",
                "service": "database"
            })

        # Performance metrics
        book_count = db.query(Book).count()
        processing_count = db.query(Book).filter(
            Book.processing_status == ProcessingStatus.PROCESSING
        ).count()

        health_status["performance_metrics"] = {
            "total_books": book_count,
            "books_processing": processing_count,
            "processing_queue_length": processing_count
        }

        return SystemHealthResponse(**health_status)

    except Exception as e:
        logger.error(f"Error getting system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system health"
        )
