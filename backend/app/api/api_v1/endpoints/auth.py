"""
Authentication endpoints.
"""
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordRequestForm
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import create_access_token
from app.db.session import get_db
from app.schemas.auth import LoginResponse, AuthResponse
from app.schemas.user import User<PERSON><PERSON>, User
from app.services.user_service import UserService
from app.core.logging import get_logger

logger = get_logger("api.auth")
router = APIRouter()


@router.post("/signup", response_model=AuthResponse)
def signup(
    user_data: UserCreate,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new user account.
    """
    try:
        user_service = UserService(db)

        # Create user
        user = user_service.create_user(user_data)

        logger.info(f"New user registered: {user.email}")

        return {
            "success": True,
            "message": "User created successfully",
            "data": {
                "user_id": str(user.id),
                "email": user.email,
                "full_name": user.full_name
            }
        }

    except ValueError as e:
        logger.warning(f"Signup failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Signup error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/login", response_model=LoginResponse)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Login user and return access token.
    """
    try:
        user_service = UserService(db)

        # Authenticate user
        user = user_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            logger.warning(f"Login failed for: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Create access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=str(user.id),
            expires_delta=access_token_expires
        )

        logger.info(f"User logged in: {user.email}")

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "role": user.role,
                "is_verified": user.is_verified
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/logout", response_model=AuthResponse)
def logout() -> Dict[str, Any]:
    """
    Logout user (client-side token removal).
    """
    # In a stateless JWT system, logout is handled client-side
    # For enhanced security, you could implement token blacklisting
    return {
        "success": True,
        "message": "Logged out successfully"
    }
