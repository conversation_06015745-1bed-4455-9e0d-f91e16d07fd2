"""
Health check endpoints.
"""
import time
from typing import Dict, Any
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.core.config import settings

router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Detailed health check including database connectivity."""
    health_data = {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "checks": {}
    }
    
    # Database check
    try:
        db.execute("SELECT 1")
        health_data["checks"]["database"] = {"status": "healthy"}
    except Exception as e:
        health_data["status"] = "unhealthy"
        health_data["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Vector database check (Qdrant)
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{settings.QDRANT_URL}/health")
            if response.status_code == 200:
                health_data["checks"]["vector_db"] = {"status": "healthy"}
            else:
                health_data["status"] = "degraded"
                health_data["checks"]["vector_db"] = {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}"
                }
    except Exception as e:
        health_data["status"] = "degraded"
        health_data["checks"]["vector_db"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    return health_data


@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)) -> Dict[str, str]:
    """Readiness check for Kubernetes/Docker deployments."""
    try:
        # Check database connectivity
        db.execute("SELECT 1")
        return {"status": "ready"}
    except Exception:
        return {"status": "not ready"}


@router.get("/live")
async def liveness_check() -> Dict[str, str]:
    """Liveness check for Kubernetes/Docker deployments."""
    return {"status": "alive"}
