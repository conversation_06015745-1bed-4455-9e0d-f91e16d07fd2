"""
Enhanced Qdrant service for production-quality vector search with local embeddings.
Supports both real Qdrant and mock data for development.
"""
import uuid
import json
import os
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from app.core.config import settings
from app.core.logging import get_logger
from app.services.local_embedding_service import get_embedding_service

logger = get_logger("services.enhanced_qdrant")

# Try to import Qdrant, fall back to mock if not available
try:
    from qdrant_client import QdrantClient
    from qdrant_client.http import models
    from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
    QDRANT_AVAILABLE = True
except ImportError:
    logger.warning("Qdrant client not available, using mock service")
    QDRANT_AVAILABLE = False


class EnhancedQdrantService:
    """
    Production-quality Qdrant service with local embeddings and mock fallback.
    
    Features:
    - Local embedding generation
    - Real Qdrant integration when available
    - Mock data fallback for development
    - Structured search results with metadata
    - Section-based grouping for medical content
    """
    
    def __init__(self):
        self.collection_name = getattr(settings, 'QDRANT_COLLECTION_NAME', 'medical_chunks')
        self.embedding_service = get_embedding_service()
        self.use_mock = False
        self.mock_data_file = os.path.join(settings.UPLOAD_DIR, 'enhanced_mock_vectors.json')
        
        # Initialize Qdrant or mock service
        self._initialize_service()
        
        # Load or create mock data
        self.mock_data = self._load_mock_data()
    
    def _initialize_service(self):
        """Initialize Qdrant service or fall back to mock."""
        if QDRANT_AVAILABLE:
            try:
                self.client = QdrantClient(
                    url=settings.QDRANT_URL,
                    api_key=getattr(settings, 'QDRANT_API_KEY', None)
                )
                # Test connection
                self.client.get_collections()
                self._ensure_collection_exists()
                self.use_mock = False
                logger.info("Connected to Qdrant vector database")
            except Exception as e:
                logger.warning(f"Could not connect to Qdrant: {e}. Using mock service.")
                self.use_mock = True
        else:
            logger.info("Qdrant not available, using mock service")
            self.use_mock = True
    
    def _ensure_collection_exists(self):
        """Ensure the collection exists in Qdrant."""
        if self.use_mock:
            return
        
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                logger.info(f"Creating collection: {self.collection_name}")
                
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.embedding_service.get_embedding_dimension(),
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Collection created: {self.collection_name}")
            else:
                logger.info(f"Collection exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
            self.use_mock = True
    
    def _load_mock_data(self) -> Dict[str, Any]:
        """Load mock vector data from file."""
        if os.path.exists(self.mock_data_file):
            try:
                with open(self.mock_data_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load mock data: {e}")
        
        # Create realistic mock medical data
        return self._create_mock_medical_data()
    
    def _create_mock_medical_data(self) -> Dict[str, Any]:
        """Create realistic mock medical data for testing."""
        mock_data = {
            "vectors": {},
            "metadata": {
                "total_chunks": 0,
                "books": ["Grant's Dissector", "Harrison's Principles", "Robbins Pathology"],
                "section_types": ["Diagnosis", "Treatment", "Prognosis", "Anatomy", "Pathophysiology"]
            }
        }
        
        # Sample medical content by section type
        sample_content = {
            "Diagnosis": [
                {
                    "text": "Kawasaki disease is diagnosed based on clinical criteria including fever lasting 5 or more days and at least 4 of the following: bilateral conjunctival injection, oral mucosal changes, cervical lymphadenopathy, extremity changes, and polymorphous rash.",
                    "book_title": "Harrison's Principles of Internal Medicine",
                    "chapter": "Vasculitis Syndromes",
                    "page_number": 2187,
                    "author": "Harrison et al."
                },
                {
                    "text": "Diabetes mellitus diagnosis requires fasting plasma glucose ≥126 mg/dL, random plasma glucose ≥200 mg/dL with symptoms, or HbA1c ≥6.5%. Oral glucose tolerance test showing 2-hour glucose ≥200 mg/dL is also diagnostic.",
                    "book_title": "Harrison's Principles of Internal Medicine", 
                    "chapter": "Diabetes Mellitus",
                    "page_number": 2399,
                    "author": "Harrison et al."
                }
            ],
            "Treatment": [
                {
                    "text": "Kawasaki disease treatment involves high-dose intravenous immunoglobulin (IVIG) 2 g/kg as a single infusion, plus aspirin 80-100 mg/kg/day divided into 4 doses. Treatment should be initiated within 10 days of fever onset.",
                    "book_title": "Harrison's Principles of Internal Medicine",
                    "chapter": "Vasculitis Syndromes", 
                    "page_number": 2188,
                    "author": "Harrison et al."
                },
                {
                    "text": "Type 2 diabetes treatment begins with lifestyle modifications including diet and exercise. Metformin is typically first-line pharmacotherapy unless contraindicated. Additional agents include sulfonylureas, DPP-4 inhibitors, GLP-1 agonists, and insulin.",
                    "book_title": "Harrison's Principles of Internal Medicine",
                    "chapter": "Diabetes Mellitus",
                    "page_number": 2408,
                    "author": "Harrison et al."
                }
            ],
            "Prognosis": [
                {
                    "text": "Kawasaki disease prognosis is generally excellent with appropriate treatment. Coronary artery aneurysms develop in 15-25% of untreated patients but only 3-5% of those receiving IVIG within 10 days of onset.",
                    "book_title": "Harrison's Principles of Internal Medicine",
                    "chapter": "Vasculitis Syndromes",
                    "page_number": 2189,
                    "author": "Harrison et al."
                }
            ],
            "Anatomy": [
                {
                    "text": "The heart is a four-chambered muscular organ located in the mediastinum. The right ventricle pumps deoxygenated blood to the lungs via the pulmonary artery, while the left ventricle pumps oxygenated blood to the systemic circulation via the aorta.",
                    "book_title": "Grant's Dissector",
                    "chapter": "Thorax",
                    "page_number": 45,
                    "author": "Grant et al."
                }
            ],
            "Pathophysiology": [
                {
                    "text": "Kawasaki disease involves acute necrotizing arteritis affecting medium-sized arteries, particularly coronary arteries. The inflammatory process leads to endothelial dysfunction, smooth muscle cell proliferation, and potential aneurysm formation.",
                    "book_title": "Robbins Basic Pathology",
                    "chapter": "Blood Vessels",
                    "page_number": 512,
                    "author": "Robbins et al."
                }
            ]
        }
        
        # Generate embeddings and store mock data
        vector_id = 0
        for section_type, contents in sample_content.items():
            for content in contents:
                vector_id += 1
                vector_id_str = str(vector_id)
                
                # Generate embedding for the text
                embedding = self.embedding_service.encode_single(content["text"])
                
                mock_data["vectors"][vector_id_str] = {
                    "id": vector_id_str,
                    "vector": embedding.tolist(),
                    "text": content["text"],
                    "metadata": {
                        "section_type": section_type,
                        "book_title": content["book_title"],
                        "chapter": content["chapter"],
                        "page_number": content["page_number"],
                        "author": content["author"],
                        "word_count": len(content["text"].split()),
                        "char_count": len(content["text"])
                    }
                }
        
        mock_data["metadata"]["total_chunks"] = len(mock_data["vectors"])
        
        # Save mock data
        self._save_mock_data(mock_data)
        return mock_data
    
    def _save_mock_data(self, data: Dict[str, Any]):
        """Save mock data to file."""
        try:
            os.makedirs(os.path.dirname(self.mock_data_file), exist_ok=True)
            with open(self.mock_data_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save mock data: {e}")
    
    def search_topic(
        self,
        query: str,
        limit: int = 20,
        score_threshold: float = 0.7
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search for topics and return results grouped by section type.
        
        Args:
            query: Search query text
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold
            
        Returns:
            Dictionary with section types as keys and lists of results as values
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_service.encode_single(query)
            
            if self.use_mock:
                return self._search_mock_data(query_embedding, query, limit, score_threshold)
            else:
                return self._search_qdrant(query_embedding, query, limit, score_threshold)
                
        except Exception as e:
            logger.error(f"Error in topic search: {e}")
            return {"error": [{"text": f"Search failed: {str(e)}", "metadata": {}}]}
    
    def _search_mock_data(
        self,
        query_embedding: np.ndarray,
        query: str,
        limit: int,
        score_threshold: float
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Search mock data and return grouped results."""
        results = []
        
        # Calculate similarities with all vectors
        for vector_id, vector_data in self.mock_data["vectors"].items():
            vector_embedding = np.array(vector_data["vector"], dtype=np.float32)
            
            # Calculate cosine similarity
            similarity = self.embedding_service.similarity(query_embedding, vector_embedding)
            
            if similarity >= score_threshold:
                results.append({
                    "score": similarity,
                    "text": vector_data["text"],
                    "metadata": vector_data["metadata"]
                })
        
        # Sort by similarity score
        results.sort(key=lambda x: x["score"], reverse=True)
        results = results[:limit]
        
        # Group by section type
        grouped_results = {}
        for result in results:
            section_type = result["metadata"].get("section_type", "Other")
            if section_type not in grouped_results:
                grouped_results[section_type] = []
            grouped_results[section_type].append(result)
        
        logger.info(f"Mock search for '{query}' returned {len(results)} results in {len(grouped_results)} sections")
        return grouped_results
    
    def _search_qdrant(
        self,
        query_embedding: np.ndarray,
        query: str,
        limit: int,
        score_threshold: float
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Search real Qdrant database and return grouped results."""
        try:
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Group results by section type
            grouped_results = {}
            for result in search_results:
                section_type = result.payload.get("section_type", "Other")
                if section_type not in grouped_results:
                    grouped_results[section_type] = []
                
                grouped_results[section_type].append({
                    "score": result.score,
                    "text": result.payload.get("text", ""),
                    "metadata": {
                        "section_type": section_type,
                        "book_title": result.payload.get("book_title", ""),
                        "chapter": result.payload.get("chapter", ""),
                        "page_number": result.payload.get("page_number", 0),
                        "author": result.payload.get("author", ""),
                        "word_count": result.payload.get("word_count", 0),
                        "char_count": result.payload.get("char_count", 0)
                    }
                })
            
            logger.info(f"Qdrant search for '{query}' returned {len(search_results)} results in {len(grouped_results)} sections")
            return grouped_results
            
        except Exception as e:
            logger.error(f"Error searching Qdrant: {e}")
            # Fall back to mock data
            return self._search_mock_data(query_embedding, query, limit, score_threshold)
    
    def add_document_chunks(self, chunks: List[Dict[str, Any]]) -> bool:
        """
        Add document chunks to the vector database.
        
        Args:
            chunks: List of document chunks with text and metadata
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if self.use_mock:
                return self._add_chunks_to_mock(chunks)
            else:
                return self._add_chunks_to_qdrant(chunks)
        except Exception as e:
            logger.error(f"Error adding document chunks: {e}")
            return False
    
    def _add_chunks_to_mock(self, chunks: List[Dict[str, Any]]) -> bool:
        """Add chunks to mock data."""
        try:
            for chunk in chunks:
                vector_id = str(len(self.mock_data["vectors"]) + 1)
                embedding = self.embedding_service.encode_single(chunk["text"])
                
                self.mock_data["vectors"][vector_id] = {
                    "id": vector_id,
                    "vector": embedding.tolist(),
                    "text": chunk["text"],
                    "metadata": chunk.get("metadata", {})
                }
            
            self.mock_data["metadata"]["total_chunks"] = len(self.mock_data["vectors"])
            self._save_mock_data(self.mock_data)
            return True
        except Exception as e:
            logger.error(f"Error adding chunks to mock: {e}")
            return False
    
    def _add_chunks_to_qdrant(self, chunks: List[Dict[str, Any]]) -> bool:
        """Add chunks to real Qdrant database."""
        try:
            points = []
            for chunk in chunks:
                embedding = self.embedding_service.encode_single(chunk["text"])
                
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding.tolist(),
                    payload={
                        "text": chunk["text"],
                        **chunk.get("metadata", {})
                    }
                )
                points.append(point)
            
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            return True
        except Exception as e:
            logger.error(f"Error adding chunks to Qdrant: {e}")
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get information about the service."""
        return {
            "service_type": "mock" if self.use_mock else "qdrant",
            "collection_name": self.collection_name,
            "embedding_model": self.embedding_service.get_model_info(),
            "total_vectors": len(self.mock_data["vectors"]) if self.use_mock else "unknown"
        }


# Global instance for reuse across requests
_qdrant_service: Optional[EnhancedQdrantService] = None


def get_qdrant_service() -> EnhancedQdrantService:
    """Get or create the global Qdrant service instance."""
    global _qdrant_service
    
    if _qdrant_service is None:
        _qdrant_service = EnhancedQdrantService()
    
    return _qdrant_service
