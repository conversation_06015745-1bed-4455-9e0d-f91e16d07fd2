"""
Embedding service for generating text embeddings using various providers.
"""
import asyncio
from typing import List, Dict, Any, Optional
import openai
import cohere
import httpx

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("services.embedding")


class EmbeddingService:
    """Service for generating text embeddings."""
    
    def __init__(self):
        self.openai_client = None
        self.cohere_client = None
        
        # Initialize clients based on available API keys
        if settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
            self.openai_client = openai
        
        if settings.COHERE_API_KEY:
            self.cohere_client = cohere.Client(settings.COHERE_API_KEY)
    
    async def generate_embedding(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            model: Optional model override
            
        Returns:
            Embedding vector
        """
        model = model or settings.EMBEDDING_MODEL
        
        try:
            if "text-embedding" in model and self.openai_client:
                return await self._generate_openai_embedding(text, model)
            elif "embed" in model and self.cohere_client:
                return await self._generate_cohere_embedding(text, model)
            else:
                raise ValueError(f"Unsupported embedding model: {model}")
                
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise
    
    async def generate_embeddings_batch(
        self, 
        texts: List[str], 
        model: Optional[str] = None,
        batch_size: int = 100
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embedding vectors
        """
        model = model or settings.EMBEDDING_MODEL
        
        try:
            embeddings = []
            
            # Process in batches
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                if "text-embedding" in model and self.openai_client:
                    batch_embeddings = await self._generate_openai_embeddings_batch(batch_texts, model)
                elif "embed" in model and self.cohere_client:
                    batch_embeddings = await self._generate_cohere_embeddings_batch(batch_texts, model)
                else:
                    raise ValueError(f"Unsupported embedding model: {model}")
                
                embeddings.extend(batch_embeddings)
                
                # Add small delay between batches to respect rate limits
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            logger.info(f"Generated {len(embeddings)} embeddings")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {str(e)}")
            raise
    
    async def _generate_openai_embedding(self, text: str, model: str) -> List[float]:
        """Generate embedding using OpenAI API."""
        try:
            response = await self.openai_client.Embedding.acreate(
                input=text,
                model=model
            )
            return response['data'][0]['embedding']
            
        except Exception as e:
            logger.error(f"OpenAI embedding error: {str(e)}")
            raise
    
    async def _generate_openai_embeddings_batch(self, texts: List[str], model: str) -> List[List[float]]:
        """Generate embeddings using OpenAI API in batch."""
        try:
            response = await self.openai_client.Embedding.acreate(
                input=texts,
                model=model
            )
            return [item['embedding'] for item in response['data']]
            
        except Exception as e:
            logger.error(f"OpenAI batch embedding error: {str(e)}")
            raise
    
    async def _generate_cohere_embedding(self, text: str, model: str) -> List[float]:
        """Generate embedding using Cohere API."""
        try:
            response = self.cohere_client.embed(
                texts=[text],
                model=model,
                input_type="search_document"
            )
            return response.embeddings[0]
            
        except Exception as e:
            logger.error(f"Cohere embedding error: {str(e)}")
            raise
    
    async def _generate_cohere_embeddings_batch(self, texts: List[str], model: str) -> List[List[float]]:
        """Generate embeddings using Cohere API in batch."""
        try:
            response = self.cohere_client.embed(
                texts=texts,
                model=model,
                input_type="search_document"
            )
            return response.embeddings
            
        except Exception as e:
            logger.error(f"Cohere batch embedding error: {str(e)}")
            raise
    
    def get_embedding_dimension(self, model: Optional[str] = None) -> int:
        """Get the dimension of embeddings for a given model."""
        model = model or settings.EMBEDDING_MODEL
        
        # Model dimension mappings
        model_dimensions = {
            "text-embedding-ada-002": 1536,
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "embed-english-v3.0": 1024,
            "embed-multilingual-v3.0": 1024,
            "embed-english-light-v3.0": 384,
            "embed-multilingual-light-v3.0": 384,
        }
        
        return model_dimensions.get(model, settings.EMBEDDING_DIMENSION)
    
    async def embed_query(self, query: str, model: Optional[str] = None) -> List[float]:
        """
        Generate embedding for a search query.
        
        Args:
            query: Search query text
            model: Optional model override
            
        Returns:
            Query embedding vector
        """
        model = model or settings.EMBEDDING_MODEL
        
        try:
            if "text-embedding" in model and self.openai_client:
                return await self._generate_openai_embedding(query, model)
            elif "embed" in model and self.cohere_client:
                # Use search_query input type for Cohere
                response = self.cohere_client.embed(
                    texts=[query],
                    model=model,
                    input_type="search_query"
                )
                return response.embeddings[0]
            else:
                raise ValueError(f"Unsupported embedding model: {model}")
                
        except Exception as e:
            logger.error(f"Error generating query embedding: {str(e)}")
            raise
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Validate available API keys."""
        validation_results = {
            "openai": False,
            "cohere": False
        }
        
        # Test OpenAI API key
        if settings.OPENAI_API_KEY:
            try:
                # Simple test call
                openai.api_key = settings.OPENAI_API_KEY
                openai.Model.list()
                validation_results["openai"] = True
            except Exception as e:
                logger.warning(f"OpenAI API key validation failed: {str(e)}")
        
        # Test Cohere API key
        if settings.COHERE_API_KEY:
            try:
                client = cohere.Client(settings.COHERE_API_KEY)
                # Simple test call
                client.embed(texts=["test"], model="embed-english-light-v3.0")
                validation_results["cohere"] = True
            except Exception as e:
                logger.warning(f"Cohere API key validation failed: {str(e)}")
        
        return validation_results
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check for embedding services."""
        health_status = {
            "status": "healthy",
            "providers": {},
            "default_model": settings.EMBEDDING_MODEL,
            "embedding_dimension": settings.EMBEDDING_DIMENSION
        }
        
        # Check OpenAI
        if self.openai_client:
            try:
                await self._generate_openai_embedding("test", "text-embedding-ada-002")
                health_status["providers"]["openai"] = {"status": "healthy"}
            except Exception as e:
                health_status["providers"]["openai"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health_status["status"] = "degraded"
        
        # Check Cohere
        if self.cohere_client:
            try:
                await self._generate_cohere_embedding("test", "embed-english-light-v3.0")
                health_status["providers"]["cohere"] = {"status": "healthy"}
            except Exception as e:
                health_status["providers"]["cohere"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                health_status["status"] = "degraded"
        
        if not health_status["providers"]:
            health_status["status"] = "unhealthy"
            health_status["error"] = "No embedding providers available"
        
        return health_status
