"""
User service for managing user operations.
"""
from typing import Optional, List
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.db.models import User, UserRole
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password
from app.core.logging import get_logger

logger = get_logger("services.user")


class UserService:
    """Service class for user operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_users(
        self, 
        skip: int = 0, 
        limit: int = 100,
        is_active: Optional[bool] = None,
        role: Optional[UserRole] = None
    ) -> List[User]:
        """Get list of users with optional filters."""
        query = self.db.query(User)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        if role is not None:
            query = query.filter(User.role == role)
        
        return query.offset(skip).limit(limit).all()
    
    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        try:
            # Check if user already exists
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user:
                raise ValueError("User with this email already exists")
            
            # Create user object
            db_user = User(
                email=user_data.email,
                hashed_password=get_password_hash(user_data.password),
                full_name=user_data.full_name,
                institution=user_data.institution,
                specialization=user_data.specialization,
                year_of_study=user_data.year_of_study,
                role=UserRole.USER,  # Default role
                is_active=True,
                is_verified=False,
                preferences={}
            )
            
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            
            logger.info(f"Created new user: {user_data.email}")
            return db_user
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Database integrity error creating user: {str(e)}")
            raise ValueError("User with this email already exists")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating user: {str(e)}")
            raise
    
    def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """Update user information."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return None
            
            # Update fields
            update_data = user_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)
            
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"Updated user: {user.email}")
            return user
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating user {user_id}: {str(e)}")
            raise
    
    def change_password(self, user_id: UUID, current_password: str, new_password: str) -> bool:
        """Change user password."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            # Verify current password
            if not verify_password(current_password, user.hashed_password):
                logger.warning(f"Invalid current password for user: {user.email}")
                return False
            
            # Update password
            user.hashed_password = get_password_hash(new_password)
            self.db.commit()
            
            logger.info(f"Password changed for user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error changing password for user {user_id}: {str(e)}")
            raise
    
    def deactivate_user(self, user_id: UUID) -> bool:
        """Deactivate a user."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = False
            self.db.commit()
            
            logger.info(f"Deactivated user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deactivating user {user_id}: {str(e)}")
            raise
    
    def activate_user(self, user_id: UUID) -> bool:
        """Activate a user."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = True
            self.db.commit()
            
            logger.info(f"Activated user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error activating user {user_id}: {str(e)}")
            raise
    
    def verify_user(self, user_id: UUID) -> bool:
        """Verify a user."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_verified = True
            self.db.commit()
            
            logger.info(f"Verified user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error verifying user {user_id}: {str(e)}")
            raise
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        user = self.get_user_by_email(email)
        if not user:
            logger.warning(f"Authentication failed - user not found: {email}")
            return None
        
        if not verify_password(password, user.hashed_password):
            logger.warning(f"Authentication failed - invalid password: {email}")
            return None
        
        if not user.is_active:
            logger.warning(f"Authentication failed - user inactive: {email}")
            return None
        
        logger.info(f"User authenticated successfully: {email}")
        return user
    
    def update_user_preferences(self, user_id: UUID, preferences: dict) -> bool:
        """Update user preferences."""
        try:
            user = self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.preferences = preferences
            self.db.commit()
            
            logger.info(f"Updated preferences for user: {user.email}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating preferences for user {user_id}: {str(e)}")
            raise
