"""
Indexing service for processing documents and creating vector embeddings.
"""
import asyncio
from typing import List, Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from app.db.models import Book, Chunk, ProcessingStatus
from app.services.pdf_service import PDFService
from app.services.embedding_service import EmbeddingService
from app.services.vector_service import VectorService
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("services.indexing")


class IndexingService:
    """Service for indexing documents and creating searchable embeddings."""
    
    def __init__(self, db: Session):
        self.db = db
        self.pdf_service = PDFService(db)
        self.embedding_service = EmbeddingService()
        self.vector_service = VectorService()
    
    async def index_book(self, book_id: UUID) -> bool:
        """
        Complete indexing pipeline for a book.
        
        Args:
            book_id: Book ID to index
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get book from database
            book = self.db.query(Book).filter(Book.id == book_id).first()
            if not book:
                logger.error(f"Book not found: {book_id}")
                return False
            
            logger.info(f"Starting indexing for book: {book.title}")
            
            # Update status to processing
            book.processing_status = ProcessingStatus.PROCESSING
            self.db.commit()
            
            # Step 1: Process PDF and create chunks
            success = self.pdf_service.process_pdf(str(book_id))
            if not success:
                logger.error(f"PDF processing failed for book: {book_id}")
                return False
            
            # Step 2: Generate embeddings for chunks
            await self._generate_embeddings_for_book(book_id)
            
            # Step 3: Update book status
            book.processing_status = ProcessingStatus.COMPLETED
            self.db.commit()
            
            logger.info(f"Successfully indexed book: {book.title}")
            return True
            
        except Exception as e:
            # Update status to failed
            book = self.db.query(Book).filter(Book.id == book_id).first()
            if book:
                book.processing_status = ProcessingStatus.FAILED
                book.processing_error = str(e)
                self.db.commit()
            
            logger.error(f"Error indexing book {book_id}: {str(e)}")
            return False
    
    async def _generate_embeddings_for_book(self, book_id: UUID) -> bool:
        """Generate embeddings for all chunks of a book."""
        try:
            # Get all chunks for the book
            chunks = self.db.query(Chunk).filter(Chunk.book_id == book_id).all()
            
            if not chunks:
                logger.warning(f"No chunks found for book: {book_id}")
                return False
            
            logger.info(f"Generating embeddings for {len(chunks)} chunks")
            
            # Prepare texts for embedding
            texts = [chunk.content for chunk in chunks]
            
            # Generate embeddings in batches
            embeddings = await self.embedding_service.generate_embeddings_batch(texts)
            
            # Prepare vector data for storage
            vectors_data = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                vector_data = {
                    "vector": embedding,
                    "chunk_id": str(chunk.id),
                    "book_id": str(chunk.book_id),
                    "content": chunk.content,
                    "page_number": chunk.page_number,
                    "chapter_title": chunk.chapter_title,
                    "topic_category": chunk.topic_category.value if chunk.topic_category else None,
                    "medical_topics": chunk.medical_topics,
                    "word_count": chunk.word_count,
                    "char_count": chunk.char_count,
                    "embedding_model": settings.EMBEDDING_MODEL
                }
                vectors_data.append(vector_data)
            
            # Store vectors in Qdrant
            vector_ids = self.vector_service.add_vectors(vectors_data)
            
            # Update chunks with vector IDs
            for chunk, vector_id in zip(chunks, vector_ids):
                chunk.vector_id = vector_id
                chunk.embedding_model = settings.EMBEDDING_MODEL
            
            self.db.commit()
            
            logger.info(f"Successfully generated and stored {len(embeddings)} embeddings")
            return True
            
        except Exception as e:
            logger.error(f"Error generating embeddings for book {book_id}: {str(e)}")
            raise
    
    async def reindex_book(self, book_id: UUID) -> bool:
        """
        Reindex a book (delete existing vectors and recreate).
        
        Args:
            book_id: Book ID to reindex
            
        Returns:
            True if successful
        """
        try:
            logger.info(f"Reindexing book: {book_id}")
            
            # Delete existing vectors
            self.vector_service.delete_vectors_by_book(str(book_id))
            
            # Clear vector IDs from chunks
            chunks = self.db.query(Chunk).filter(Chunk.book_id == book_id).all()
            for chunk in chunks:
                chunk.vector_id = None
                chunk.embedding_model = None
            self.db.commit()
            
            # Reindex
            return await self.index_book(book_id)
            
        except Exception as e:
            logger.error(f"Error reindexing book {book_id}: {str(e)}")
            return False
    
    async def delete_book_index(self, book_id: UUID) -> bool:
        """
        Delete all index data for a book.
        
        Args:
            book_id: Book ID to delete index for
            
        Returns:
            True if successful
        """
        try:
            logger.info(f"Deleting index for book: {book_id}")
            
            # Delete vectors from Qdrant
            self.vector_service.delete_vectors_by_book(str(book_id))
            
            # Delete chunks from database
            self.db.query(Chunk).filter(Chunk.book_id == book_id).delete()
            self.db.commit()
            
            logger.info(f"Successfully deleted index for book: {book_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting index for book {book_id}: {str(e)}")
            return False
    
    async def get_indexing_status(self, book_id: UUID) -> Dict[str, Any]:
        """Get indexing status for a book."""
        try:
            book = self.db.query(Book).filter(Book.id == book_id).first()
            if not book:
                return {"error": "Book not found"}
            
            chunks_count = self.db.query(Chunk).filter(Chunk.book_id == book_id).count()
            indexed_chunks = self.db.query(Chunk).filter(
                Chunk.book_id == book_id,
                Chunk.vector_id.isnot(None)
            ).count()
            
            return {
                "book_id": str(book_id),
                "title": book.title,
                "processing_status": book.processing_status.value,
                "total_pages": book.total_pages,
                "total_chunks": chunks_count,
                "indexed_chunks": indexed_chunks,
                "indexing_progress": (indexed_chunks / chunks_count * 100) if chunks_count > 0 else 0,
                "processing_error": book.processing_error
            }
            
        except Exception as e:
            logger.error(f"Error getting indexing status for book {book_id}: {str(e)}")
            return {"error": str(e)}
    
    async def batch_index_books(self, book_ids: List[UUID]) -> Dict[str, Any]:
        """
        Index multiple books in batch.
        
        Args:
            book_ids: List of book IDs to index
            
        Returns:
            Summary of indexing results
        """
        results = {
            "total": len(book_ids),
            "successful": 0,
            "failed": 0,
            "errors": []
        }
        
        for book_id in book_ids:
            try:
                success = await self.index_book(book_id)
                if success:
                    results["successful"] += 1
                else:
                    results["failed"] += 1
                    results["errors"].append(f"Failed to index book: {book_id}")
                    
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Error indexing book {book_id}: {str(e)}")
        
        logger.info(f"Batch indexing completed: {results}")
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check for indexing services."""
        health_status = {
            "status": "healthy",
            "services": {}
        }
        
        # Check embedding service
        try:
            embedding_health = await self.embedding_service.health_check()
            health_status["services"]["embedding"] = embedding_health
            if embedding_health["status"] != "healthy":
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["services"]["embedding"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # Check vector service
        try:
            vector_info = self.vector_service.get_collection_info()
            health_status["services"]["vector"] = {
                "status": "healthy",
                "collection_info": vector_info
            }
        except Exception as e:
            health_status["services"]["vector"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        return health_status
