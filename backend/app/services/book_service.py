"""
Book service for managing medical book metadata and operations.
"""
import os
import shutil
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.db.models import Book, User, ProcessingStatus
from app.core.config import settings
from app.core.logging import get_logger
from app.services.pdf_service import PDFService

logger = get_logger("services.book")


class BookService:
    """Service class for book operations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.pdf_service = PDFService(db)
    
    def get_book_by_id(self, book_id: UUID) -> Optional[Book]:
        """Get book by ID."""
        return self.db.query(Book).filter(Book.id == book_id).first()
    
    def get_books(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ProcessingStatus] = None,
        search: Optional[str] = None
    ) -> List[Book]:
        """Get list of books with optional filters."""
        query = self.db.query(Book)
        
        if status:
            query = query.filter(Book.processing_status == status)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                Book.title.ilike(search_term) |
                Book.authors.any(search_term) |
                Book.description.ilike(search_term)
            )
        
        return query.offset(skip).limit(limit).all()
    
    def create_book(
        self,
        title: str,
        authors: List[str],
        file_path: str,
        uploaded_by: UUID,
        isbn: Optional[str] = None,
        publisher: Optional[str] = None,
        publication_year: Optional[int] = None,
        edition: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        language: str = "en"
    ) -> Book:
        """Create a new book record."""
        try:
            # Validate PDF file
            is_valid, error_msg = self.pdf_service.validate_pdf_file(file_path)
            if not is_valid:
                raise ValueError(f"Invalid PDF file: {error_msg}")
            
            # Generate file hash
            file_hash = self.pdf_service.get_file_hash(file_path)
            
            # Check for duplicate files
            existing_book = self.db.query(Book).filter(Book.file_hash == file_hash).first()
            if existing_book:
                raise ValueError("A book with this file already exists")
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Create book object
            book = Book(
                title=title,
                authors=authors,
                isbn=isbn,
                publisher=publisher,
                publication_year=publication_year,
                edition=edition,
                file_path=file_path,
                file_size=file_size,
                file_hash=file_hash,
                processing_status=ProcessingStatus.PENDING,
                description=description,
                tags=tags or [],
                language=language,
                uploaded_by=uploaded_by
            )
            
            self.db.add(book)
            self.db.commit()
            self.db.refresh(book)
            
            logger.info(f"Created new book: {title}")
            return book
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Database integrity error creating book: {str(e)}")
            raise ValueError("Book with this ISBN or file already exists")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating book: {str(e)}")
            raise
    
    def update_book(
        self,
        book_id: UUID,
        title: Optional[str] = None,
        authors: Optional[List[str]] = None,
        isbn: Optional[str] = None,
        publisher: Optional[str] = None,
        publication_year: Optional[int] = None,
        edition: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        language: Optional[str] = None
    ) -> Optional[Book]:
        """Update book metadata."""
        try:
            book = self.get_book_by_id(book_id)
            if not book:
                return None
            
            # Update fields
            if title is not None:
                book.title = title
            if authors is not None:
                book.authors = authors
            if isbn is not None:
                book.isbn = isbn
            if publisher is not None:
                book.publisher = publisher
            if publication_year is not None:
                book.publication_year = publication_year
            if edition is not None:
                book.edition = edition
            if description is not None:
                book.description = description
            if tags is not None:
                book.tags = tags
            if language is not None:
                book.language = language
            
            self.db.commit()
            self.db.refresh(book)
            
            logger.info(f"Updated book: {book.title}")
            return book
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating book {book_id}: {str(e)}")
            raise
    
    def delete_book(self, book_id: UUID) -> bool:
        """Delete a book and its associated file."""
        try:
            book = self.get_book_by_id(book_id)
            if not book:
                return False
            
            # Delete file if it exists
            if os.path.exists(book.file_path):
                os.remove(book.file_path)
                logger.info(f"Deleted file: {book.file_path}")
            
            # Delete book record (cascades to chunks)
            self.db.delete(book)
            self.db.commit()
            
            logger.info(f"Deleted book: {book.title}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting book {book_id}: {str(e)}")
            raise
    
    def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """Save uploaded file to storage."""
        try:
            # Ensure upload directory exists
            os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
            
            # Generate unique filename
            import uuid
            file_id = str(uuid.uuid4())
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{file_id}{file_extension}"
            file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            logger.info(f"Saved uploaded file: {filename} -> {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error saving uploaded file: {str(e)}")
            raise
    
    def process_book(self, book_id: UUID) -> bool:
        """Start processing a book."""
        try:
            book = self.get_book_by_id(book_id)
            if not book:
                logger.error(f"Book not found for processing: {book_id}")
                return False
            
            if book.processing_status != ProcessingStatus.PENDING:
                logger.warning(f"Book {book_id} is not in pending status")
                return False
            
            # Process the PDF
            success = self.pdf_service.process_pdf(str(book_id))
            
            if success:
                logger.info(f"Successfully processed book: {book.title}")
            else:
                logger.error(f"Failed to process book: {book.title}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error processing book {book_id}: {str(e)}")
            return False
    
    def get_book_statistics(self) -> Dict[str, Any]:
        """Get book statistics."""
        try:
            total_books = self.db.query(Book).count()
            
            status_counts = {}
            for status in ProcessingStatus:
                count = self.db.query(Book).filter(Book.processing_status == status).count()
                status_counts[status.value] = count
            
            total_chunks = self.db.query(Book).with_entities(
                self.db.func.sum(Book.total_chunks)
            ).scalar() or 0
            
            total_pages = self.db.query(Book).with_entities(
                self.db.func.sum(Book.total_pages)
            ).scalar() or 0
            
            return {
                "total_books": total_books,
                "status_counts": status_counts,
                "total_chunks": total_chunks,
                "total_pages": total_pages
            }
            
        except Exception as e:
            logger.error(f"Error getting book statistics: {str(e)}")
            raise
    
    def search_books(self, query: str, limit: int = 20) -> List[Book]:
        """Search books by title, authors, or description."""
        try:
            search_term = f"%{query}%"
            
            books = self.db.query(Book).filter(
                Book.title.ilike(search_term) |
                Book.description.ilike(search_term) |
                Book.tags.any(search_term)
            ).limit(limit).all()
            
            return books
            
        except Exception as e:
            logger.error(f"Error searching books: {str(e)}")
            raise
