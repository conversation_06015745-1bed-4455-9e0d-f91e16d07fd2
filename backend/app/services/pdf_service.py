"""
PDF processing service for extracting and chunking text from medical books.
"""
import os
import hashlib
import re
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

import fitz  # PyMuPDF
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.logging import get_logger
from app.db.models import Book, Chunk, ProcessingStatus, TopicCategory
from app.utils.text_processing import TextProcessor

logger = get_logger("services.pdf")


class PDFProcessor:
    """PDF processing and text extraction."""
    
    def __init__(self):
        self.text_processor = TextProcessor()
    
    def extract_text_from_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Extract text from PDF file with metadata.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            List of page dictionaries with text and metadata
        """
        try:
            doc = fitz.open(file_path)
            pages = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Extract text
                text = page.get_text()
                
                # Extract metadata
                page_data = {
                    "page_number": page_num + 1,
                    "text": text,
                    "char_count": len(text),
                    "word_count": len(text.split()),
                    "bbox": page.rect,  # Page bounding box
                }
                
                # Try to extract chapter/section titles
                chapter_title = self._extract_chapter_title(text)
                if chapter_title:
                    page_data["chapter_title"] = chapter_title
                
                pages.append(page_data)
            
            doc.close()
            logger.info(f"Extracted text from {len(pages)} pages")
            return pages
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            raise
    
    def _extract_chapter_title(self, text: str) -> Optional[str]:
        """Extract chapter title from page text."""
        lines = text.split('\n')
        
        # Look for common chapter patterns
        chapter_patterns = [
            r'^Chapter\s+\d+[:\-\s]+(.+)$',
            r'^CHAPTER\s+\d+[:\-\s]+(.+)$',
            r'^\d+\.\s+(.+)$',
            r'^[A-Z][A-Z\s]{10,}$',  # All caps titles
        ]
        
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if not line:
                continue
                
            for pattern in chapter_patterns:
                match = re.match(pattern, line)
                if match:
                    title = match.group(1) if match.groups() else line
                    return title.strip()
        
        return None
    
    def chunk_text(self, pages: List[Dict[str, Any]], book_id: str) -> List[Dict[str, Any]]:
        """
        Chunk text into smaller segments for vector storage.
        
        Args:
            pages: List of page dictionaries
            book_id: Book ID for reference
            
        Returns:
            List of chunk dictionaries
        """
        chunks = []
        chunk_index = 0
        
        for page in pages:
            page_text = page["text"]
            page_number = page["page_number"]
            chapter_title = page.get("chapter_title")
            
            # Split page into chunks
            page_chunks = self.text_processor.split_text(
                page_text,
                chunk_size=settings.CHUNK_SIZE,
                chunk_overlap=settings.CHUNK_OVERLAP
            )
            
            for i, chunk_text in enumerate(page_chunks):
                if not chunk_text.strip():
                    continue
                
                # Create chunk metadata
                chunk_data = {
                    "content": chunk_text,
                    "content_hash": self._generate_content_hash(chunk_text),
                    "book_id": book_id,
                    "page_number": page_number,
                    "chunk_index": chunk_index,
                    "chapter_title": chapter_title,
                    "word_count": len(chunk_text.split()),
                    "char_count": len(chunk_text),
                }
                
                # Add context from surrounding chunks
                if i > 0:
                    chunk_data["preceding_context"] = page_chunks[i-1][-200:]  # Last 200 chars
                if i < len(page_chunks) - 1:
                    chunk_data["following_context"] = page_chunks[i+1][:200]  # First 200 chars
                
                # Categorize chunk
                category = self._categorize_chunk(chunk_text)
                if category:
                    chunk_data["topic_category"] = category
                
                # Extract medical topics
                topics = self._extract_medical_topics(chunk_text)
                if topics:
                    chunk_data["medical_topics"] = topics
                
                chunks.append(chunk_data)
                chunk_index += 1
        
        logger.info(f"Created {len(chunks)} chunks from {len(pages)} pages")
        return chunks
    
    def _generate_content_hash(self, content: str) -> str:
        """Generate hash for content deduplication."""
        return hashlib.sha256(content.encode()).hexdigest()
    
    def _categorize_chunk(self, text: str) -> Optional[TopicCategory]:
        """Categorize chunk based on content."""
        text_lower = text.lower()
        
        # Define category keywords
        category_keywords = {
            TopicCategory.DIAGNOSIS: [
                "diagnosis", "diagnostic", "symptoms", "signs", "presentation",
                "clinical features", "differential diagnosis", "workup"
            ],
            TopicCategory.TREATMENT: [
                "treatment", "therapy", "management", "medication", "drug",
                "surgery", "surgical", "intervention", "procedure"
            ],
            TopicCategory.PROGNOSIS: [
                "prognosis", "outcome", "survival", "mortality", "morbidity",
                "complications", "follow-up", "long-term"
            ],
            TopicCategory.PATHOPHYSIOLOGY: [
                "pathophysiology", "mechanism", "etiology", "pathogenesis",
                "molecular", "cellular", "biochemical"
            ],
            TopicCategory.EPIDEMIOLOGY: [
                "epidemiology", "incidence", "prevalence", "risk factors",
                "demographics", "population", "statistics"
            ],
            TopicCategory.PREVENTION: [
                "prevention", "prophylaxis", "screening", "vaccination",
                "lifestyle", "diet", "exercise"
            ]
        }
        
        # Score each category
        category_scores = {}
        for category, keywords in category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return TopicCategory.GENERAL
    
    def _extract_medical_topics(self, text: str) -> List[str]:
        """Extract medical topics/conditions from text."""
        # This is a simplified implementation
        # In production, you might use NER models or medical ontologies
        
        medical_patterns = [
            r'\b[A-Z][a-z]+\s+[Ss]yndrome\b',  # Syndromes
            r'\b[A-Z][a-z]+\s+[Dd]isease\b',   # Diseases
            r'\b[A-Z][a-z]+\s+[Dd]isorder\b',  # Disorders
            r'\b[A-Z][a-z]+itis\b',            # Inflammations
            r'\b[A-Z][a-z]+oma\b',             # Tumors
        ]
        
        topics = []
        for pattern in medical_patterns:
            matches = re.findall(pattern, text)
            topics.extend(matches)
        
        # Remove duplicates and return
        return list(set(topics))


class PDFService:
    """Service for managing PDF processing workflow."""
    
    def __init__(self, db: Session):
        self.db = db
        self.processor = PDFProcessor()
    
    def process_pdf(self, book_id: str) -> bool:
        """
        Process a PDF file and create chunks.
        
        Args:
            book_id: Book ID to process
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get book from database
            book = self.db.query(Book).filter(Book.id == book_id).first()
            if not book:
                logger.error(f"Book not found: {book_id}")
                return False
            
            # Update status to processing
            book.processing_status = ProcessingStatus.PROCESSING
            self.db.commit()
            
            logger.info(f"Starting PDF processing for book: {book.title}")
            
            # Extract text from PDF
            pages = self.processor.extract_text_from_pdf(book.file_path)
            
            # Update total pages
            book.total_pages = len(pages)
            self.db.commit()
            
            # Create chunks
            chunks_data = self.processor.chunk_text(pages, str(book.id))
            
            # Save chunks to database
            for chunk_data in chunks_data:
                chunk = Chunk(**chunk_data)
                self.db.add(chunk)
            
            # Update book status
            book.total_chunks = len(chunks_data)
            book.processing_status = ProcessingStatus.COMPLETED
            self.db.commit()
            
            logger.info(f"Successfully processed PDF: {book.title} ({len(chunks_data)} chunks)")
            return True
            
        except Exception as e:
            # Update status to failed
            book.processing_status = ProcessingStatus.FAILED
            book.processing_error = str(e)
            self.db.commit()
            
            logger.error(f"Error processing PDF {book_id}: {str(e)}")
            return False
    
    def validate_pdf_file(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """
        Validate PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                return False, "File does not exist"
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > settings.MAX_FILE_SIZE:
                return False, f"File size exceeds maximum allowed size ({settings.MAX_FILE_SIZE} bytes)"
            
            # Check if it's a valid PDF
            try:
                doc = fitz.open(file_path)
                page_count = len(doc)
                doc.close()
                
                if page_count == 0:
                    return False, "PDF file has no pages"
                
            except Exception as e:
                return False, f"Invalid PDF file: {str(e)}"
            
            return True, None
            
        except Exception as e:
            return False, f"Error validating file: {str(e)}"
    
    def get_file_hash(self, file_path: str) -> str:
        """Generate hash for file deduplication."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
