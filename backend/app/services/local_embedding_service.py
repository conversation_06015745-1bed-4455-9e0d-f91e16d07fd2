"""
Local embedding service using SentenceTransformers for offline operation.
Provides high-quality embeddings without requiring external API calls.
"""
import os
import logging
import numpy as np
from typing import List, Union, Optional, Dict, Any
from functools import lru_cache
import torch
from sentence_transformers import SentenceTransformer
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("services.local_embedding")


class LocalEmbeddingService:
    """
    Production-quality local embedding service using SentenceTransformers.
    
    Features:
    - Offline operation (no API calls)
    - Model caching and reuse
    - Batch processing for efficiency
    - GPU acceleration when available
    - Error handling and fallbacks
    """
    
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        """
        Initialize the local embedding service.
        
        Args:
            model_name: SentenceTransformers model name. Options:
                - "sentence-transformers/all-MiniLM-L6-v2" (384 dim, fast, good quality)
                - "hkunlp/instructor-xl" (768 dim, slower, highest quality)
                - "sentence-transformers/all-mpnet-base-v2" (768 dim, balanced)
        """
        self.model_name = model_name
        self.model = None
        self.device = self._get_device()
        self.embedding_dimension = None
        self._initialize_model()
    
    def _get_device(self) -> str:
        """Determine the best device for model inference."""
        if torch.cuda.is_available():
            device = "cuda"
            logger.info("Using GPU for embeddings")
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"  # Apple Silicon GPU
            logger.info("Using Apple Silicon GPU for embeddings")
        else:
            device = "cpu"
            logger.info("Using CPU for embeddings")
        return device
    
    def _initialize_model(self):
        """Initialize and cache the SentenceTransformer model."""
        try:
            logger.info(f"Loading embedding model: {self.model_name}")
            
            # Create cache directory
            cache_dir = os.path.join(settings.UPLOAD_DIR, "model_cache")
            os.makedirs(cache_dir, exist_ok=True)
            
            # Load model with caching
            self.model = SentenceTransformer(
                self.model_name,
                device=self.device,
                cache_folder=cache_dir
            )
            
            # Get embedding dimension
            self.embedding_dimension = self.model.get_sentence_embedding_dimension()
            
            logger.info(f"Model loaded successfully. Dimension: {self.embedding_dimension}")
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # Fallback to a smaller, more reliable model
            try:
                logger.info("Attempting fallback to all-MiniLM-L6-v2")
                self.model_name = "sentence-transformers/all-MiniLM-L6-v2"
                self.model = SentenceTransformer(self.model_name, device=self.device)
                self.embedding_dimension = self.model.get_sentence_embedding_dimension()
                logger.info(f"Fallback model loaded. Dimension: {self.embedding_dimension}")
            except Exception as fallback_error:
                logger.error(f"Fallback model also failed: {fallback_error}")
                raise RuntimeError("Could not load any embedding model")
    
    @lru_cache(maxsize=1000)
    def encode_single_cached(self, text: str) -> np.ndarray:
        """
        Encode a single text with caching for frequently used queries.
        
        Args:
            text: Input text to encode
            
        Returns:
            Embedding vector as numpy array
        """
        return self._encode_batch([text])[0]
    
    def encode_single(self, text: str) -> np.ndarray:
        """
        Encode a single text string into an embedding vector.
        
        Args:
            text: Input text to encode
            
        Returns:
            Embedding vector as numpy array
        """
        if not self.model:
            raise RuntimeError("Embedding model not initialized")
        
        if not text or not text.strip():
            logger.warning("Empty text provided for encoding")
            return np.zeros(self.embedding_dimension, dtype=np.float32)
        
        try:
            # Clean and prepare text
            cleaned_text = self._preprocess_text(text)
            
            # Generate embedding
            embedding = self.model.encode(
                cleaned_text,
                convert_to_numpy=True,
                normalize_embeddings=True,
                show_progress_bar=False
            )
            
            return embedding.astype(np.float32)
            
        except Exception as e:
            logger.error(f"Error encoding text: {e}")
            # Return zero vector as fallback
            return np.zeros(self.embedding_dimension, dtype=np.float32)
    
    def encode_batch(self, texts: List[str], batch_size: int = 32) -> List[np.ndarray]:
        """
        Encode multiple texts efficiently in batches.
        
        Args:
            texts: List of input texts to encode
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embedding vectors as numpy arrays
        """
        if not self.model:
            raise RuntimeError("Embedding model not initialized")
        
        if not texts:
            return []
        
        try:
            # Clean and prepare texts
            cleaned_texts = [self._preprocess_text(text) for text in texts]
            
            # Generate embeddings in batches
            embeddings = self.model.encode(
                cleaned_texts,
                batch_size=batch_size,
                convert_to_numpy=True,
                normalize_embeddings=True,
                show_progress_bar=len(texts) > 100
            )
            
            return [emb.astype(np.float32) for emb in embeddings]
            
        except Exception as e:
            logger.error(f"Error encoding batch: {e}")
            # Return zero vectors as fallback
            return [np.zeros(self.embedding_dimension, dtype=np.float32) for _ in texts]
    
    def _encode_batch(self, texts: List[str]) -> List[np.ndarray]:
        """Internal method for batch encoding (used by cached method)."""
        return self.encode_batch(texts)
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for better embedding quality.
        
        Args:
            text: Raw input text
            
        Returns:
            Cleaned and preprocessed text
        """
        if not text:
            return ""
        
        # Basic cleaning
        text = text.strip()
        
        # Remove excessive whitespace
        text = " ".join(text.split())
        
        # Truncate if too long (most models have token limits)
        max_length = 512  # Conservative limit for most models
        if len(text) > max_length:
            text = text[:max_length]
            logger.debug(f"Text truncated to {max_length} characters")
        
        return text
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this model."""
        return self.embedding_dimension
    
    def get_model_info(self) -> dict:
        """Get information about the current model."""
        return {
            "model_name": self.model_name,
            "embedding_dimension": self.embedding_dimension,
            "device": self.device,
            "is_loaded": self.model is not None
        }
    
    def similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score between -1 and 1
        """
        try:
            # Normalize vectors
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # Calculate cosine similarity
            similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check for the embedding service."""
        try:
            if self.model is None:
                return {
                    "status": "unhealthy",
                    "error": "Model not loaded"
                }

            # Test embedding generation with a simple text
            test_text = "health check test"
            embedding = await self.get_embeddings([test_text])

            if embedding and len(embedding) > 0 and len(embedding[0]) == self.embedding_dimension:
                return {
                    "status": "healthy",
                    "model": self.model_name,
                    "dimension": self.embedding_dimension,
                    "device": str(self.device)
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Invalid embedding output"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance for reuse across requests
_embedding_service: Optional[LocalEmbeddingService] = None


def get_embedding_service() -> LocalEmbeddingService:
    """
    Get or create the global embedding service instance.
    
    Returns:
        Singleton LocalEmbeddingService instance
    """
    global _embedding_service
    
    if _embedding_service is None:
        # Choose model based on performance requirements
        model_name = getattr(settings, 'EMBEDDING_MODEL_LOCAL', 'sentence-transformers/all-MiniLM-L6-v2')
        _embedding_service = LocalEmbeddingService(model_name)
    
    return _embedding_service


def encode_query(query: str) -> np.ndarray:
    """
    Convenience function to encode a search query.
    
    Args:
        query: Search query text
        
    Returns:
        Query embedding vector
    """
    service = get_embedding_service()
    return service.encode_single_cached(query)


def encode_documents(documents: List[str]) -> List[np.ndarray]:
    """
    Convenience function to encode multiple documents.
    
    Args:
        documents: List of document texts
        
    Returns:
        List of document embedding vectors
    """
    service = get_embedding_service()
    return service.encode_batch(documents)
