"""
Vector database service for managing embeddings with Qdrant.
"""
import uuid
from typing import List, Dict, Any, Optional, Tu<PERSON>
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger("services.vector")


class VectorService:
    """Service for managing vector operations with Qdrant."""
    
    def __init__(self):
        self.client = QdrantClient(
            url=settings.QDRANT_URL,
            api_key=settings.QDRANT_API_KEY
        )
        self.collection_name = settings.QDRANT_COLLECTION_NAME
        self._ensure_collection_exists()
    
    def _ensure_collection_exists(self):
        """Ensure the collection exists in Qdrant."""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                logger.info(f"Creating collection: {self.collection_name}")
                
                # Create collection with vector configuration
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=settings.EMBEDDING_DIMENSION,
                        distance=Distance.COSINE
                    )
                )
                
                # Create indexes for better performance
                self._create_indexes()
                
                logger.info(f"Collection created successfully: {self.collection_name}")
            else:
                logger.info(f"Collection already exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")
            raise
    
    def _create_indexes(self):
        """Create indexes for better search performance."""
        try:
            # Create index for book_id
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="book_id",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            # Create index for topic_category
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="topic_category",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            # Create index for medical_topics
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="medical_topics",
                field_schema=models.PayloadSchemaType.KEYWORD
            )
            
            logger.info("Indexes created successfully")
            
        except Exception as e:
            logger.warning(f"Error creating indexes: {str(e)}")
    
    def add_vectors(self, vectors_data: List[Dict[str, Any]]) -> List[str]:
        """
        Add vectors to the collection.
        
        Args:
            vectors_data: List of dictionaries containing vector data
                Each dict should have: vector, chunk_id, and metadata
                
        Returns:
            List of vector IDs
        """
        try:
            points = []
            vector_ids = []
            
            for data in vectors_data:
                vector_id = str(uuid.uuid4())
                vector_ids.append(vector_id)
                
                # Create point
                point = PointStruct(
                    id=vector_id,
                    vector=data["vector"],
                    payload={
                        "chunk_id": data["chunk_id"],
                        "book_id": data.get("book_id"),
                        "content": data.get("content", ""),
                        "page_number": data.get("page_number"),
                        "chapter_title": data.get("chapter_title"),
                        "topic_category": data.get("topic_category"),
                        "medical_topics": data.get("medical_topics", []),
                        "word_count": data.get("word_count", 0),
                        "char_count": data.get("char_count", 0),
                        "embedding_model": data.get("embedding_model", settings.EMBEDDING_MODEL)
                    }
                )
                points.append(point)
            
            # Upload points to Qdrant
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Added {len(points)} vectors to collection")
            return vector_ids
            
        except Exception as e:
            logger.error(f"Error adding vectors: {str(e)}")
            raise
    
    def search_vectors(
        self,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar vectors.
        
        Args:
            query_vector: Query vector for similarity search
            limit: Maximum number of results to return
            score_threshold: Minimum similarity score threshold
            filters: Optional filters to apply
            
        Returns:
            List of search results with metadata
        """
        try:
            # Build filter conditions
            filter_conditions = None
            if filters:
                conditions = []
                
                if "book_id" in filters:
                    conditions.append(
                        FieldCondition(
                            key="book_id",
                            match=MatchValue(value=filters["book_id"])
                        )
                    )
                
                if "topic_category" in filters:
                    conditions.append(
                        FieldCondition(
                            key="topic_category",
                            match=MatchValue(value=filters["topic_category"])
                        )
                    )
                
                if "medical_topics" in filters:
                    for topic in filters["medical_topics"]:
                        conditions.append(
                            FieldCondition(
                                key="medical_topics",
                                match=MatchValue(value=topic)
                            )
                        )
                
                if conditions:
                    filter_conditions = Filter(must=conditions)
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                query_filter=filter_conditions,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    "vector_id": result.id,
                    "score": result.score,
                    "chunk_id": result.payload.get("chunk_id"),
                    "book_id": result.payload.get("book_id"),
                    "content": result.payload.get("content"),
                    "page_number": result.payload.get("page_number"),
                    "chapter_title": result.payload.get("chapter_title"),
                    "topic_category": result.payload.get("topic_category"),
                    "medical_topics": result.payload.get("medical_topics", []),
                    "word_count": result.payload.get("word_count"),
                    "char_count": result.payload.get("char_count")
                })
            
            logger.info(f"Found {len(results)} similar vectors")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vectors: {str(e)}")
            raise
    
    def delete_vectors_by_book(self, book_id: str) -> bool:
        """
        Delete all vectors for a specific book.
        
        Args:
            book_id: Book ID to delete vectors for
            
        Returns:
            True if successful
        """
        try:
            # Delete points with matching book_id
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="book_id",
                                match=MatchValue(value=book_id)
                            )
                        ]
                    )
                )
            )
            
            logger.info(f"Deleted vectors for book: {book_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting vectors for book {book_id}: {str(e)}")
            raise
    
    def delete_vector(self, vector_id: str) -> bool:
        """
        Delete a specific vector.
        
        Args:
            vector_id: Vector ID to delete
            
        Returns:
            True if successful
        """
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=[vector_id]
                )
            )
            
            logger.info(f"Deleted vector: {vector_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting vector {vector_id}: {str(e)}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        try:
            info = self.client.get_collection(self.collection_name)
            
            return {
                "name": info.config.params.vectors.size,
                "vector_size": info.config.params.vectors.size,
                "distance": info.config.params.vectors.distance,
                "points_count": info.points_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "status": info.status
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            raise
    
    def update_vector_metadata(self, vector_id: str, metadata: Dict[str, Any]) -> bool:
        """
        Update metadata for a specific vector.
        
        Args:
            vector_id: Vector ID to update
            metadata: New metadata to set
            
        Returns:
            True if successful
        """
        try:
            self.client.set_payload(
                collection_name=self.collection_name,
                payload=metadata,
                points=[vector_id]
            )
            
            logger.info(f"Updated metadata for vector: {vector_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating vector metadata {vector_id}: {str(e)}")
            raise
    
    def batch_search(
        self,
        query_vectors: List[List[float]],
        limit: int = 10,
        score_threshold: float = 0.7
    ) -> List[List[Dict[str, Any]]]:
        """
        Perform batch search for multiple query vectors.
        
        Args:
            query_vectors: List of query vectors
            limit: Maximum number of results per query
            score_threshold: Minimum similarity score threshold
            
        Returns:
            List of search results for each query
        """
        try:
            # Perform batch search
            search_queries = [
                models.SearchRequest(
                    vector=query_vector,
                    limit=limit,
                    score_threshold=score_threshold,
                    with_payload=True,
                    with_vector=False
                )
                for query_vector in query_vectors
            ]
            
            batch_results = self.client.search_batch(
                collection_name=self.collection_name,
                requests=search_queries
            )
            
            # Format results
            all_results = []
            for search_results in batch_results:
                results = []
                for result in search_results:
                    results.append({
                        "vector_id": result.id,
                        "score": result.score,
                        "chunk_id": result.payload.get("chunk_id"),
                        "content": result.payload.get("content"),
                        "metadata": result.payload
                    })
                all_results.append(results)
            
            logger.info(f"Performed batch search for {len(query_vectors)} queries")
            return all_results
            
        except Exception as e:
            logger.error(f"Error in batch search: {str(e)}")
            raise
