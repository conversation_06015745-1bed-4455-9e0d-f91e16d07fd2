"""
Text processing utilities for medical content.
"""
import re
from typing import List, Optional
from app.core.logging import get_logger

logger = get_logger("utils.text_processing")


class TextProcessor:
    """Text processing and cleaning utilities."""
    
    def __init__(self):
        # Common medical abbreviations that should be preserved
        self.medical_abbreviations = {
            "Dr.", "Mr.", "Mrs.", "Ms.", "Prof.", "vs.", "etc.", "i.e.", "e.g.",
            "mg", "kg", "ml", "cm", "mm", "mcg", "IU", "bid", "tid", "qid",
            "q.d.", "b.i.d.", "t.i.d.", "q.i.d.", "p.r.n.", "a.c.", "p.c.",
            "h.s.", "q.h.", "q.4h.", "q.6h.", "q.8h.", "q.12h.",
            "IV", "IM", "PO", "SQ", "PR", "SL", "TOP", "INH",
            "CBC", "BUN", "ESR", "CRP", "LDH", "ALT", "AST", "BNP",
            "ECG", "EKG", "CT", "MRI", "PET", "US", "CXR",
            "BP", "HR", "RR", "O2", "CO2", "pH", "WBC", "RBC", "Hgb", "Hct",
            "DNA", "RNA", "PCR", "ELISA", "HIV", "HBV", "HCV", "TB",
            "ICU", "ER", "OR", "PACU", "CCU", "NICU", "PICU"
        }
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text content.
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page numbers and headers/footers (common patterns)
        text = re.sub(r'^\d+\s*$', '', text, flags=re.MULTILINE)
        text = re.sub(r'^Page \d+.*$', '', text, flags=re.MULTILINE)
        text = re.sub(r'^Chapter \d+.*\d+$', '', text, flags=re.MULTILINE)
        
        # Remove figure/table references that are standalone
        text = re.sub(r'^(Figure|Table|Fig\.|Tab\.)\s+\d+.*$', '', text, flags=re.MULTILINE)
        
        # Remove excessive line breaks
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Normalize quotes
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r"[''']", "'", text)
        
        # Fix common OCR errors
        text = self._fix_ocr_errors(text)
        
        return text.strip()
    
    def _fix_ocr_errors(self, text: str) -> str:
        """Fix common OCR errors in medical text."""
        # Common OCR substitutions
        ocr_fixes = {
            r'\bl\b': 'I',  # lowercase l to uppercase I
            r'\b0\b': 'O',  # zero to letter O in some contexts
            r'rn': 'm',     # rn to m
            r'vv': 'w',     # double v to w
            r'cl': 'd',     # cl to d in some contexts
        }
        
        for pattern, replacement in ocr_fixes.items():
            text = re.sub(pattern, replacement, text)
        
        return text
    
    def split_text(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        Split text into overlapping chunks.
        
        Args:
            text: Text to split
            chunk_size: Maximum size of each chunk
            chunk_overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks
        """
        if not text or len(text) <= chunk_size:
            return [text] if text else []
        
        chunks = []
        start = 0
        
        while start < len(text):
            # Calculate end position
            end = start + chunk_size
            
            # If this is not the last chunk, try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                search_start = max(end - 200, start)
                sentence_end = self._find_sentence_boundary(text, search_start, end)
                
                if sentence_end > start:
                    end = sentence_end
            
            # Extract chunk
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position (with overlap)
            start = max(start + 1, end - chunk_overlap)
            
            # Prevent infinite loop
            if start >= len(text):
                break
        
        return chunks
    
    def _find_sentence_boundary(self, text: str, search_start: int, search_end: int) -> int:
        """Find the best sentence boundary within the given range."""
        # Look for sentence endings
        sentence_endings = ['.', '!', '?']
        
        # Search backwards from the end
        for i in range(search_end - 1, search_start - 1, -1):
            if text[i] in sentence_endings:
                # Check if this is likely a real sentence ending
                if self._is_sentence_ending(text, i):
                    return i + 1
        
        # If no sentence ending found, look for paragraph breaks
        for i in range(search_end - 1, search_start - 1, -1):
            if text[i] == '\n':
                return i + 1
        
        # If no good break point, return the original end
        return search_end
    
    def _is_sentence_ending(self, text: str, pos: int) -> bool:
        """Check if a period/punctuation is likely a sentence ending."""
        if pos >= len(text) - 1:
            return True
        
        # Get context around the position
        before = text[max(0, pos - 10):pos]
        after = text[pos + 1:pos + 10]
        
        # Check for common abbreviations
        for abbrev in self.medical_abbreviations:
            if before.endswith(abbrev[:-1]):  # Remove the period from abbreviation
                return False
        
        # Check if followed by whitespace and capital letter
        if after and after[0].isspace() and len(after) > 1 and after[1].isupper():
            return True
        
        # Check for numbered lists (e.g., "1. ", "2. ")
        if re.match(r'^\s*\d+\.?\s+[A-Z]', after):
            return True
        
        return False
    
    def extract_sentences(self, text: str) -> List[str]:
        """Extract sentences from text."""
        # Simple sentence splitting (can be improved with NLTK or spaCy)
        sentences = []
        
        # Split on sentence endings
        parts = re.split(r'[.!?]+', text)
        
        for part in parts:
            part = part.strip()
            if part and len(part) > 10:  # Filter out very short fragments
                sentences.append(part)
        
        return sentences
    
    def normalize_medical_terms(self, text: str) -> str:
        """Normalize medical terminology for better matching."""
        # Convert to lowercase for processing
        normalized = text.lower()
        
        # Normalize common medical term variations
        normalizations = {
            r'\bheart attack\b': 'myocardial infarction',
            r'\bstroke\b': 'cerebrovascular accident',
            r'\bhigh blood pressure\b': 'hypertension',
            r'\bsugar diabetes\b': 'diabetes mellitus',
            r'\bkidney failure\b': 'renal failure',
            r'\blung cancer\b': 'pulmonary carcinoma',
        }
        
        for pattern, replacement in normalizations.items():
            normalized = re.sub(pattern, replacement, normalized)
        
        return normalized
    
    def extract_key_phrases(self, text: str, min_length: int = 3) -> List[str]:
        """Extract key phrases from medical text."""
        # This is a simplified implementation
        # In production, you might use more sophisticated NLP techniques
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
            'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
            'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
        }
        
        # Extract potential phrases
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        phrases = []
        
        # Create n-grams
        for i in range(len(words)):
            for j in range(i + min_length, min(i + 6, len(words) + 1)):  # Up to 5-grams
                phrase_words = words[i:j]
                
                # Filter out phrases with too many stop words
                if sum(1 for w in phrase_words if w in stop_words) < len(phrase_words) / 2:
                    phrase = ' '.join(phrase_words)
                    phrases.append(phrase)
        
        # Remove duplicates and return
        return list(set(phrases))
