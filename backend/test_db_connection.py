#!/usr/bin/env python3
"""
Test database and services connections.
"""
import psycopg2
import requests
import sys
import os

def test_postgresql():
    """Test PostgreSQL connection."""
    print("🔍 Testing PostgreSQL connection...")
    try:
        conn = psycopg2.connect('postgresql://medprep_user:medprep_password@localhost:5432/medprep')
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ PostgreSQL connected: {version[0][:50]}...")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_qdrant():
    """Test Qdrant connection."""
    print("🔍 Testing Qdrant connection...")
    try:
        response = requests.get("http://localhost:6333/collections", timeout=5)
        if response.status_code == 200:
            data = response.json()
            collections = data.get('result', {}).get('collections', [])
            print(f"✅ Qdrant connected: {len(collections)} collections")
            for col in collections:
                print(f"   - {col['name']}")
            return True
        else:
            print(f"❌ Qdrant returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Qdrant connection failed: {e}")
        return False

def test_backend_health():
    """Test backend health endpoint."""
    print("🔍 Testing backend health...")
    try:
        response = requests.get("http://localhost:8000/api/v1/admin/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend health: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        return False

def main():
    print("🚀 TESTING ALL SERVICES")
    print("=" * 50)
    
    results = []
    results.append(("PostgreSQL", test_postgresql()))
    results.append(("Qdrant", test_qdrant()))
    results.append(("Backend Health", test_backend_health()))
    
    print("\n" + "=" * 50)
    print("📊 RESULTS SUMMARY")
    print("=" * 50)
    
    all_good = True
    for service, status in results:
        icon = "✅" if status else "❌"
        print(f"{icon} {service}: {'CONNECTED' if status else 'FAILED'}")
        if not status:
            all_good = False
    
    if all_good:
        print("\n🎉 ALL SERVICES ARE RUNNING CORRECTLY!")
        print("The system health should now show as 'healthy'")
    else:
        print("\n⚠️  SOME SERVICES NEED ATTENTION")
        print("Check the failed services above")
    
    return 0 if all_good else 1

if __name__ == "__main__":
    sys.exit(main())
