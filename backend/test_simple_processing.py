#!/usr/bin/env python3
"""
Simple test for book processing functionality.
"""
import requests
import json
import time


def test_processing():
    """Test book processing functionality."""
    base_url = "http://localhost:8000"
    
    print("🚀 TESTING BOOK PROCESSING")
    print("=" * 50)
    
    # Login
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code}")
        return
    
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get books
    print("📚 Getting books...")
    response = requests.get(f"{base_url}/api/v1/admin/books", headers=headers)
    if response.status_code != 200:
        print(f"❌ Failed to get books: {response.status_code}")
        return
    
    books_data = response.json()
    print(f"✅ Got books response")
    
    # Handle different response formats
    if isinstance(books_data, list):
        books = books_data
    elif isinstance(books_data, dict) and 'books' in books_data:
        books = books_data['books']
    else:
        books = []
    
    print(f"📖 Found {len(books)} books")
    
    # Find pending books
    pending_books = []
    for book in books:
        if isinstance(book, dict):
            status = book.get('processing_status', book.get('status', 'unknown'))
            if status == 'pending':
                pending_books.append(book)
                print(f"   📋 Pending: {book.get('title', 'Unknown')} (ID: {book.get('id', 'Unknown')})")
    
    if not pending_books:
        print("ℹ️  No pending books found")
        return
    
    # Process first pending book
    book = pending_books[0]
    book_id = book.get('id')
    book_title = book.get('title', 'Unknown')
    
    print(f"\n⚙️ Processing: {book_title}")
    response = requests.post(f"{base_url}/api/v1/admin/books/{book_id}/process", headers=headers)
    
    if response.status_code == 200:
        print("✅ Processing started successfully!")
        
        # Monitor progress
        print("\n📊 Monitoring progress...")
        for i in range(12):  # Check for 1 minute
            time.sleep(5)
            
            # Check processing queue
            queue_response = requests.get(f"{base_url}/api/v1/admin/processing-queue", headers=headers)
            if queue_response.status_code == 200:
                queue_data = queue_response.json()
                summary = queue_data.get('summary', {})
                
                print(f"   Check #{i+1}: Processing={summary.get('processing', 0)}, "
                      f"Pending={summary.get('pending', 0)}, "
                      f"Completed={summary.get('completed', 0)}, "
                      f"Failed={summary.get('failed', 0)}")
                
                # If no processing or pending, we're done
                if summary.get('processing', 0) == 0 and summary.get('pending', 0) == 0:
                    if summary.get('completed', 0) > 0:
                        print("🎉 Processing completed successfully!")
                    else:
                        print("❌ Processing may have failed")
                    break
        
        print(f"\n🎯 FINAL STATUS:")
        print(f"✅ Book processing has been triggered")
        print(f"📝 You can now:")
        print(f"   1. Check System Monitoring page for real-time status")
        print(f"   2. Use the 'Process' button in Book Management for pending books")
        print(f"   3. Once completed, search functionality will work with the new content")
        
    else:
        print(f"❌ Processing failed: {response.status_code} - {response.text}")


if __name__ == "__main__":
    test_processing()
