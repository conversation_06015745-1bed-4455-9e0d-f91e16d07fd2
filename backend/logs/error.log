2025-07-13 16:19:35 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:36 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:38 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:39 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:42 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:167 - Starlette exception: 404 - Not Found
2025-07-13 16:19:45 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:46 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:20:13 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:13 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:14 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:14 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:54 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:55 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:55 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:51 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:51 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:52 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:52 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:03 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:03 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:04 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:04 - uvicorn.error - ERROR - /Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py:431 - Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:59 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x1104eff50> can't render element of type UUID
2025-07-13 16:23:26 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x10a608490> can't render element of type UUID
2025-07-13 16:26:34 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: FATAL:  role "medprep_user" does not exist

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:33:41 - app.services.user - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:84 - Error creating user: User with this email already exists
2025-07-13 16:35:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:18 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:56 - Search error: [Errno 61] Connection refused
2025-07-13 16:35:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:19 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:56 - Search error: [Errno 61] Connection refused
2025-07-13 16:35:42 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:42 - app.api.qa - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/qa.py:49 - Q&A error: [Errno 61] Connection refused
2025-07-13 16:35:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:43 - app.api.qa - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/qa.py:49 - Q&A error: [Errno 61] Connection refused
2025-07-13 17:32:02 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:32:02 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:21 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:21 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:30 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:30 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:50 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:50 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:35:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:35:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:36:25 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:36:55 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:37:25 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:39:44 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:39:44 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:11 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:41:11 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:41:13 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:41:13 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:41:29 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:29 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:45 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:45 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:45:45 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:45:45 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:45:47 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:45:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:50:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:37 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:55 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:09 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:47 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:48 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:47 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:48 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:51 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:51 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:52 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:59:04 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:59:04 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:59:04 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:59:04 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:59:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:59:05 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:59:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:59:05 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:00:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:00:36 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:00:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:00:36 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:00:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:00:37 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:00:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:00:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:01:06 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:01:06 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:01:07 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:01:07 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:02:14 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:02:14 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:02:14 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:02:14 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:02:15 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:02:15 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:02:15 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:02:15 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:06:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:06:36 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:06:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:06:36 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:06:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:06:37 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:06:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:06:37 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:07:42 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:07:42 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:07:42 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:07:42 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:07:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:07:43 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:07:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:07:43 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:08:12 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:08:12 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:08:13 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:08:13 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:24 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:24 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:25 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:25 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:34:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:34:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:25 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:25 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:26 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:26 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:26 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:26 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:48 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:49 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:49 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:50 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:35:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:35:50 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:38:03 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:03 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:04 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:04 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:05 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:05 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:08 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:08 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:08 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:08 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:09 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:09 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:10 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:10 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:24 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:24 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:25 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:38:25 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:38:25 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:39:29 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:39:29 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:39:29 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:39:29 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:39:30 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:39:30 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:39:30 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:39:30 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:41:16 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:16 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:41:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:17 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:41:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:18 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:41:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:18 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:41:21 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:21 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:41:21 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:21 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:41:22 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:22 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:41:22 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:22 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:41:51 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:51 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:41:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:41:52 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:21 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:21 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:22 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:22 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:40 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:40 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:42:54 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:54 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:54 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:54 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:55 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:55 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:55 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:55 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:57 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:57 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:57 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:57 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:58 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:42:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:42:58 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:44:55 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:55 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:44:55 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:55 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:44:56 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:56 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:44:56 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:56 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:160 - Error getting search analytics: [Errno 61] Connection refused
2025-07-13 18:44:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:44:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:44:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:45:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:45:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:45:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:45:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:45:29 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:45:29 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:45:30 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:45:30 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:45:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:45:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:46:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:46:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:46:29 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:46:29 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:46:30 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:46:30 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:46:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:46:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:47:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:47:00 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 18:56:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:56:35 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:57:05 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:57:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:57:24 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:57:54 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:58:32 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:58:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 18:58:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:06:34 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'UserService' object has no attribute 'get_users_paginated'
2025-07-13 19:06:34 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'UserService' object has no attribute 'get_users_paginated'
2025-07-13 19:06:35 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'UserService' object has no attribute 'get_users_paginated'
2025-07-13 19:06:35 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'UserService' object has no attribute 'get_users_paginated'
2025-07-13 19:06:59 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:06:59 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:00 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:00 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:33 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:33 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:34 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:34 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:49 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:49 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:50 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:50 - app.api.users - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/users.py:182 - Error getting users: 'User' object has no attribute 'last_login'
2025-07-13 19:07:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:07:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:10:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:10:36 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:10:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:10:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:11:09 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:11:09 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:11:15 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:11:15 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:12:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:12:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:12:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:12:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:16:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:16:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:17:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:17:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:17:32 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 19:19:25 - app.services.user - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:118 - Error creating user: User with this email already exists
2025-07-13 19:20:36 - app.services.user - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:118 - Error creating user: User with this email already exists
2025-07-13 19:22:10 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 405 - Method Not Allowed
2025-07-13 19:23:06 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 19:23:06 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:23:06 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:23:06 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 19:25:08 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:25:08 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:25:09 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:25:09 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:25:16 - app.api.books - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/books.py:67 - Error retrieving books: 'Book' object has no attribute 'author'
2025-07-13 19:25:16 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:25:16 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:27:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:27:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:29:11 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:29:11 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:34:08 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:36:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:36:00 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:36:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:36:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:38:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:38:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:39:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:39:50 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:39:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:39:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:41:54 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:42:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:42:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:42:45 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:42:46 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:43:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:43:44 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:43:45 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:43:45 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:44:16 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:26 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:27 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:28 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 19:45:28 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:83 - Error ensuring collection exists: [Errno 61] Connection refused
