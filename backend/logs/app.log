2025-07-13 15:09:52 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 15:12:12 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:19:32 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:19:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:35 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:35 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0531s
2025-07-13 16:19:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:36 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:36 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0056s
2025-07-13 16:19:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:38 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:38 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0066s
2025-07-13 16:19:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:39 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:39 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0126s
2025-07-13 16:19:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:19:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0114s
2025-07-13 16:19:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /favicon.ico
2025-07-13 16:19:42 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:167 - Starlette exception: 404 - Not Found
2025-07-13 16:19:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0034s
2025-07-13 16:19:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:45 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:45 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0390s
2025-07-13 16:19:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:46 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:46 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0080s
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/auth/signup
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0313s
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:13 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:13 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:14 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:14 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:28 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:20:28 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0097s
2025-07-13 16:20:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:54 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:55 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:55 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:45 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:21:51 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:21:51 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:21:51 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:52 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:21:52 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:21:52 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:21:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0044s
2025-07-13 16:22:03 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:03 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:03 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:04 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:04 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:04 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:24 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:87 - Shutting down Medical Preparation Platform API
2025-07-13 16:22:26 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:22:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:45 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0174s
2025-07-13 16:22:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:22:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0096s
2025-07-13 16:22:57 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:87 - Shutting down Medical Preparation Platform API
2025-07-13 16:22:59 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:22:59 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x1104eff50> can't render element of type UUID
2025-07-13 16:23:26 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:23:26 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x10a608490> can't render element of type UUID
2025-07-13 16:23:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:97 - Shutting down Medical Preparation Platform API
2025-07-13 16:26:34 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:26:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:26:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:26:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:26:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:26:34 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: FATAL:  role "medprep_user" does not exist

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:26:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:26:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0051s
2025-07-13 16:26:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:26:37 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:26:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.2958s
2025-07-13 16:27:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:27:17 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:27:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0655s
2025-07-13 16:27:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:27:18 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:27:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0142s
2025-07-13 16:27:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:27:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0197s
2025-07-13 16:27:22 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:27:22 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:27:22 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0359s
2025-07-13 16:27:45 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:27:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:27:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:27:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:27:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:27:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:90 - Database tables created successfully
2025-07-13 16:27:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:27:49 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:27:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0207s
2025-07-13 16:27:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:27:50 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:27:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0031s
2025-07-13 16:27:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:27:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0054s
2025-07-13 16:28:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:28:06 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:28:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0160s
2025-07-13 16:29:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:29:20 - app.services.user - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:192 - Authentication failed - user not found: <EMAIL>
2025-07-13 16:29:20 - app.api.auth - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:77 - Login failed for: <EMAIL>
2025-07-13 16:29:20 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Incorrect email or password
2025-07-13 16:29:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.1689s
2025-07-13 16:29:23 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:29:23 - app.services.user - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:75 - Created new user: <EMAIL>
2025-07-13 16:29:23 - app.api.auth - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:37 - New user registered: <EMAIL>
2025-07-13 16:29:23 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.2775s
2025-07-13 16:30:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:30:05 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-07-13 16:30:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0110s
2025-07-13 16:31:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:31:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.1333s
2025-07-13 16:31:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:31:56 - app.services.user - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:192 - Authentication failed - user not found: <EMAIL>
2025-07-13 16:31:56 - app.api.auth - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:77 - Login failed for: <EMAIL>
2025-07-13 16:31:56 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Incorrect email or password
2025-07-13 16:31:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.3396s
2025-07-13 16:32:12 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/auth/signup
2025-07-13 16:32:12 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0149s
2025-07-13 16:32:12 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:32:12 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:32:12 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0366s
2025-07-13 16:33:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:33:41 - app.services.user - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:84 - Error creating user: User with this email already exists
2025-07-13 16:33:41 - app.api.auth - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:50 - Signup failed: User with this email already exists
2025-07-13 16:33:41 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 400 - User with this email already exists
2025-07-13 16:33:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 400 in 0.1111s
2025-07-13 16:34:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:34:32 - app.services.user - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:203 - User authenticated successfully: <EMAIL>
2025-07-13 16:34:32 - app.api.auth - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:91 - User logged in: <EMAIL>
2025-07-13 16:34:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.3973s
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/search
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0189s
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/search
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0118s
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/search/
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0004s
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/search/
2025-07-13 16:35:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:18 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:56 - Search error: [Errno 61] Connection refused
2025-07-13 16:35:18 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Search failed
2025-07-13 16:35:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.1992s
2025-07-13 16:35:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/search
2025-07-13 16:35:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0830s
2025-07-13 16:35:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/search/
2025-07-13 16:35:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:19 - app.api.search - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/search.py:56 - Search error: [Errno 61] Connection refused
2025-07-13 16:35:19 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Search failed
2025-07-13 16:35:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0496s
2025-07-13 16:35:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/qa/ask
2025-07-13 16:35:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0049s
2025-07-13 16:35:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/qa/ask
2025-07-13 16:35:42 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:42 - app.api.qa - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/qa.py:49 - Q&A error: [Errno 61] Connection refused
2025-07-13 16:35:42 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to generate answer
2025-07-13 16:35:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0478s
2025-07-13 16:35:43 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/qa/ask
2025-07-13 16:35:43 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 16:35:43 - app.api.qa - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/qa.py:49 - Q&A error: [Errno 61] Connection refused
2025-07-13 16:35:43 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to generate answer
2025-07-13 16:35:43 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0240s
2025-07-13 16:36:04 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:36:04 - app.services.user - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:192 - Authentication failed - user not found: <EMAIL>
2025-07-13 16:36:04 - app.api.auth - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:77 - Login failed for: <EMAIL>
2025-07-13 16:36:04 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Incorrect email or password
2025-07-13 16:36:04 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.0763s
2025-07-13 16:40:11 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:40:11 - app.services.user - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:192 - Authentication failed - user not found: <EMAIL>
2025-07-13 16:40:11 - app.api.auth - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:77 - Login failed for: <EMAIL>
2025-07-13 16:40:11 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Incorrect email or password
2025-07-13 16:40:11 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.1034s
2025-07-13 16:40:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:40:29 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': 'admin123', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:40:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0269s
2025-07-13 16:40:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:40:41 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:155 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': 'admin123!', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:40:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0144s
2025-07-13 16:42:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:42:18 - app.services.user - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:75 - Created new user: <EMAIL>
2025-07-13 16:42:18 - app.api.auth - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:37 - New user registered: <EMAIL>
2025-07-13 16:42:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.3395s
2025-07-13 16:43:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:43:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.1437s
2025-07-13 16:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:44:34 - app.services.user - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:203 - User authenticated successfully: <EMAIL>
2025-07-13 16:44:34 - app.api.auth - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:91 - User logged in: <EMAIL>
2025-07-13 16:44:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.3073s
2025-07-13 17:24:40 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 17:24:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 1.0967s
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/system/stats
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/system/stats
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.1007s
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0234s
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:32:02 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.1179s
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:32:02 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:32:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0015s
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0184s
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0082s
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:13 - app.core.dependencies - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/core/dependencies.py:40 - Invalid token provided
2025-07-13 17:34:13 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Could not validate credentials
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.0665s
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:13 - app.core.dependencies - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/core/dependencies.py:40 - Invalid token provided
2025-07-13 17:34:13 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Could not validate credentials
2025-07-13 17:34:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.0037s
2025-07-13 17:34:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 17:34:19 - app.services.user - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/user_service.py:203 - User authenticated successfully: <EMAIL>
2025-07-13 17:34:19 - app.api.auth - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:91 - User logged in: <EMAIL>
2025-07-13 17:34:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.5218s
2025-07-13 17:34:21 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:21 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:21 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0431s
2025-07-13 17:34:21 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:21 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:21 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0072s
2025-07-13 17:34:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.1332s
2025-07-13 17:34:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0053s
2025-07-13 17:34:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:30 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.1135s
2025-07-13 17:34:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:30 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0032s
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/system/health
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/processing/status
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/system/health
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/processing/status
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0376s
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0197s
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0180s
2025-07-13 17:34:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0177s
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/processing/status
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/health
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0017s
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0016s
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/processing/status
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/health
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0028s
2025-07-13 17:34:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0027s
2025-07-13 17:34:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:50 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0258s
2025-07-13 17:34:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:34:50 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:34:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0021s
2025-07-13 17:34:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0733s
2025-07-13 17:34:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:34:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0044s
2025-07-13 17:35:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:35:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:35:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0341s
2025-07-13 17:35:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:35:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:35:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0017s
2025-07-13 17:36:25 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:36:25 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:36:25 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0350s
2025-07-13 17:36:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:36:55 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:36:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0137s
2025-07-13 17:37:25 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:37:25 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:37:25 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0114s
2025-07-13 17:39:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books/upload
2025-07-13 17:39:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0084s
2025-07-13 17:39:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/admin/books/upload
2025-07-13 17:39:34 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:210 - Saved uploaded file: Grant’s Dissector 16ed [Shared By Ussama Maqbool].pdf -> ./uploads/eec73ee8-6f6a-4e88-8732-856ed6d195e0.pdf
2025-07-13 17:39:35 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:107 - Created new book: Grants Dissector
2025-07-13 17:39:35 - app.api.admin - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:89 - Book uploaded <NAME_EMAIL>: Grants Dissector
2025-07-13 17:39:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.5226s
2025-07-13 17:39:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:39:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0859s
2025-07-13 17:39:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:39:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0042s
2025-07-13 17:39:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:39:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0161s
2025-07-13 17:39:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:39:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0061s
2025-07-13 17:39:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:39:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0066s
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/users
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/users
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0029s
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0383s
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/users
2025-07-13 17:39:44 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0040s
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/users
2025-07-13 17:39:44 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:39:44 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0012s
2025-07-13 17:41:11 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/admin/books/upload
2025-07-13 17:41:11 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:210 - Saved uploaded file: Grant’s Dissector 16ed [Shared By Ussama Maqbool].pdf -> ./uploads/90a860bb-ae69-4937-9897-1da6ec190e26.pdf
2025-07-13 17:41:11 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:41:11 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:41:11 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to upload book
2025-07-13 17:41:11 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.4232s
2025-07-13 17:41:12 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/admin/books/upload
2025-07-13 17:41:13 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:210 - Saved uploaded file: Grant’s Dissector 16ed [Shared By Ussama Maqbool].pdf -> ./uploads/a8ee9db0-8582-4cfa-9976-8f9efba58d8b.pdf
2025-07-13 17:41:13 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:41:13 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:41:13 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to upload book
2025-07-13 17:41:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.3605s
2025-07-13 17:41:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:41:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0255s
2025-07-13 17:41:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:41:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0062s
2025-07-13 17:41:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:41:29 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0064s
2025-07-13 17:41:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/system/stats
2025-07-13 17:41:29 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:29 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0078s
2025-07-13 17:41:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/users
2025-07-13 17:41:45 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0089s
2025-07-13 17:41:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/users
2025-07-13 17:41:45 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:188 - Starlette exception: 404 - Not Found
2025-07-13 17:41:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0020s
2025-07-13 17:41:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:41:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0362s
2025-07-13 17:41:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:41:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0042s
2025-07-13 17:41:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:41:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0135s
2025-07-13 17:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0198s
2025-07-13 17:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0147s
2025-07-13 17:44:33 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:44:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0734s
2025-07-13 17:44:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:44:34 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.7872s
2025-07-13 17:44:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:44:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0397s
2025-07-13 17:44:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:44:49 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0206s
2025-07-13 17:45:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/admin/books/upload
2025-07-13 17:45:45 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:210 - Saved uploaded file: Grant’s Dissector 16ed [Shared By Ussama Maqbool].pdf -> ./uploads/16e13f3a-39fd-467b-830d-0b65b424e80a.pdf
2025-07-13 17:45:45 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:45:45 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:45:45 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to upload book
2025-07-13 17:45:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.4182s
2025-07-13 17:45:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/admin/books/upload
2025-07-13 17:45:47 - app.services.book - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:210 - Saved uploaded file: Grant’s Dissector 16ed [Shared By Ussama Maqbool].pdf -> ./uploads/675280be-9873-4c22-b0d9-85c43a3da931.pdf
2025-07-13 17:45:47 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:116 - Error creating book: A book with this file already exists
2025-07-13 17:45:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:101 - Error uploading book: A book with this file already exists
2025-07-13 17:45:47 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to upload book
2025-07-13 17:45:47 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.3830s
2025-07-13 17:45:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:45:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.2032s
2025-07-13 17:45:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:45:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0404s
2025-07-13 17:46:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:46:55 - app.core.dependencies - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/core/dependencies.py:40 - Invalid token provided
2025-07-13 17:46:55 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 401 - Could not validate credentials
2025-07-13 17:46:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 401 in 0.0115s
2025-07-13 17:47:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:47:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.2170s
2025-07-13 17:47:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:47:27 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0303s
2025-07-13 17:48:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:48:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0321s
2025-07-13 17:48:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:48:05 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0098s
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0198s
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0107s
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0578s
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:49:31 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0055s
2025-07-13 17:49:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/books
2025-07-13 17:49:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0027s
2025-07-13 17:49:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:49:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0181s
2025-07-13 17:49:40 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:49:40 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0361s
2025-07-13 17:50:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:50:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0487s
2025-07-13 17:50:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:50:06 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0101s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0073s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0023s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0028s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users/
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0018s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0012s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users/
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0007s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0372s
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:50:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0043s
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/health
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/health
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0008s
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0007s
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0786s
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:19 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:19 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:19 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0194s
2025-07-13 17:50:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0500s
2025-07-13 17:50:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:20 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:20 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:20 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0261s
2025-07-13 17:50:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:37 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:37 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:37 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:37 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0642s
2025-07-13 17:50:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:38 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:38 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:38 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0313s
2025-07-13 17:50:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0214s
2025-07-13 17:50:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:50:39 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:50:39 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:50:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0198s
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/stats
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/admin/stats
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0215s
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0093s
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:50:55 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0370s
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:50:55 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:55 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:50:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0111s
2025-07-13 17:50:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:50:56 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:50:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0063s
2025-07-13 17:50:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:50:56 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:50:56 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:50:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0047s
2025-07-13 17:51:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0423s
2025-07-13 17:51:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:01 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:01 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0272s
2025-07-13 17:51:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0318s
2025-07-13 17:51:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:02 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:02 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:02 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0275s
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0132s
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0053s
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:51:09 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0125s
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:51:09 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:09 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:51:09 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0077s
2025-07-13 17:51:10 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:51:10 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:51:10 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0259s
2025-07-13 17:51:10 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/stats
2025-07-13 17:51:10 - app.services.book - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/book_service.py:269 - Error getting book statistics: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:413 - Error getting system stats: 'Session' object has no attribute 'func'
2025-07-13 17:51:10 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system statistics
2025-07-13 17:51:10 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0061s
2025-07-13 17:51:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:51:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0075s
2025-07-13 17:51:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:51:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0057s
2025-07-13 17:51:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0381s
2025-07-13 17:51:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:17 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0275s
2025-07-13 17:51:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0498s
2025-07-13 17:51:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:18 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0231s
2025-07-13 17:51:47 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:47 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:47 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:47 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0412s
2025-07-13 17:51:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:51:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:51:48 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:51:48 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:51:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0224s
2025-07-13 17:52:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:17 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:17 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:17 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:17 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0599s
2025-07-13 17:52:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:18 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:18 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:18 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:18 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0227s
2025-07-13 17:52:47 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:47 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:47 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:47 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:47 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0454s
2025-07-13 17:52:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:48 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:48 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:48 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0236s
2025-07-13 17:52:51 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:51 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:51 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:51 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:51 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.2271s
2025-07-13 17:52:51 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:52 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:52 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:52 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:52 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.2203s
2025-07-13 17:52:52 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:53 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0525s
2025-07-13 17:52:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:52:53 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:52:53 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:52:53 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0289s
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0037s
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0024s
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0229s
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:52:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0130s
2025-07-13 17:52:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:52:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0495s
2025-07-13 17:52:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:52:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0043s
2025-07-13 17:53:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:53:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0293s
2025-07-13 17:53:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:53:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0057s
2025-07-13 17:53:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:53:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.5161s
2025-07-13 17:53:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:53:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0509s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0070s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0071s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0014s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users/
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0015s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0017s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/users/
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0021s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0155s
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:53:50 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0084s
2025-07-13 17:53:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:53:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:53:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0483s
2025-07-13 17:53:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:53:58 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:58 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:53:58 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0278s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:53:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0238s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/health
2025-07-13 17:53:59 - app.services.vector - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/services/vector_service.py:54 - Error ensuring collection exists: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.api.admin - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/admin.py:550 - Error getting system health: [Errno 61] Connection refused
2025-07-13 17:53:59 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:133 - HTTP exception: 500 - Failed to get system health
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0245s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0023s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 307 in 0.0016s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0064s
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/users/
2025-07-13 17:53:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0072s
2025-07-13 17:54:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:54:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0605s
2025-07-13 17:54:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:54:01 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0747s
2025-07-13 17:54:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:54:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.2179s
2025-07-13 17:54:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:54:30 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0493s
2025-07-13 17:55:10 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:97 - Shutting down Medical Preparation Platform API
2025-07-13 17:55:14 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 17:55:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 17:55:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 17:55:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 17:55:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 17:55:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:90 - Database tables created successfully
2025-07-13 17:55:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:55:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.3637s
2025-07-13 17:55:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /api/v1/admin/books
2025-07-13 17:55:15 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0292s
2025-07-13 17:55:24 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:97 - Shutting down Medical Preparation Platform API
