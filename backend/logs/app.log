2025-07-13 15:09:52 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 15:12:12 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:19:32 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:19:32 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:19:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:35 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:35 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:35 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0531s
2025-07-13 16:19:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:36 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:36 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:36 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0056s
2025-07-13 16:19:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:38 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:38 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:38 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0066s
2025-07-13 16:19:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:39 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:39 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:39 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0126s
2025-07-13 16:19:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:19:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0114s
2025-07-13 16:19:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /favicon.ico
2025-07-13 16:19:42 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:167 - Starlette exception: 404 - Not Found
2025-07-13 16:19:42 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 404 in 0.0034s
2025-07-13 16:19:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:45 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:45 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0390s
2025-07-13 16:19:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/login
2025-07-13 16:19:46 - app.api.auth - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/api/api_v1/endpoints/auth.py:109 - Login error: (psycopg2.OperationalError) connection to server at "localhost" (::1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?
connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-13 16:19:46 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:123 - HTTP exception: 500 - Internal server error
2025-07-13 16:19:46 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 500 in 0.0080s
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: OPTIONS /api/v1/auth/signup
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0313s
2025-07-13 16:20:13 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:13 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:13 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:14 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:14 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:14 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:28 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:20:28 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0097s
2025-07-13 16:20:54 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:54 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:54 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:20:55 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:20:55 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:20:55 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:45 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:21:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:21:51 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:21:51 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:21:51 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:52 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:21:52 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:21:52 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:21:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:21:56 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0044s
2025-07-13 16:22:03 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:03 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:03 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:04 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:04 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:04 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:189 - Unhandled exception: Object of type ValueError is not JSON serializable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 98, in receive
    return self.receive_nowait()
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 93, in receive_nowait
    raise WouldBlock
anyio.WouldBlock

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 78, in call_next
    message = await recv_stream.receive()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/anyio/streams/memory.py", line 118, in receive
    raise EndOfStream
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 55, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 108, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 32, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 84, in call_next
    raise app_exc
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 88, in __call__
    response = await handler(request, exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/app/main.py", line 153, in validation_exception_handler
    return JSONResponse(
           ^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/MedPrep/backend/venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type ValueError is not JSON serializable
2025-07-13 16:22:24 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:87 - Shutting down Medical Preparation Platform API
2025-07-13 16:22:26 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:22:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:22:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: POST /api/v1/auth/signup
2025-07-13 16:22:45 - app.main - WARNING - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:145 - Validation error: [{'type': 'value_error', 'loc': ('body', 'password'), 'msg': 'Value error, Password must contain at least one uppercase letter', 'input': '12345678', 'ctx': {'error': ValueError('Password must contain at least one uppercase letter')}, 'url': 'https://errors.pydantic.dev/2.5/v/value_error'}]
2025-07-13 16:22:45 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 422 in 0.0174s
2025-07-13 16:22:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:45 - Request: GET /
2025-07-13 16:22:48 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:59 - Response: 200 in 0.0096s
2025-07-13 16:22:57 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:87 - Shutting down Medical Preparation Platform API
2025-07-13 16:22:59 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:22:59 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:22:59 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x1104eff50> can't render element of type UUID
2025-07-13 16:23:26 - app.core.logging - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/core/logging.py:112 - Logging configured with level: INFO
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:76 - Starting Medical Preparation Platform API
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:77 - Environment: development
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:78 - Debug mode: False
2025-07-13 16:23:26 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:88 - Creating database tables...
2025-07-13 16:23:26 - app.main - ERROR - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:92 - Error creating database tables: (in table 'users', column 'id'): Compiler <sqlalchemy.dialects.sqlite.base.SQLiteTypeCompiler object at 0x10a608490> can't render element of type UUID
2025-07-13 16:23:41 - app.main - INFO - /Users/<USER>/Desktop/MedPrep/backend/app/main.py:97 - Shutting down Medical Preparation Platform API
