#!/usr/bin/env python3
"""
Test all services with authentication.
"""
import requests
import json

def test_all_services():
    """Test all services with proper authentication."""
    base_url = "http://localhost:8000"
    
    print("🚀 TESTING ALL SERVICES WITH AUTH")
    print("=" * 60)
    
    # Step 1: Login
    print("1. 🔐 Getting authentication token...")
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print("   ✅ Authentication successful")
        else:
            print(f"   ❌ Authentication failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Authentication error: {e}")
        return False
    
    # Step 2: Test health endpoint with auth
    print("\n2. 🏥 Testing system health...")
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{base_url}/api/v1/admin/health", headers=headers)
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ System health: {health_data.get('status', 'unknown')}")
            
            # Show detailed health info
            services = health_data.get('services', {})
            for service_name, service_info in services.items():
                status = service_info.get('status', 'unknown')
                response_time = service_info.get('response_time_ms', 'N/A')
                icon = "✅" if status == 'connected' else "❌"
                print(f"   {icon} {service_name}: {status} ({response_time}ms)")
            
            return health_data.get('status') == 'healthy'
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False

def main():
    success = test_all_services()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL SERVICES ARE HEALTHY!")
        print("✅ PostgreSQL: Connected")
        print("✅ Qdrant: Connected") 
        print("✅ Backend: Healthy")
        print("\nThe admin dashboard should now show 'healthy' status.")
    else:
        print("⚠️  SYSTEM HEALTH ISSUES DETECTED")
        print("Check the service status above for details.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
