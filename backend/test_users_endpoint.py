#!/usr/bin/env python3
"""
Test the users endpoint specifically to verify the trailing slash issue is resolved.
"""
import requests
import json


def test_users_endpoint():
    """Test both /users and /users/ endpoints."""
    base_url = "http://localhost:8000"
    
    print("🔐 Getting admin token...")
    
    # Login as admin user
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Origin": "http://localhost:3001"
    }
    
    print("\n🔍 Testing Users Endpoints:")
    print("=" * 50)
    
    # Test 1: /users (without trailing slash) - should redirect
    print("\n1. Testing /api/v1/users (without trailing slash)")
    try:
        response = requests.get(f"{base_url}/api/v1/users?skip=0&limit=10", headers=headers, allow_redirects=False)
        print(f"   Status: {response.status_code}")
        if response.status_code == 307:
            print("   ✅ Correctly returns 307 redirect")
            redirect_url = response.headers.get('Location', 'No location header')
            print(f"   Redirect to: {redirect_url}")
        else:
            print(f"   ❌ Expected 307, got {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: /users/ (with trailing slash) - should work directly
    print("\n2. Testing /api/v1/users/ (with trailing slash)")
    try:
        response = requests.get(f"{base_url}/api/v1/users/?skip=0&limit=10", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Success - returns user data")
            print(f"   Users returned: {len(data.get('users', []))}")
            print(f"   Total users: {data.get('total', 'N/A')}")
            if data.get('users'):
                sample_user = data['users'][0]
                print(f"   Sample user: {sample_user.get('email', 'N/A')} ({sample_user.get('role', 'N/A')})")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: /users with automatic redirect following
    print("\n3. Testing /api/v1/users with redirect following (like frontend)")
    try:
        response = requests.get(f"{base_url}/api/v1/users?skip=0&limit=10", headers=headers, allow_redirects=True)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Success - redirect followed automatically")
            print(f"   Users returned: {len(data.get('users', []))}")
            print(f"   Final URL: {response.url}")
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 FRONTEND FIX STATUS")
    print("=" * 60)
    print("✅ Backend API is working correctly")
    print("✅ /users/ endpoint returns real user data")
    print("✅ Frontend adminService.ts updated to use /users/")
    print("\n💡 Frontend should now show real data instead of dummy data!")
    print("   Refresh the admin dashboard to see the changes.")


if __name__ == "__main__":
    test_users_endpoint()
