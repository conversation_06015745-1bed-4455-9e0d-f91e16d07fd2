#!/usr/bin/env python3
"""
Comprehensive authentication system test script.
Tests login, signup, token validation, and protected endpoints.
"""
import requests
import json
import time


def test_authentication_system():
    """Test the complete authentication system."""
    base_url = "http://localhost:8000"
    
    print("🔐 Testing Authentication System")
    print("=" * 50)
    
    # Test 1: User Registration
    print("\n1. Testing User Registration...")
    register_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "full_name": "Test Auth User"
    }

    try:
        register_response = requests.post(f"{base_url}/api/v1/auth/signup", json=register_data)
        if register_response.status_code in [200, 201]:
            print("✅ User registration successful")
        elif register_response.status_code == 400 and "already registered" in register_response.text:
            print("✅ User already exists (expected)")
        else:
            print(f"❌ Registration failed: {register_response.status_code} - {register_response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False
    
    # Test 2: User Login
    print("\n2. Testing User Login...")
    login_data = {
        "username": "<EMAIL>",  # Use existing test user
        "password": "testpass123"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data["access_token"]
            print("✅ Login successful")
            print(f"   Token type: {token_data.get('token_type', 'bearer')}")
            print(f"   Token length: {len(access_token)} characters")
        else:
            print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test 3: Protected Endpoint Access
    print("\n3. Testing Protected Endpoint Access...")
    headers = {"Authorization": f"Bearer {access_token}"}
    
    try:
        # Test user profile endpoint
        profile_response = requests.get(f"{base_url}/api/v1/users/me", headers=headers)
        if profile_response.status_code == 200:
            user_data = profile_response.json()
            print("✅ Protected endpoint access successful")
            print(f"   User: {user_data.get('email', 'Unknown')}")
            print(f"   Role: {user_data.get('role', 'Unknown')}")
        else:
            print(f"❌ Protected endpoint failed: {profile_response.status_code} - {profile_response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False
    
    # Test 4: Invalid Token
    print("\n4. Testing Invalid Token Handling...")
    invalid_headers = {"Authorization": "Bearer invalid_token_here"}
    
    try:
        invalid_response = requests.get(f"{base_url}/api/v1/users/me", headers=invalid_headers)
        if invalid_response.status_code == 401:
            print("✅ Invalid token properly rejected")
        else:
            print(f"❌ Invalid token not rejected: {invalid_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Invalid token test error: {e}")
        return False
    
    # Test 5: No Token
    print("\n5. Testing No Token Handling...")
    
    try:
        no_token_response = requests.get(f"{base_url}/api/v1/users/me")
        if no_token_response.status_code == 401:
            print("✅ Missing token properly rejected")
        else:
            print(f"❌ Missing token not rejected: {no_token_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ No token test error: {e}")
        return False
    
    # Test 6: Admin Endpoints (if user is admin)
    print("\n6. Testing Admin Endpoint Access...")
    
    try:
        admin_response = requests.get(f"{base_url}/api/v1/admin/stats", headers=headers)
        if admin_response.status_code == 200:
            print("✅ Admin endpoint access successful")
            stats = admin_response.json()
            print(f"   Total users: {stats.get('total_users', 'Unknown')}")
            print(f"   Total books: {stats.get('total_books', 'Unknown')}")
        elif admin_response.status_code == 403:
            print("✅ Admin endpoint properly restricted (user not admin)")
        else:
            print(f"❌ Admin endpoint unexpected response: {admin_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Admin endpoint test error: {e}")
        return False
    
    # Test 7: Topic Search with Authentication
    print("\n7. Testing Topic Search with Authentication...")
    
    search_data = {
        "query": "diabetes mellitus diagnosis",
        "limit": 5,
        "score_threshold": 0.6
    }
    
    try:
        search_response = requests.post(
            f"{base_url}/api/v1/search/topic",
            json=search_data,
            headers=headers
        )
        if search_response.status_code == 200:
            search_results = search_response.json()
            print("✅ Authenticated topic search successful")
            print(f"   Query: {search_results.get('query', 'Unknown')}")
            print(f"   Total results: {search_results.get('total_results', 0)}")
            print(f"   Sections: {list(search_results.get('grouped_results', {}).keys())}")
        else:
            print(f"❌ Topic search failed: {search_response.status_code} - {search_response.text}")
            return False
    except Exception as e:
        print(f"❌ Topic search error: {e}")
        return False
    
    # Test 8: CORS Headers
    print("\n8. Testing CORS Headers...")
    
    try:
        cors_response = requests.options(f"{base_url}/api/v1/auth/login")
        cors_headers = cors_response.headers
        
        if "Access-Control-Allow-Origin" in cors_headers:
            print("✅ CORS headers present")
            print(f"   Allow-Origin: {cors_headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"   Allow-Methods: {cors_headers.get('Access-Control-Allow-Methods', 'Not set')}")
        else:
            print("❌ CORS headers missing")
            return False
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All authentication tests passed!")
    return True


def test_frontend_login():
    """Test login from frontend perspective."""
    print("\n🌐 Testing Frontend Login Flow")
    print("=" * 50)

    # Simulate frontend login request using FormData like the frontend does
    form_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "http://localhost:3001"
    }

    try:
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            data=form_data,
            headers=headers
        )
        
        if response.status_code == 200:
            print("✅ Frontend login simulation successful")
            token_data = response.json()
            print(f"   Access token received: {len(token_data['access_token'])} characters")
            return True
        else:
            print(f"❌ Frontend login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Frontend login error: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Comprehensive Authentication Tests\n")
    
    # Test backend authentication
    backend_success = test_authentication_system()
    
    # Test frontend login simulation
    frontend_success = test_frontend_login()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Backend Authentication: {'✅ PASS' if backend_success else '❌ FAIL'}")
    print(f"Frontend Login Flow: {'✅ PASS' if frontend_success else '❌ FAIL'}")
    
    if backend_success and frontend_success:
        print("\n🎉 All authentication tests passed!")
        print("\n💡 Next steps:")
        print("   1. Frontend should now be able to login successfully")
        print("   2. CORS issues should be resolved")
        print("   3. All protected endpoints should work with valid tokens")
    else:
        print("\n❌ Some authentication tests failed.")
        print("   Please check the error messages above.")
    
    exit(0 if backend_success and frontend_success else 1)
