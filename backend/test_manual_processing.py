#!/usr/bin/env python3
"""
Test manual processing trigger for pending books.
"""
import requests
import json
import time


def test_manual_processing():
    """Test triggering manual processing for pending books."""
    base_url = "http://localhost:8000"
    
    print("🚀 TESTING MANUAL BOOK PROCESSING")
    print("=" * 60)
    
    # Step 1: Login
    print("1. 🔐 Getting authentication token...")
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print("   ✅ Authentication successful")
        else:
            print(f"   ❌ Authentication failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Authentication error: {e}")
        return False
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Origin": "http://localhost:3001"
    }
    
    # Step 2: Get pending books
    print("\n2. 📚 Getting pending books...")
    try:
        response = requests.get(f"{base_url}/api/v1/admin/books", headers=headers)
        if response.status_code == 200:
            books = response.json()
            pending_books = [book for book in books if book.get('processing_status') == 'pending']
            
            print(f"   ✅ Found {len(pending_books)} pending books:")
            for book in pending_books:
                print(f"      - {book['title']} (ID: {book['id']})")
                
            if not pending_books:
                print("   ℹ️  No pending books found")
                return True
                
        else:
            print(f"   ❌ Failed to get books: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error getting books: {e}")
        return False
    
    # Step 3: Process each pending book
    print("\n3. ⚙️ Starting processing for pending books...")
    for book in pending_books:
        book_id = book['id']
        book_title = book['title']
        
        print(f"\n   📖 Processing: {book_title}")
        try:
            response = requests.post(
                f"{base_url}/api/v1/admin/books/{book_id}/process",
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Processing started: {result.get('message', 'Success')}")
                
                # Check processing status
                print(f"   🔍 Checking processing status...")
                status_response = requests.get(
                    f"{base_url}/api/v1/admin/books/{book_id}/status",
                    headers=headers
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"      Status: {status_data.get('processing_status', 'unknown')}")
                    print(f"      Progress: {status_data.get('indexing_progress', 0):.1f}%")
                    print(f"      Chunks: {status_data.get('indexed_chunks', 0)}/{status_data.get('total_chunks', 0)}")
                
            else:
                print(f"   ❌ Processing failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error processing book: {e}")
    
    # Step 4: Monitor processing progress
    print("\n4. 📊 Monitoring processing progress...")
    for i in range(10):  # Check for up to 10 iterations
        print(f"\n   Check #{i+1}:")
        try:
            response = requests.get(f"{base_url}/api/v1/admin/processing-queue", headers=headers)
            if response.status_code == 200:
                queue_data = response.json()
                summary = queue_data.get('summary', {})
                queue_items = queue_data.get('processing_queue', [])
                
                print(f"      Processing: {summary.get('processing', 0)}")
                print(f"      Pending: {summary.get('pending', 0)}")
                print(f"      Completed: {summary.get('completed', 0)}")
                print(f"      Failed: {summary.get('failed', 0)}")
                
                if queue_items:
                    for item in queue_items:
                        print(f"      - {item['title']}: {item['status']} ({item['progress']}%)")
                
                # If no processing or pending items, we're done
                if summary.get('processing', 0) == 0 and summary.get('pending', 0) == 0:
                    print("   ✅ All processing completed!")
                    break
                    
        except Exception as e:
            print(f"      ❌ Error checking queue: {e}")
        
        if i < 9:  # Don't sleep on the last iteration
            print("      ⏳ Waiting 5 seconds...")
            time.sleep(5)
    
    print("\n" + "=" * 60)
    print("🎯 PROCESSING RESULTS")
    print("=" * 60)
    
    # Final status check
    try:
        response = requests.get(f"{base_url}/api/v1/admin/processing-queue", headers=headers)
        if response.status_code == 200:
            queue_data = response.json()
            summary = queue_data.get('summary', {})
            
            print(f"📊 Final Status:")
            print(f"   ✅ Completed: {summary.get('completed', 0)}")
            print(f"   ⚙️  Processing: {summary.get('processing', 0)}")
            print(f"   ⏳ Pending: {summary.get('pending', 0)}")
            print(f"   ❌ Failed: {summary.get('failed', 0)}")
            
            if summary.get('completed', 0) > 0:
                print(f"\n🎉 SUCCESS! Books have been processed and are ready for search!")
                print(f"📝 You can now:")
                print(f"   1. Search for content in the processed books")
                print(f"   2. Ask questions about the medical content")
                print(f"   3. Use all platform features with the new content")
            elif summary.get('processing', 0) > 0:
                print(f"\n⏳ PROCESSING IN PROGRESS...")
                print(f"📝 Processing is still running. Check back in a few minutes.")
            elif summary.get('pending', 0) > 0:
                print(f"\n⚠️  BOOKS STILL PENDING")
                print(f"📝 Some books are still pending. You may need to trigger processing manually.")
            else:
                print(f"\n❌ NO BOOKS FOUND")
                print(f"📝 No books found in any processing state.")
                
    except Exception as e:
        print(f"❌ Error getting final status: {e}")
    
    return True


if __name__ == "__main__":
    test_manual_processing()
