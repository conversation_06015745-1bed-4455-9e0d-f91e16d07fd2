# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: collections.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ollections.proto\x12\x06qdrant\"\xfa\x01\n\x0cVectorParams\x12\x0c\n\x04size\x18\x01 \x01(\x04\x12\"\n\x08\x64istance\x18\x02 \x01(\x0e\x32\x10.qdrant.Distance\x12\x30\n\x0bhnsw_config\x18\x03 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12<\n\x13quantization_config\x18\x04 \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\x01\x88\x01\x01\x12\x14\n\x07on_disk\x18\x05 \x01(\x08H\x02\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\x16\n\x14_quantization_configB\n\n\x08_on_disk\"\xd0\x01\n\x10VectorParamsDiff\x12\x30\n\x0bhnsw_config\x18\x01 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12@\n\x13quantization_config\x18\x02 \x01(\x0b\x32\x1e.qdrant.QuantizationConfigDiffH\x01\x88\x01\x01\x12\x14\n\x07on_disk\x18\x03 \x01(\x08H\x02\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\x16\n\x14_quantization_configB\n\n\x08_on_disk\"\x82\x01\n\x0fVectorParamsMap\x12-\n\x03map\x18\x01 \x03(\x0b\x32 .qdrant.VectorParamsMap.MapEntry\x1a@\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.qdrant.VectorParams:\x02\x38\x01\"\x8e\x01\n\x13VectorParamsDiffMap\x12\x31\n\x03map\x18\x01 \x03(\x0b\x32$.qdrant.VectorParamsDiffMap.MapEntry\x1a\x44\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.qdrant.VectorParamsDiff:\x02\x38\x01\"p\n\rVectorsConfig\x12&\n\x06params\x18\x01 \x01(\x0b\x32\x14.qdrant.VectorParamsH\x00\x12-\n\nparams_map\x18\x02 \x01(\x0b\x32\x17.qdrant.VectorParamsMapH\x00\x42\x08\n\x06\x63onfig\"|\n\x11VectorsConfigDiff\x12*\n\x06params\x18\x01 \x01(\x0b\x32\x18.qdrant.VectorParamsDiffH\x00\x12\x31\n\nparams_map\x18\x02 \x01(\x0b\x32\x1b.qdrant.VectorParamsDiffMapH\x00\x42\x08\n\x06\x63onfig\"M\n\x12SparseVectorParams\x12-\n\x05index\x18\x01 \x01(\x0b\x32\x19.qdrant.SparseIndexConfigH\x00\x88\x01\x01\x42\x08\n\x06_index\"\x8e\x01\n\x12SparseVectorConfig\x12\x30\n\x03map\x18\x01 \x03(\x0b\x32#.qdrant.SparseVectorConfig.MapEntry\x1a\x46\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\x05value\x18\x02 \x01(\x0b\x32\x1a.qdrant.SparseVectorParams:\x02\x38\x01\"3\n\x18GetCollectionInfoRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"\x18\n\x16ListCollectionsRequest\"%\n\x15\x43ollectionDescription\x12\x0c\n\x04name\x18\x01 \x01(\t\"Q\n\x19GetCollectionInfoResponse\x12&\n\x06result\x18\x01 \x01(\x0b\x32\x16.qdrant.CollectionInfo\x12\x0c\n\x04time\x18\x02 \x01(\x01\"[\n\x17ListCollectionsResponse\x12\x32\n\x0b\x63ollections\x18\x01 \x03(\x0b\x32\x1d.qdrant.CollectionDescription\x12\x0c\n\x04time\x18\x02 \x01(\x01\",\n\x0fOptimizerStatus\x12\n\n\x02ok\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\x90\x02\n\x0eHnswConfigDiff\x12\x0e\n\x01m\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x19\n\x0c\x65\x66_construct\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12 \n\x13\x66ull_scan_threshold\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12!\n\x14max_indexing_threads\x18\x04 \x01(\x04H\x03\x88\x01\x01\x12\x14\n\x07on_disk\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x16\n\tpayload_m\x18\x06 \x01(\x04H\x05\x88\x01\x01\x42\x04\n\x02_mB\x0f\n\r_ef_constructB\x16\n\x14_full_scan_thresholdB\x17\n\x15_max_indexing_threadsB\n\n\x08_on_diskB\x0c\n\n_payload_m\"o\n\x11SparseIndexConfig\x12 \n\x13\x66ull_scan_threshold\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x14\n\x07on_disk\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x16\n\x14_full_scan_thresholdB\n\n\x08_on_disk\"y\n\rWalConfigDiff\x12\x1c\n\x0fwal_capacity_mb\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x1f\n\x12wal_segments_ahead\x18\x02 \x01(\x04H\x01\x88\x01\x01\x42\x12\n\x10_wal_capacity_mbB\x15\n\x13_wal_segments_ahead\"\xec\x03\n\x14OptimizersConfigDiff\x12\x1e\n\x11\x64\x65leted_threshold\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12%\n\x18vacuum_min_vector_number\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12#\n\x16\x64\x65\x66\x61ult_segment_number\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12\x1d\n\x10max_segment_size\x18\x04 \x01(\x04H\x03\x88\x01\x01\x12\x1d\n\x10memmap_threshold\x18\x05 \x01(\x04H\x04\x88\x01\x01\x12\x1f\n\x12indexing_threshold\x18\x06 \x01(\x04H\x05\x88\x01\x01\x12\x1f\n\x12\x66lush_interval_sec\x18\x07 \x01(\x04H\x06\x88\x01\x01\x12%\n\x18max_optimization_threads\x18\x08 \x01(\x04H\x07\x88\x01\x01\x42\x14\n\x12_deleted_thresholdB\x1b\n\x19_vacuum_min_vector_numberB\x19\n\x17_default_segment_numberB\x13\n\x11_max_segment_sizeB\x13\n\x11_memmap_thresholdB\x15\n\x13_indexing_thresholdB\x15\n\x13_flush_interval_secB\x1b\n\x19_max_optimization_threads\"\x88\x01\n\x12ScalarQuantization\x12&\n\x04type\x18\x01 \x01(\x0e\x32\x18.qdrant.QuantizationType\x12\x15\n\x08quantile\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x17\n\nalways_ram\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x0b\n\t_quantileB\r\n\x0b_always_ram\"l\n\x13ProductQuantization\x12-\n\x0b\x63ompression\x18\x01 \x01(\x0e\x32\x18.qdrant.CompressionRatio\x12\x17\n\nalways_ram\x18\x02 \x01(\x08H\x00\x88\x01\x01\x42\r\n\x0b_always_ram\"<\n\x12\x42inaryQuantization\x12\x17\n\nalways_ram\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\r\n\x0b_always_ram\"\xb0\x01\n\x12QuantizationConfig\x12,\n\x06scalar\x18\x01 \x01(\x0b\x32\x1a.qdrant.ScalarQuantizationH\x00\x12.\n\x07product\x18\x02 \x01(\x0b\x32\x1b.qdrant.ProductQuantizationH\x00\x12,\n\x06\x62inary\x18\x03 \x01(\x0b\x32\x1a.qdrant.BinaryQuantizationH\x00\x42\x0e\n\x0cquantization\"\n\n\x08\x44isabled\"\xda\x01\n\x16QuantizationConfigDiff\x12,\n\x06scalar\x18\x01 \x01(\x0b\x32\x1a.qdrant.ScalarQuantizationH\x00\x12.\n\x07product\x18\x02 \x01(\x0b\x32\x1b.qdrant.ProductQuantizationH\x00\x12$\n\x08\x64isabled\x18\x03 \x01(\x0b\x32\x10.qdrant.DisabledH\x00\x12,\n\x06\x62inary\x18\x04 \x01(\x0b\x32\x1a.qdrant.BinaryQuantizationH\x00\x42\x0e\n\x0cquantization\"\x85\x07\n\x10\x43reateCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x30\n\x0bhnsw_config\x18\x04 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12.\n\nwal_config\x18\x05 \x01(\x0b\x32\x15.qdrant.WalConfigDiffH\x01\x88\x01\x01\x12<\n\x11optimizers_config\x18\x06 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiffH\x02\x88\x01\x01\x12\x19\n\x0cshard_number\x18\x07 \x01(\rH\x03\x88\x01\x01\x12\x1c\n\x0fon_disk_payload\x18\x08 \x01(\x08H\x04\x88\x01\x01\x12\x14\n\x07timeout\x18\t \x01(\x04H\x05\x88\x01\x01\x12\x32\n\x0evectors_config\x18\n \x01(\x0b\x32\x15.qdrant.VectorsConfigH\x06\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x0b \x01(\rH\x07\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x0c \x01(\rH\x08\x88\x01\x01\x12!\n\x14init_from_collection\x18\r \x01(\tH\t\x88\x01\x01\x12<\n\x13quantization_config\x18\x0e \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\n\x88\x01\x01\x12\x34\n\x0fsharding_method\x18\x0f \x01(\x0e\x32\x16.qdrant.ShardingMethodH\x0b\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\x10 \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x0c\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\r\n\x0b_wal_configB\x14\n\x12_optimizers_configB\x0f\n\r_shard_numberB\x12\n\x10_on_disk_payloadB\n\n\x08_timeoutB\x11\n\x0f_vectors_configB\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x17\n\x15_init_from_collectionB\x16\n\x14_quantization_configB\x12\n\x10_sharding_methodB\x18\n\x16_sparse_vectors_configJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"\xa0\x04\n\x10UpdateCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12<\n\x11optimizers_config\x18\x02 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiffH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x31\n\x06params\x18\x04 \x01(\x0b\x32\x1c.qdrant.CollectionParamsDiffH\x02\x88\x01\x01\x12\x30\n\x0bhnsw_config\x18\x05 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x03\x88\x01\x01\x12\x36\n\x0evectors_config\x18\x06 \x01(\x0b\x32\x19.qdrant.VectorsConfigDiffH\x04\x88\x01\x01\x12@\n\x13quantization_config\x18\x07 \x01(\x0b\x32\x1e.qdrant.QuantizationConfigDiffH\x05\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\x08 \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x06\x88\x01\x01\x42\x14\n\x12_optimizers_configB\n\n\x08_timeoutB\t\n\x07_paramsB\x0e\n\x0c_hnsw_configB\x11\n\x0f_vectors_configB\x16\n\x14_quantization_configB\x18\n\x16_sparse_vectors_config\"M\n\x10\x44\x65leteCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x14\n\x07timeout\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\";\n\x1b\x43ollectionOperationResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\xee\x03\n\x10\x43ollectionParams\x12\x14\n\x0cshard_number\x18\x03 \x01(\r\x12\x17\n\x0fon_disk_payload\x18\x04 \x01(\x08\x12\x32\n\x0evectors_config\x18\x05 \x01(\x0b\x32\x15.qdrant.VectorsConfigH\x00\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x06 \x01(\rH\x01\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x07 \x01(\rH\x02\x88\x01\x01\x12 \n\x13read_fan_out_factor\x18\x08 \x01(\rH\x03\x88\x01\x01\x12\x34\n\x0fsharding_method\x18\t \x01(\x0e\x32\x16.qdrant.ShardingMethodH\x04\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\n \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x05\x88\x01\x01\x42\x11\n\x0f_vectors_configB\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x16\n\x14_read_fan_out_factorB\x12\n\x10_sharding_methodB\x18\n\x16_sparse_vectors_configJ\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03\"\xfe\x01\n\x14\x43ollectionParamsDiff\x12\x1f\n\x12replication_factor\x18\x01 \x01(\rH\x00\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x02 \x01(\rH\x01\x88\x01\x01\x12\x1c\n\x0fon_disk_payload\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12 \n\x13read_fan_out_factor\x18\x04 \x01(\rH\x03\x88\x01\x01\x42\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x12\n\x10_on_disk_payloadB\x16\n\x14_read_fan_out_factor\"\xa2\x02\n\x10\x43ollectionConfig\x12(\n\x06params\x18\x01 \x01(\x0b\x32\x18.qdrant.CollectionParams\x12+\n\x0bhnsw_config\x18\x02 \x01(\x0b\x32\x16.qdrant.HnswConfigDiff\x12\x36\n\x10optimizer_config\x18\x03 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiff\x12)\n\nwal_config\x18\x04 \x01(\x0b\x32\x15.qdrant.WalConfigDiff\x12<\n\x13quantization_config\x18\x05 \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\x00\x88\x01\x01\x42\x16\n\x14_quantization_config\"\xbd\x01\n\x0fTextIndexParams\x12(\n\ttokenizer\x18\x01 \x01(\x0e\x32\x15.qdrant.TokenizerType\x12\x16\n\tlowercase\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1a\n\rmin_token_len\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x1a\n\rmax_token_len\x18\x04 \x01(\x04H\x02\x88\x01\x01\x42\x0c\n\n_lowercaseB\x10\n\x0e_min_token_lenB\x10\n\x0e_max_token_len\"Z\n\x12PayloadIndexParams\x12\x34\n\x11text_index_params\x18\x01 \x01(\x0b\x32\x17.qdrant.TextIndexParamsH\x00\x42\x0e\n\x0cindex_params\"\x9d\x01\n\x11PayloadSchemaInfo\x12,\n\tdata_type\x18\x01 \x01(\x0e\x32\x19.qdrant.PayloadSchemaType\x12/\n\x06params\x18\x02 \x01(\x0b\x32\x1a.qdrant.PayloadIndexParamsH\x00\x88\x01\x01\x12\x13\n\x06points\x18\x03 \x01(\x04H\x01\x88\x01\x01\x42\t\n\x07_paramsB\t\n\x07_points\"\xe7\x03\n\x0e\x43ollectionInfo\x12(\n\x06status\x18\x01 \x01(\x0e\x32\x18.qdrant.CollectionStatus\x12\x31\n\x10optimizer_status\x18\x02 \x01(\x0b\x32\x17.qdrant.OptimizerStatus\x12\x1a\n\rvectors_count\x18\x03 \x01(\x04H\x00\x88\x01\x01\x12\x16\n\x0esegments_count\x18\x04 \x01(\x04\x12(\n\x06\x63onfig\x18\x07 \x01(\x0b\x32\x18.qdrant.CollectionConfig\x12\x41\n\x0epayload_schema\x18\x08 \x03(\x0b\x32).qdrant.CollectionInfo.PayloadSchemaEntry\x12\x19\n\x0cpoints_count\x18\t \x01(\x04H\x01\x88\x01\x01\x12\"\n\x15indexed_vectors_count\x18\n \x01(\x04H\x02\x88\x01\x01\x1aO\n\x12PayloadSchemaEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.qdrant.PayloadSchemaInfo:\x02\x38\x01\x42\x10\n\x0e_vectors_countB\x0f\n\r_points_countB\x18\n\x16_indexed_vectors_countJ\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07\"[\n\rChangeAliases\x12(\n\x07\x61\x63tions\x18\x01 \x03(\x0b\x32\x17.qdrant.AliasOperations\x12\x14\n\x07timeout\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"\xa2\x01\n\x0f\x41liasOperations\x12+\n\x0c\x63reate_alias\x18\x01 \x01(\x0b\x32\x13.qdrant.CreateAliasH\x00\x12+\n\x0crename_alias\x18\x02 \x01(\x0b\x32\x13.qdrant.RenameAliasH\x00\x12+\n\x0c\x64\x65lete_alias\x18\x03 \x01(\x0b\x32\x13.qdrant.DeleteAliasH\x00\x42\x08\n\x06\x61\x63tion\":\n\x0b\x43reateAlias\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x12\n\nalias_name\x18\x02 \x01(\t\"=\n\x0bRenameAlias\x12\x16\n\x0eold_alias_name\x18\x01 \x01(\t\x12\x16\n\x0enew_alias_name\x18\x02 \x01(\t\"!\n\x0b\x44\x65leteAlias\x12\x12\n\nalias_name\x18\x01 \x01(\t\"\x14\n\x12ListAliasesRequest\"7\n\x1cListCollectionAliasesRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"?\n\x10\x41liasDescription\x12\x12\n\nalias_name\x18\x01 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x02 \x01(\t\"N\n\x13ListAliasesResponse\x12)\n\x07\x61liases\x18\x01 \x03(\x0b\x32\x18.qdrant.AliasDescription\x12\x0c\n\x04time\x18\x02 \x01(\x01\"7\n\x1c\x43ollectionClusterInfoRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"6\n\x08ShardKey\x12\x11\n\x07keyword\x18\x01 \x01(\tH\x00\x12\x10\n\x06number\x18\x02 \x01(\x04H\x00\x42\x05\n\x03key\"\x95\x01\n\x0eLocalShardInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x14\n\x0cpoints_count\x18\x02 \x01(\x04\x12#\n\x05state\x18\x03 \x01(\x0e\x32\x14.qdrant.ReplicaState\x12(\n\tshard_key\x18\x04 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x00\x88\x01\x01\x42\x0c\n\n_shard_key\"\x91\x01\n\x0fRemoteShardInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0f\n\x07peer_id\x18\x02 \x01(\x04\x12#\n\x05state\x18\x03 \x01(\x0e\x32\x14.qdrant.ReplicaState\x12(\n\tshard_key\x18\x04 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x00\x88\x01\x01\x42\x0c\n\n_shard_key\"M\n\x11ShardTransferInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0c\n\x04\x66rom\x18\x02 \x01(\x04\x12\n\n\x02to\x18\x03 \x01(\x04\x12\x0c\n\x04sync\x18\x04 \x01(\x08\"\xd7\x01\n\x1d\x43ollectionClusterInfoResponse\x12\x0f\n\x07peer_id\x18\x01 \x01(\x04\x12\x13\n\x0bshard_count\x18\x02 \x01(\x04\x12,\n\x0clocal_shards\x18\x03 \x03(\x0b\x32\x16.qdrant.LocalShardInfo\x12.\n\rremote_shards\x18\x04 \x03(\x0b\x32\x17.qdrant.RemoteShardInfo\x12\x32\n\x0fshard_transfers\x18\x05 \x03(\x0b\x32\x19.qdrant.ShardTransferInfo\"\x84\x01\n\tMoveShard\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x14\n\x0c\x66rom_peer_id\x18\x02 \x01(\x04\x12\x12\n\nto_peer_id\x18\x03 \x01(\x04\x12\x30\n\x06method\x18\x04 \x01(\x0e\x32\x1b.qdrant.ShardTransferMethodH\x00\x88\x01\x01\x42\t\n\x07_method\",\n\x07Replica\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0f\n\x07peer_id\x18\x02 \x01(\x04\"\xae\x01\n\x0e\x43reateShardKey\x12#\n\tshard_key\x18\x01 \x01(\x0b\x32\x10.qdrant.ShardKey\x12\x1a\n\rshards_number\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x03 \x01(\rH\x01\x88\x01\x01\x12\x11\n\tplacement\x18\x04 \x03(\x04\x42\x10\n\x0e_shards_numberB\x15\n\x13_replication_factor\"5\n\x0e\x44\x65leteShardKey\x12#\n\tshard_key\x18\x01 \x01(\x0b\x32\x10.qdrant.ShardKey\"\x82\x03\n#UpdateCollectionClusterSetupRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\nmove_shard\x18\x02 \x01(\x0b\x32\x11.qdrant.MoveShardH\x00\x12,\n\x0freplicate_shard\x18\x03 \x01(\x0b\x32\x11.qdrant.MoveShardH\x00\x12+\n\x0e\x61\x62ort_transfer\x18\x04 \x01(\x0b\x32\x11.qdrant.MoveShardH\x00\x12\'\n\x0c\x64rop_replica\x18\x05 \x01(\x0b\x32\x0f.qdrant.ReplicaH\x00\x12\x32\n\x10\x63reate_shard_key\x18\x07 \x01(\x0b\x32\x16.qdrant.CreateShardKeyH\x00\x12\x32\n\x10\x64\x65lete_shard_key\x18\x08 \x01(\x0b\x32\x16.qdrant.DeleteShardKeyH\x00\x12\x14\n\x07timeout\x18\x06 \x01(\x04H\x01\x88\x01\x01\x42\x0b\n\toperationB\n\n\x08_timeout\"6\n$UpdateCollectionClusterSetupResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"{\n\x15\x43reateShardKeyRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x07request\x18\x02 \x01(\x0b\x32\x16.qdrant.CreateShardKey\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"{\n\x15\x44\x65leteShardKeyRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x07request\x18\x02 \x01(\x0b\x32\x16.qdrant.DeleteShardKey\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"(\n\x16\x43reateShardKeyResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x16\x44\x65leteShardKeyResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08*O\n\x08\x44istance\x12\x13\n\x0fUnknownDistance\x10\x00\x12\n\n\x06\x43osine\x10\x01\x12\n\n\x06\x45uclid\x10\x02\x12\x07\n\x03\x44ot\x10\x03\x12\r\n\tManhattan\x10\x04*O\n\x10\x43ollectionStatus\x12\x1b\n\x17UnknownCollectionStatus\x10\x00\x12\t\n\x05Green\x10\x01\x12\n\n\x06Yellow\x10\x02\x12\x07\n\x03Red\x10\x03*f\n\x11PayloadSchemaType\x12\x0f\n\x0bUnknownType\x10\x00\x12\x0b\n\x07Keyword\x10\x01\x12\x0b\n\x07Integer\x10\x02\x12\t\n\x05\x46loat\x10\x03\x12\x07\n\x03Geo\x10\x04\x12\x08\n\x04Text\x10\x05\x12\x08\n\x04\x42ool\x10\x06*5\n\x10QuantizationType\x12\x17\n\x13UnknownQuantization\x10\x00\x12\x08\n\x04Int8\x10\x01*=\n\x10\x43ompressionRatio\x12\x06\n\x02x4\x10\x00\x12\x06\n\x02x8\x10\x01\x12\x07\n\x03x16\x10\x02\x12\x07\n\x03x32\x10\x03\x12\x07\n\x03x64\x10\x04*&\n\x0eShardingMethod\x12\x08\n\x04\x41uto\x10\x00\x12\n\n\x06\x43ustom\x10\x01*T\n\rTokenizerType\x12\x0b\n\x07Unknown\x10\x00\x12\n\n\x06Prefix\x10\x01\x12\x0e\n\nWhitespace\x10\x02\x12\x08\n\x04Word\x10\x03\x12\x10\n\x0cMultilingual\x10\x04*f\n\x0cReplicaState\x12\n\n\x06\x41\x63tive\x10\x00\x12\x08\n\x04\x44\x65\x61\x64\x10\x01\x12\x0b\n\x07Partial\x10\x02\x12\x10\n\x0cInitializing\x10\x03\x12\x0c\n\x08Listener\x10\x04\x12\x13\n\x0fPartialSnapshot\x10\x05*6\n\x13ShardTransferMethod\x12\x11\n\rStreamRecords\x10\x00\x12\x0c\n\x08Snapshot\x10\x01\x42\x15\xaa\x02\x12Qdrant.Client.Grpcb\x06proto3')

_DISTANCE = DESCRIPTOR.enum_types_by_name['Distance']
Distance = enum_type_wrapper.EnumTypeWrapper(_DISTANCE)
_COLLECTIONSTATUS = DESCRIPTOR.enum_types_by_name['CollectionStatus']
CollectionStatus = enum_type_wrapper.EnumTypeWrapper(_COLLECTIONSTATUS)
_PAYLOADSCHEMATYPE = DESCRIPTOR.enum_types_by_name['PayloadSchemaType']
PayloadSchemaType = enum_type_wrapper.EnumTypeWrapper(_PAYLOADSCHEMATYPE)
_QUANTIZATIONTYPE = DESCRIPTOR.enum_types_by_name['QuantizationType']
QuantizationType = enum_type_wrapper.EnumTypeWrapper(_QUANTIZATIONTYPE)
_COMPRESSIONRATIO = DESCRIPTOR.enum_types_by_name['CompressionRatio']
CompressionRatio = enum_type_wrapper.EnumTypeWrapper(_COMPRESSIONRATIO)
_SHARDINGMETHOD = DESCRIPTOR.enum_types_by_name['ShardingMethod']
ShardingMethod = enum_type_wrapper.EnumTypeWrapper(_SHARDINGMETHOD)
_TOKENIZERTYPE = DESCRIPTOR.enum_types_by_name['TokenizerType']
TokenizerType = enum_type_wrapper.EnumTypeWrapper(_TOKENIZERTYPE)
_REPLICASTATE = DESCRIPTOR.enum_types_by_name['ReplicaState']
ReplicaState = enum_type_wrapper.EnumTypeWrapper(_REPLICASTATE)
_SHARDTRANSFERMETHOD = DESCRIPTOR.enum_types_by_name['ShardTransferMethod']
ShardTransferMethod = enum_type_wrapper.EnumTypeWrapper(_SHARDTRANSFERMETHOD)
UnknownDistance = 0
Cosine = 1
Euclid = 2
Dot = 3
Manhattan = 4
UnknownCollectionStatus = 0
Green = 1
Yellow = 2
Red = 3
UnknownType = 0
Keyword = 1
Integer = 2
Float = 3
Geo = 4
Text = 5
Bool = 6
UnknownQuantization = 0
Int8 = 1
x4 = 0
x8 = 1
x16 = 2
x32 = 3
x64 = 4
Auto = 0
Custom = 1
Unknown = 0
Prefix = 1
Whitespace = 2
Word = 3
Multilingual = 4
Active = 0
Dead = 1
Partial = 2
Initializing = 3
Listener = 4
PartialSnapshot = 5
StreamRecords = 0
Snapshot = 1


_VECTORPARAMS = DESCRIPTOR.message_types_by_name['VectorParams']
_VECTORPARAMSDIFF = DESCRIPTOR.message_types_by_name['VectorParamsDiff']
_VECTORPARAMSMAP = DESCRIPTOR.message_types_by_name['VectorParamsMap']
_VECTORPARAMSMAP_MAPENTRY = _VECTORPARAMSMAP.nested_types_by_name['MapEntry']
_VECTORPARAMSDIFFMAP = DESCRIPTOR.message_types_by_name['VectorParamsDiffMap']
_VECTORPARAMSDIFFMAP_MAPENTRY = _VECTORPARAMSDIFFMAP.nested_types_by_name['MapEntry']
_VECTORSCONFIG = DESCRIPTOR.message_types_by_name['VectorsConfig']
_VECTORSCONFIGDIFF = DESCRIPTOR.message_types_by_name['VectorsConfigDiff']
_SPARSEVECTORPARAMS = DESCRIPTOR.message_types_by_name['SparseVectorParams']
_SPARSEVECTORCONFIG = DESCRIPTOR.message_types_by_name['SparseVectorConfig']
_SPARSEVECTORCONFIG_MAPENTRY = _SPARSEVECTORCONFIG.nested_types_by_name['MapEntry']
_GETCOLLECTIONINFOREQUEST = DESCRIPTOR.message_types_by_name['GetCollectionInfoRequest']
_LISTCOLLECTIONSREQUEST = DESCRIPTOR.message_types_by_name['ListCollectionsRequest']
_COLLECTIONDESCRIPTION = DESCRIPTOR.message_types_by_name['CollectionDescription']
_GETCOLLECTIONINFORESPONSE = DESCRIPTOR.message_types_by_name['GetCollectionInfoResponse']
_LISTCOLLECTIONSRESPONSE = DESCRIPTOR.message_types_by_name['ListCollectionsResponse']
_OPTIMIZERSTATUS = DESCRIPTOR.message_types_by_name['OptimizerStatus']
_HNSWCONFIGDIFF = DESCRIPTOR.message_types_by_name['HnswConfigDiff']
_SPARSEINDEXCONFIG = DESCRIPTOR.message_types_by_name['SparseIndexConfig']
_WALCONFIGDIFF = DESCRIPTOR.message_types_by_name['WalConfigDiff']
_OPTIMIZERSCONFIGDIFF = DESCRIPTOR.message_types_by_name['OptimizersConfigDiff']
_SCALARQUANTIZATION = DESCRIPTOR.message_types_by_name['ScalarQuantization']
_PRODUCTQUANTIZATION = DESCRIPTOR.message_types_by_name['ProductQuantization']
_BINARYQUANTIZATION = DESCRIPTOR.message_types_by_name['BinaryQuantization']
_QUANTIZATIONCONFIG = DESCRIPTOR.message_types_by_name['QuantizationConfig']
_DISABLED = DESCRIPTOR.message_types_by_name['Disabled']
_QUANTIZATIONCONFIGDIFF = DESCRIPTOR.message_types_by_name['QuantizationConfigDiff']
_CREATECOLLECTION = DESCRIPTOR.message_types_by_name['CreateCollection']
_UPDATECOLLECTION = DESCRIPTOR.message_types_by_name['UpdateCollection']
_DELETECOLLECTION = DESCRIPTOR.message_types_by_name['DeleteCollection']
_COLLECTIONOPERATIONRESPONSE = DESCRIPTOR.message_types_by_name['CollectionOperationResponse']
_COLLECTIONPARAMS = DESCRIPTOR.message_types_by_name['CollectionParams']
_COLLECTIONPARAMSDIFF = DESCRIPTOR.message_types_by_name['CollectionParamsDiff']
_COLLECTIONCONFIG = DESCRIPTOR.message_types_by_name['CollectionConfig']
_TEXTINDEXPARAMS = DESCRIPTOR.message_types_by_name['TextIndexParams']
_PAYLOADINDEXPARAMS = DESCRIPTOR.message_types_by_name['PayloadIndexParams']
_PAYLOADSCHEMAINFO = DESCRIPTOR.message_types_by_name['PayloadSchemaInfo']
_COLLECTIONINFO = DESCRIPTOR.message_types_by_name['CollectionInfo']
_COLLECTIONINFO_PAYLOADSCHEMAENTRY = _COLLECTIONINFO.nested_types_by_name['PayloadSchemaEntry']
_CHANGEALIASES = DESCRIPTOR.message_types_by_name['ChangeAliases']
_ALIASOPERATIONS = DESCRIPTOR.message_types_by_name['AliasOperations']
_CREATEALIAS = DESCRIPTOR.message_types_by_name['CreateAlias']
_RENAMEALIAS = DESCRIPTOR.message_types_by_name['RenameAlias']
_DELETEALIAS = DESCRIPTOR.message_types_by_name['DeleteAlias']
_LISTALIASESREQUEST = DESCRIPTOR.message_types_by_name['ListAliasesRequest']
_LISTCOLLECTIONALIASESREQUEST = DESCRIPTOR.message_types_by_name['ListCollectionAliasesRequest']
_ALIASDESCRIPTION = DESCRIPTOR.message_types_by_name['AliasDescription']
_LISTALIASESRESPONSE = DESCRIPTOR.message_types_by_name['ListAliasesResponse']
_COLLECTIONCLUSTERINFOREQUEST = DESCRIPTOR.message_types_by_name['CollectionClusterInfoRequest']
_SHARDKEY = DESCRIPTOR.message_types_by_name['ShardKey']
_LOCALSHARDINFO = DESCRIPTOR.message_types_by_name['LocalShardInfo']
_REMOTESHARDINFO = DESCRIPTOR.message_types_by_name['RemoteShardInfo']
_SHARDTRANSFERINFO = DESCRIPTOR.message_types_by_name['ShardTransferInfo']
_COLLECTIONCLUSTERINFORESPONSE = DESCRIPTOR.message_types_by_name['CollectionClusterInfoResponse']
_MOVESHARD = DESCRIPTOR.message_types_by_name['MoveShard']
_REPLICA = DESCRIPTOR.message_types_by_name['Replica']
_CREATESHARDKEY = DESCRIPTOR.message_types_by_name['CreateShardKey']
_DELETESHARDKEY = DESCRIPTOR.message_types_by_name['DeleteShardKey']
_UPDATECOLLECTIONCLUSTERSETUPREQUEST = DESCRIPTOR.message_types_by_name['UpdateCollectionClusterSetupRequest']
_UPDATECOLLECTIONCLUSTERSETUPRESPONSE = DESCRIPTOR.message_types_by_name['UpdateCollectionClusterSetupResponse']
_CREATESHARDKEYREQUEST = DESCRIPTOR.message_types_by_name['CreateShardKeyRequest']
_DELETESHARDKEYREQUEST = DESCRIPTOR.message_types_by_name['DeleteShardKeyRequest']
_CREATESHARDKEYRESPONSE = DESCRIPTOR.message_types_by_name['CreateShardKeyResponse']
_DELETESHARDKEYRESPONSE = DESCRIPTOR.message_types_by_name['DeleteShardKeyResponse']
VectorParams = _reflection.GeneratedProtocolMessageType('VectorParams', (_message.Message,), {
  'DESCRIPTOR' : _VECTORPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParams)
  })
_sym_db.RegisterMessage(VectorParams)

VectorParamsDiff = _reflection.GeneratedProtocolMessageType('VectorParamsDiff', (_message.Message,), {
  'DESCRIPTOR' : _VECTORPARAMSDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiff)
  })
_sym_db.RegisterMessage(VectorParamsDiff)

VectorParamsMap = _reflection.GeneratedProtocolMessageType('VectorParamsMap', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _VECTORPARAMSMAP_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.VectorParamsMap.MapEntry)
    })
  ,
  'DESCRIPTOR' : _VECTORPARAMSMAP,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsMap)
  })
_sym_db.RegisterMessage(VectorParamsMap)
_sym_db.RegisterMessage(VectorParamsMap.MapEntry)

VectorParamsDiffMap = _reflection.GeneratedProtocolMessageType('VectorParamsDiffMap', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _VECTORPARAMSDIFFMAP_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiffMap.MapEntry)
    })
  ,
  'DESCRIPTOR' : _VECTORPARAMSDIFFMAP,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiffMap)
  })
_sym_db.RegisterMessage(VectorParamsDiffMap)
_sym_db.RegisterMessage(VectorParamsDiffMap.MapEntry)

VectorsConfig = _reflection.GeneratedProtocolMessageType('VectorsConfig', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsConfig)
  })
_sym_db.RegisterMessage(VectorsConfig)

VectorsConfigDiff = _reflection.GeneratedProtocolMessageType('VectorsConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsConfigDiff)
  })
_sym_db.RegisterMessage(VectorsConfigDiff)

SparseVectorParams = _reflection.GeneratedProtocolMessageType('SparseVectorParams', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEVECTORPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseVectorParams)
  })
_sym_db.RegisterMessage(SparseVectorParams)

SparseVectorConfig = _reflection.GeneratedProtocolMessageType('SparseVectorConfig', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _SPARSEVECTORCONFIG_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.SparseVectorConfig.MapEntry)
    })
  ,
  'DESCRIPTOR' : _SPARSEVECTORCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseVectorConfig)
  })
_sym_db.RegisterMessage(SparseVectorConfig)
_sym_db.RegisterMessage(SparseVectorConfig.MapEntry)

GetCollectionInfoRequest = _reflection.GeneratedProtocolMessageType('GetCollectionInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCOLLECTIONINFOREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetCollectionInfoRequest)
  })
_sym_db.RegisterMessage(GetCollectionInfoRequest)

ListCollectionsRequest = _reflection.GeneratedProtocolMessageType('ListCollectionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONSREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionsRequest)
  })
_sym_db.RegisterMessage(ListCollectionsRequest)

CollectionDescription = _reflection.GeneratedProtocolMessageType('CollectionDescription', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONDESCRIPTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionDescription)
  })
_sym_db.RegisterMessage(CollectionDescription)

GetCollectionInfoResponse = _reflection.GeneratedProtocolMessageType('GetCollectionInfoResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCOLLECTIONINFORESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetCollectionInfoResponse)
  })
_sym_db.RegisterMessage(GetCollectionInfoResponse)

ListCollectionsResponse = _reflection.GeneratedProtocolMessageType('ListCollectionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONSRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionsResponse)
  })
_sym_db.RegisterMessage(ListCollectionsResponse)

OptimizerStatus = _reflection.GeneratedProtocolMessageType('OptimizerStatus', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZERSTATUS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OptimizerStatus)
  })
_sym_db.RegisterMessage(OptimizerStatus)

HnswConfigDiff = _reflection.GeneratedProtocolMessageType('HnswConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _HNSWCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.HnswConfigDiff)
  })
_sym_db.RegisterMessage(HnswConfigDiff)

SparseIndexConfig = _reflection.GeneratedProtocolMessageType('SparseIndexConfig', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEINDEXCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseIndexConfig)
  })
_sym_db.RegisterMessage(SparseIndexConfig)

WalConfigDiff = _reflection.GeneratedProtocolMessageType('WalConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _WALCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WalConfigDiff)
  })
_sym_db.RegisterMessage(WalConfigDiff)

OptimizersConfigDiff = _reflection.GeneratedProtocolMessageType('OptimizersConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZERSCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OptimizersConfigDiff)
  })
_sym_db.RegisterMessage(OptimizersConfigDiff)

ScalarQuantization = _reflection.GeneratedProtocolMessageType('ScalarQuantization', (_message.Message,), {
  'DESCRIPTOR' : _SCALARQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ScalarQuantization)
  })
_sym_db.RegisterMessage(ScalarQuantization)

ProductQuantization = _reflection.GeneratedProtocolMessageType('ProductQuantization', (_message.Message,), {
  'DESCRIPTOR' : _PRODUCTQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ProductQuantization)
  })
_sym_db.RegisterMessage(ProductQuantization)

BinaryQuantization = _reflection.GeneratedProtocolMessageType('BinaryQuantization', (_message.Message,), {
  'DESCRIPTOR' : _BINARYQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.BinaryQuantization)
  })
_sym_db.RegisterMessage(BinaryQuantization)

QuantizationConfig = _reflection.GeneratedProtocolMessageType('QuantizationConfig', (_message.Message,), {
  'DESCRIPTOR' : _QUANTIZATIONCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QuantizationConfig)
  })
_sym_db.RegisterMessage(QuantizationConfig)

Disabled = _reflection.GeneratedProtocolMessageType('Disabled', (_message.Message,), {
  'DESCRIPTOR' : _DISABLED,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Disabled)
  })
_sym_db.RegisterMessage(Disabled)

QuantizationConfigDiff = _reflection.GeneratedProtocolMessageType('QuantizationConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _QUANTIZATIONCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QuantizationConfigDiff)
  })
_sym_db.RegisterMessage(QuantizationConfigDiff)

CreateCollection = _reflection.GeneratedProtocolMessageType('CreateCollection', (_message.Message,), {
  'DESCRIPTOR' : _CREATECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateCollection)
  })
_sym_db.RegisterMessage(CreateCollection)

UpdateCollection = _reflection.GeneratedProtocolMessageType('UpdateCollection', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollection)
  })
_sym_db.RegisterMessage(UpdateCollection)

DeleteCollection = _reflection.GeneratedProtocolMessageType('DeleteCollection', (_message.Message,), {
  'DESCRIPTOR' : _DELETECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteCollection)
  })
_sym_db.RegisterMessage(DeleteCollection)

CollectionOperationResponse = _reflection.GeneratedProtocolMessageType('CollectionOperationResponse', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONOPERATIONRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionOperationResponse)
  })
_sym_db.RegisterMessage(CollectionOperationResponse)

CollectionParams = _reflection.GeneratedProtocolMessageType('CollectionParams', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionParams)
  })
_sym_db.RegisterMessage(CollectionParams)

CollectionParamsDiff = _reflection.GeneratedProtocolMessageType('CollectionParamsDiff', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONPARAMSDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionParamsDiff)
  })
_sym_db.RegisterMessage(CollectionParamsDiff)

CollectionConfig = _reflection.GeneratedProtocolMessageType('CollectionConfig', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionConfig)
  })
_sym_db.RegisterMessage(CollectionConfig)

TextIndexParams = _reflection.GeneratedProtocolMessageType('TextIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _TEXTINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.TextIndexParams)
  })
_sym_db.RegisterMessage(TextIndexParams)

PayloadIndexParams = _reflection.GeneratedProtocolMessageType('PayloadIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadIndexParams)
  })
_sym_db.RegisterMessage(PayloadIndexParams)

PayloadSchemaInfo = _reflection.GeneratedProtocolMessageType('PayloadSchemaInfo', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADSCHEMAINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadSchemaInfo)
  })
_sym_db.RegisterMessage(PayloadSchemaInfo)

CollectionInfo = _reflection.GeneratedProtocolMessageType('CollectionInfo', (_message.Message,), {

  'PayloadSchemaEntry' : _reflection.GeneratedProtocolMessageType('PayloadSchemaEntry', (_message.Message,), {
    'DESCRIPTOR' : _COLLECTIONINFO_PAYLOADSCHEMAENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.CollectionInfo.PayloadSchemaEntry)
    })
  ,
  'DESCRIPTOR' : _COLLECTIONINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionInfo)
  })
_sym_db.RegisterMessage(CollectionInfo)
_sym_db.RegisterMessage(CollectionInfo.PayloadSchemaEntry)

ChangeAliases = _reflection.GeneratedProtocolMessageType('ChangeAliases', (_message.Message,), {
  'DESCRIPTOR' : _CHANGEALIASES,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ChangeAliases)
  })
_sym_db.RegisterMessage(ChangeAliases)

AliasOperations = _reflection.GeneratedProtocolMessageType('AliasOperations', (_message.Message,), {
  'DESCRIPTOR' : _ALIASOPERATIONS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.AliasOperations)
  })
_sym_db.RegisterMessage(AliasOperations)

CreateAlias = _reflection.GeneratedProtocolMessageType('CreateAlias', (_message.Message,), {
  'DESCRIPTOR' : _CREATEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateAlias)
  })
_sym_db.RegisterMessage(CreateAlias)

RenameAlias = _reflection.GeneratedProtocolMessageType('RenameAlias', (_message.Message,), {
  'DESCRIPTOR' : _RENAMEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RenameAlias)
  })
_sym_db.RegisterMessage(RenameAlias)

DeleteAlias = _reflection.GeneratedProtocolMessageType('DeleteAlias', (_message.Message,), {
  'DESCRIPTOR' : _DELETEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteAlias)
  })
_sym_db.RegisterMessage(DeleteAlias)

ListAliasesRequest = _reflection.GeneratedProtocolMessageType('ListAliasesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTALIASESREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListAliasesRequest)
  })
_sym_db.RegisterMessage(ListAliasesRequest)

ListCollectionAliasesRequest = _reflection.GeneratedProtocolMessageType('ListCollectionAliasesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONALIASESREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionAliasesRequest)
  })
_sym_db.RegisterMessage(ListCollectionAliasesRequest)

AliasDescription = _reflection.GeneratedProtocolMessageType('AliasDescription', (_message.Message,), {
  'DESCRIPTOR' : _ALIASDESCRIPTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.AliasDescription)
  })
_sym_db.RegisterMessage(AliasDescription)

ListAliasesResponse = _reflection.GeneratedProtocolMessageType('ListAliasesResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTALIASESRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListAliasesResponse)
  })
_sym_db.RegisterMessage(ListAliasesResponse)

CollectionClusterInfoRequest = _reflection.GeneratedProtocolMessageType('CollectionClusterInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCLUSTERINFOREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionClusterInfoRequest)
  })
_sym_db.RegisterMessage(CollectionClusterInfoRequest)

ShardKey = _reflection.GeneratedProtocolMessageType('ShardKey', (_message.Message,), {
  'DESCRIPTOR' : _SHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ShardKey)
  })
_sym_db.RegisterMessage(ShardKey)

LocalShardInfo = _reflection.GeneratedProtocolMessageType('LocalShardInfo', (_message.Message,), {
  'DESCRIPTOR' : _LOCALSHARDINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.LocalShardInfo)
  })
_sym_db.RegisterMessage(LocalShardInfo)

RemoteShardInfo = _reflection.GeneratedProtocolMessageType('RemoteShardInfo', (_message.Message,), {
  'DESCRIPTOR' : _REMOTESHARDINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RemoteShardInfo)
  })
_sym_db.RegisterMessage(RemoteShardInfo)

ShardTransferInfo = _reflection.GeneratedProtocolMessageType('ShardTransferInfo', (_message.Message,), {
  'DESCRIPTOR' : _SHARDTRANSFERINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ShardTransferInfo)
  })
_sym_db.RegisterMessage(ShardTransferInfo)

CollectionClusterInfoResponse = _reflection.GeneratedProtocolMessageType('CollectionClusterInfoResponse', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCLUSTERINFORESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionClusterInfoResponse)
  })
_sym_db.RegisterMessage(CollectionClusterInfoResponse)

MoveShard = _reflection.GeneratedProtocolMessageType('MoveShard', (_message.Message,), {
  'DESCRIPTOR' : _MOVESHARD,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MoveShard)
  })
_sym_db.RegisterMessage(MoveShard)

Replica = _reflection.GeneratedProtocolMessageType('Replica', (_message.Message,), {
  'DESCRIPTOR' : _REPLICA,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Replica)
  })
_sym_db.RegisterMessage(Replica)

CreateShardKey = _reflection.GeneratedProtocolMessageType('CreateShardKey', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKey)
  })
_sym_db.RegisterMessage(CreateShardKey)

DeleteShardKey = _reflection.GeneratedProtocolMessageType('DeleteShardKey', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKey)
  })
_sym_db.RegisterMessage(DeleteShardKey)

UpdateCollectionClusterSetupRequest = _reflection.GeneratedProtocolMessageType('UpdateCollectionClusterSetupRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTIONCLUSTERSETUPREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollectionClusterSetupRequest)
  })
_sym_db.RegisterMessage(UpdateCollectionClusterSetupRequest)

UpdateCollectionClusterSetupResponse = _reflection.GeneratedProtocolMessageType('UpdateCollectionClusterSetupResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTIONCLUSTERSETUPRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollectionClusterSetupResponse)
  })
_sym_db.RegisterMessage(UpdateCollectionClusterSetupResponse)

CreateShardKeyRequest = _reflection.GeneratedProtocolMessageType('CreateShardKeyRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEYREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKeyRequest)
  })
_sym_db.RegisterMessage(CreateShardKeyRequest)

DeleteShardKeyRequest = _reflection.GeneratedProtocolMessageType('DeleteShardKeyRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEYREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKeyRequest)
  })
_sym_db.RegisterMessage(DeleteShardKeyRequest)

CreateShardKeyResponse = _reflection.GeneratedProtocolMessageType('CreateShardKeyResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEYRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKeyResponse)
  })
_sym_db.RegisterMessage(CreateShardKeyResponse)

DeleteShardKeyResponse = _reflection.GeneratedProtocolMessageType('DeleteShardKeyResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEYRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKeyResponse)
  })
_sym_db.RegisterMessage(DeleteShardKeyResponse)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\252\002\022Qdrant.Client.Grpc'
  _VECTORPARAMSMAP_MAPENTRY._options = None
  _VECTORPARAMSMAP_MAPENTRY._serialized_options = b'8\001'
  _VECTORPARAMSDIFFMAP_MAPENTRY._options = None
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_options = b'8\001'
  _SPARSEVECTORCONFIG_MAPENTRY._options = None
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_options = b'8\001'
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._options = None
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_options = b'8\001'
  _DISTANCE._serialized_start=9418
  _DISTANCE._serialized_end=9497
  _COLLECTIONSTATUS._serialized_start=9499
  _COLLECTIONSTATUS._serialized_end=9578
  _PAYLOADSCHEMATYPE._serialized_start=9580
  _PAYLOADSCHEMATYPE._serialized_end=9682
  _QUANTIZATIONTYPE._serialized_start=9684
  _QUANTIZATIONTYPE._serialized_end=9737
  _COMPRESSIONRATIO._serialized_start=9739
  _COMPRESSIONRATIO._serialized_end=9800
  _SHARDINGMETHOD._serialized_start=9802
  _SHARDINGMETHOD._serialized_end=9840
  _TOKENIZERTYPE._serialized_start=9842
  _TOKENIZERTYPE._serialized_end=9926
  _REPLICASTATE._serialized_start=9928
  _REPLICASTATE._serialized_end=10030
  _SHARDTRANSFERMETHOD._serialized_start=10032
  _SHARDTRANSFERMETHOD._serialized_end=10086
  _VECTORPARAMS._serialized_start=30
  _VECTORPARAMS._serialized_end=280
  _VECTORPARAMSDIFF._serialized_start=283
  _VECTORPARAMSDIFF._serialized_end=491
  _VECTORPARAMSMAP._serialized_start=494
  _VECTORPARAMSMAP._serialized_end=624
  _VECTORPARAMSMAP_MAPENTRY._serialized_start=560
  _VECTORPARAMSMAP_MAPENTRY._serialized_end=624
  _VECTORPARAMSDIFFMAP._serialized_start=627
  _VECTORPARAMSDIFFMAP._serialized_end=769
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_start=701
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_end=769
  _VECTORSCONFIG._serialized_start=771
  _VECTORSCONFIG._serialized_end=883
  _VECTORSCONFIGDIFF._serialized_start=885
  _VECTORSCONFIGDIFF._serialized_end=1009
  _SPARSEVECTORPARAMS._serialized_start=1011
  _SPARSEVECTORPARAMS._serialized_end=1088
  _SPARSEVECTORCONFIG._serialized_start=1091
  _SPARSEVECTORCONFIG._serialized_end=1233
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_start=1163
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_end=1233
  _GETCOLLECTIONINFOREQUEST._serialized_start=1235
  _GETCOLLECTIONINFOREQUEST._serialized_end=1286
  _LISTCOLLECTIONSREQUEST._serialized_start=1288
  _LISTCOLLECTIONSREQUEST._serialized_end=1312
  _COLLECTIONDESCRIPTION._serialized_start=1314
  _COLLECTIONDESCRIPTION._serialized_end=1351
  _GETCOLLECTIONINFORESPONSE._serialized_start=1353
  _GETCOLLECTIONINFORESPONSE._serialized_end=1434
  _LISTCOLLECTIONSRESPONSE._serialized_start=1436
  _LISTCOLLECTIONSRESPONSE._serialized_end=1527
  _OPTIMIZERSTATUS._serialized_start=1529
  _OPTIMIZERSTATUS._serialized_end=1573
  _HNSWCONFIGDIFF._serialized_start=1576
  _HNSWCONFIGDIFF._serialized_end=1848
  _SPARSEINDEXCONFIG._serialized_start=1850
  _SPARSEINDEXCONFIG._serialized_end=1961
  _WALCONFIGDIFF._serialized_start=1963
  _WALCONFIGDIFF._serialized_end=2084
  _OPTIMIZERSCONFIGDIFF._serialized_start=2087
  _OPTIMIZERSCONFIGDIFF._serialized_end=2579
  _SCALARQUANTIZATION._serialized_start=2582
  _SCALARQUANTIZATION._serialized_end=2718
  _PRODUCTQUANTIZATION._serialized_start=2720
  _PRODUCTQUANTIZATION._serialized_end=2828
  _BINARYQUANTIZATION._serialized_start=2830
  _BINARYQUANTIZATION._serialized_end=2890
  _QUANTIZATIONCONFIG._serialized_start=2893
  _QUANTIZATIONCONFIG._serialized_end=3069
  _DISABLED._serialized_start=3071
  _DISABLED._serialized_end=3081
  _QUANTIZATIONCONFIGDIFF._serialized_start=3084
  _QUANTIZATIONCONFIGDIFF._serialized_end=3302
  _CREATECOLLECTION._serialized_start=3305
  _CREATECOLLECTION._serialized_end=4206
  _UPDATECOLLECTION._serialized_start=4209
  _UPDATECOLLECTION._serialized_end=4753
  _DELETECOLLECTION._serialized_start=4755
  _DELETECOLLECTION._serialized_end=4832
  _COLLECTIONOPERATIONRESPONSE._serialized_start=4834
  _COLLECTIONOPERATIONRESPONSE._serialized_end=4893
  _COLLECTIONPARAMS._serialized_start=4896
  _COLLECTIONPARAMS._serialized_end=5390
  _COLLECTIONPARAMSDIFF._serialized_start=5393
  _COLLECTIONPARAMSDIFF._serialized_end=5647
  _COLLECTIONCONFIG._serialized_start=5650
  _COLLECTIONCONFIG._serialized_end=5940
  _TEXTINDEXPARAMS._serialized_start=5943
  _TEXTINDEXPARAMS._serialized_end=6132
  _PAYLOADINDEXPARAMS._serialized_start=6134
  _PAYLOADINDEXPARAMS._serialized_end=6224
  _PAYLOADSCHEMAINFO._serialized_start=6227
  _PAYLOADSCHEMAINFO._serialized_end=6384
  _COLLECTIONINFO._serialized_start=6387
  _COLLECTIONINFO._serialized_end=6874
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_start=6722
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_end=6801
  _CHANGEALIASES._serialized_start=6876
  _CHANGEALIASES._serialized_end=6967
  _ALIASOPERATIONS._serialized_start=6970
  _ALIASOPERATIONS._serialized_end=7132
  _CREATEALIAS._serialized_start=7134
  _CREATEALIAS._serialized_end=7192
  _RENAMEALIAS._serialized_start=7194
  _RENAMEALIAS._serialized_end=7255
  _DELETEALIAS._serialized_start=7257
  _DELETEALIAS._serialized_end=7290
  _LISTALIASESREQUEST._serialized_start=7292
  _LISTALIASESREQUEST._serialized_end=7312
  _LISTCOLLECTIONALIASESREQUEST._serialized_start=7314
  _LISTCOLLECTIONALIASESREQUEST._serialized_end=7369
  _ALIASDESCRIPTION._serialized_start=7371
  _ALIASDESCRIPTION._serialized_end=7434
  _LISTALIASESRESPONSE._serialized_start=7436
  _LISTALIASESRESPONSE._serialized_end=7514
  _COLLECTIONCLUSTERINFOREQUEST._serialized_start=7516
  _COLLECTIONCLUSTERINFOREQUEST._serialized_end=7571
  _SHARDKEY._serialized_start=7573
  _SHARDKEY._serialized_end=7627
  _LOCALSHARDINFO._serialized_start=7630
  _LOCALSHARDINFO._serialized_end=7779
  _REMOTESHARDINFO._serialized_start=7782
  _REMOTESHARDINFO._serialized_end=7927
  _SHARDTRANSFERINFO._serialized_start=7929
  _SHARDTRANSFERINFO._serialized_end=8006
  _COLLECTIONCLUSTERINFORESPONSE._serialized_start=8009
  _COLLECTIONCLUSTERINFORESPONSE._serialized_end=8224
  _MOVESHARD._serialized_start=8227
  _MOVESHARD._serialized_end=8359
  _REPLICA._serialized_start=8361
  _REPLICA._serialized_end=8405
  _CREATESHARDKEY._serialized_start=8408
  _CREATESHARDKEY._serialized_end=8582
  _DELETESHARDKEY._serialized_start=8584
  _DELETESHARDKEY._serialized_end=8637
  _UPDATECOLLECTIONCLUSTERSETUPREQUEST._serialized_start=8640
  _UPDATECOLLECTIONCLUSTERSETUPREQUEST._serialized_end=9026
  _UPDATECOLLECTIONCLUSTERSETUPRESPONSE._serialized_start=9028
  _UPDATECOLLECTIONCLUSTERSETUPRESPONSE._serialized_end=9082
  _CREATESHARDKEYREQUEST._serialized_start=9084
  _CREATESHARDKEYREQUEST._serialized_end=9207
  _DELETESHARDKEYREQUEST._serialized_start=9209
  _DELETESHARDKEYREQUEST._serialized_end=9332
  _CREATESHARDKEYRESPONSE._serialized_start=9334
  _CREATESHARDKEYRESPONSE._serialized_end=9374
  _DELETESHARDKEYRESPONSE._serialized_start=9376
  _DELETESHARDKEYRESPONSE._serialized_end=9416
# @@protoc_insertion_point(module_scope)
