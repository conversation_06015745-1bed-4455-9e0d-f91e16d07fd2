syntax = "proto3";

package qdrant;
option csharp_namespace = "Qdrant.Client.Grpc";

import "google/protobuf/timestamp.proto";

service Snapshots {
  /*
 Create collection snapshot
  */
  rpc Create (CreateSnapshotRequest) returns (CreateSnapshotResponse) {}
  /*
  List collection snapshots
   */
  rpc List (ListSnapshotsRequest) returns (ListSnapshotsResponse) {}
  /*
  Delete collection snapshot
   */
  rpc Delete (DeleteSnapshotRequest) returns (DeleteSnapshotResponse) {}
  /*
  Create full storage snapshot
  */
  rpc CreateFull (CreateFullSnapshotRequest) returns (CreateSnapshotResponse) {}
  /*
  List full storage snapshots
   */
  rpc ListFull (ListFullSnapshotsRequest) returns (ListSnapshotsResponse) {}
  /*
  Delete full storage snapshot
   */
  rpc DeleteFull (DeleteFullSnapshotRequest) returns (DeleteSnapshotResponse) {}
}

message CreateFullSnapshotRequest {}

message ListFullSnapshotsRequest {}

message DeleteFullSnapshotRequest {
  string snapshot_name = 1; // Name of the full snapshot
}

message CreateSnapshotRequest {
  string collection_name = 1; // Name of the collection
}

message ListSnapshotsRequest {
  string collection_name = 1; // Name of the collection
}

message DeleteSnapshotRequest {
  string collection_name = 1; // Name of the collection
  string snapshot_name = 2; // Name of the collection snapshot
}

message SnapshotDescription {
  string name = 1; // Name of the snapshot
  google.protobuf.Timestamp creation_time = 2; // Creation time of the snapshot
  int64 size = 3; // Size of the snapshot in bytes
}

message CreateSnapshotResponse {
  SnapshotDescription snapshot_description = 1;
  double time = 2; // Time spent to process
}

message ListSnapshotsResponse {
  repeated SnapshotDescription snapshot_descriptions = 1;
  double time = 2; // Time spent to process
}

message DeleteSnapshotResponse {
  double time = 1; // Time spent to process
}
