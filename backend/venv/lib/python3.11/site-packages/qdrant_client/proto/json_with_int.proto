// Fork of the google.protobuf.Value with explicit support for integer values

syntax = "proto3";

package qdrant;
option csharp_namespace = "Qdrant.Client.Grpc";

// `Struct` represents a structured data value, consisting of fields
// which map to dynamically typed values. In some languages, `Struct`
// might be supported by a native representation. For example, in
// scripting languages like JS a struct is represented as an
// object. The details of that representation are described together
// with the proto support for the language.
//
// The JSON representation for `Struct` is a JSON object.
message Struct {
  // Unordered map of dynamically typed values.
  map<string, Value> fields = 1;
}

// `Value` represents a dynamically typed value which can be either
// null, a number, a string, a boolean, a recursive struct value, or a
// list of values. A producer of value is expected to set one of those
// variants, absence of any variant indicates an error.
//
// The JSON representation for `Value` is a JSON value.
message Value {
  // The kind of value.
  oneof kind {
    // Represents a null value.
    NullValue null_value = 1;
    // Represents a double value.
    double double_value = 2;
    // Represents an integer value
    int64 integer_value = 3;
    // Represents a string value.
    string string_value = 4;
    // Represents a boolean value.
    bool bool_value = 5;
    // Represents a structured value.
    Struct struct_value = 6;
    // Represents a repeated `Value`.
    ListValue list_value = 7;
  }
}

// `NullValue` is a singleton enumeration to represent the null value for the
// `Value` type union.
//
//  The JSON representation for `NullValue` is JSON `null`.
enum NullValue {
  // Null value.
  NULL_VALUE = 0;
}

// `ListValue` is a wrapper around a repeated field of values.
//
// The JSON representation for `ListValue` is a JSON array.
message ListValue {
  // Repeated field of dynamically typed values.
  repeated Value values = 1;
}
