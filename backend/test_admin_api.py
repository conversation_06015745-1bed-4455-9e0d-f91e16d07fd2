#!/usr/bin/env python3
"""
Test admin API endpoints to debug frontend issues.
"""
import requests
import json


def test_admin_endpoints():
    """Test all admin endpoints that the frontend uses."""
    base_url = "http://localhost:8000"
    
    print("🔐 Getting admin token...")
    
    # Login as admin user
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data["access_token"]
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test endpoints that frontend uses
    endpoints = [
        ("GET", "/api/v1/admin/stats", "Admin Stats"),
        ("GET", "/api/v1/users/?skip=0&limit=10", "Users List"),
        ("GET", "/api/v1/books/", "Books List"),
        ("GET", "/api/v1/search/analytics", "Search Analytics"),
        ("GET", "/api/v1/admin/health", "System Health"),
        ("GET", "/api/v1/search/service-info", "Service Info"),
    ]
    
    for method, endpoint, name in endpoints:
        print(f"\n🔍 Testing {name}: {method} {endpoint}")
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", headers=headers)
            else:
                response = requests.post(f"{base_url}{endpoint}", headers=headers)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Success")
                
                # Show key data points
                if "stats" in endpoint:
                    print(f"      Total users: {data.get('total_users', 'N/A')}")
                    print(f"      Total books: {data.get('total_books', 'N/A')}")
                elif "users" in endpoint:
                    if isinstance(data, dict) and 'users' in data:
                        print(f"      Users returned: {len(data['users'])}")
                        print(f"      Total: {data.get('total', 'N/A')}")
                        if data['users']:
                            print(f"      Sample user: {data['users'][0].get('email', 'N/A')}")
                    elif isinstance(data, list):
                        print(f"      Users returned: {len(data)}")
                        if data:
                            print(f"      Sample user: {data[0].get('email', 'N/A')}")
                elif "books" in endpoint:
                    if isinstance(data, list):
                        print(f"      Books returned: {len(data)}")
                        if data:
                            print(f"      Sample book: {data[0].get('title', 'N/A')}")
                elif "analytics" in endpoint:
                    print(f"      Total searches: {data.get('total_searches', 'N/A')}")
                elif "health" in endpoint:
                    print(f"      Status: {data.get('status', 'N/A')}")
                elif "service-info" in endpoint:
                    print(f"      Status: {data.get('status', 'N/A')}")
                    print(f"      Service type: {data.get('service_info', {}).get('service_type', 'N/A')}")
            else:
                print(f"   ❌ Failed: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 FRONTEND DEBUGGING TIPS")
    print("=" * 60)
    print("1. Check browser console for CORS errors")
    print("2. Verify frontend is making requests to correct endpoints")
    print("3. Check if authentication token is being sent properly")
    print("4. Look for network errors in browser dev tools")


if __name__ == "__main__":
    test_admin_endpoints()
