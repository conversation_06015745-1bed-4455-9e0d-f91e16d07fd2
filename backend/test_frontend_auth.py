#!/usr/bin/env python3
"""
Test frontend authentication and API calls to debug the admin dashboard issues.
"""
import requests
import json


def test_frontend_auth_flow():
    """Test the complete frontend authentication flow."""
    base_url = "http://localhost:8000"
    
    print("🔐 Testing Frontend Authentication Flow")
    print("=" * 60)
    
    # Step 1: Login (simulate frontend login)
    print("\n1. Testing Login (Frontend Style)")
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    # Use form data like the frontend does
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "http://localhost:3001"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data, headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print("   ✅ Login successful")
            print(f"   Token: {access_token[:20]}...")
        else:
            print(f"   ❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return None
    
    # Step 2: Test API calls with token (simulate frontend API calls)
    print("\n2. Testing API Calls with Token")
    api_headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Origin": "http://localhost:3001"
    }
    
    # Test admin stats
    print("\n   2a. Admin Stats (/api/v1/admin/stats)")
    try:
        response = requests.get(f"{base_url}/api/v1/admin/stats", headers=api_headers)
        print(f"      Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("      ✅ Success")
            print(f"      total_books: {data.get('total_books', 'N/A')}")
            print(f"      total_users: {data.get('total_users', 'N/A')}")
        else:
            print(f"      ❌ Failed: {response.text}")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    # Test users list
    print("\n   2b. Users List (/api/v1/users/)")
    try:
        response = requests.get(f"{base_url}/api/v1/users/?skip=0&limit=10", headers=api_headers)
        print(f"      Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("      ✅ Success")
            print(f"      users: {len(data.get('users', []))}")
            print(f"      total: {data.get('total', 'N/A')}")
        else:
            print(f"      ❌ Failed: {response.text}")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    # Test books list
    print("\n   2c. Books List (/api/v1/books/)")
    try:
        response = requests.get(f"{base_url}/api/v1/books/", headers=api_headers)
        print(f"      Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("      ✅ Success")
            print(f"      books: {len(data)}")
        else:
            print(f"      ❌ Failed: {response.text}")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    # Test search analytics
    print("\n   2d. Search Analytics (/api/v1/search/analytics)")
    try:
        response = requests.get(f"{base_url}/api/v1/search/analytics", headers=api_headers)
        print(f"      Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("      ✅ Success")
            print(f"      total_searches: {data.get('total_searches', 'N/A')}")
        else:
            print(f"      ❌ Failed: {response.text}")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    # Step 3: Test CORS preflight
    print("\n3. Testing CORS Preflight")
    cors_headers = {
        "Origin": "http://localhost:3001",
        "Access-Control-Request-Method": "GET",
        "Access-Control-Request-Headers": "authorization,content-type"
    }
    
    try:
        response = requests.options(f"{base_url}/api/v1/admin/stats", headers=cors_headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ CORS preflight successful")
            cors_headers = response.headers
            print(f"   Allow-Origin: {cors_headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"   Allow-Methods: {cors_headers.get('Access-Control-Allow-Methods', 'Not set')}")
            print(f"   Allow-Headers: {cors_headers.get('Access-Control-Allow-Headers', 'Not set')}")
        else:
            print(f"   ❌ CORS preflight failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ CORS error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUGGING SUMMARY")
    print("=" * 60)
    print("If all tests above pass, the backend is working correctly.")
    print("If frontend still shows errors, check:")
    print("1. Browser console for JavaScript errors")
    print("2. Network tab for failed requests")
    print("3. Local storage for auth token")
    print("4. React component error boundaries")
    
    return access_token


if __name__ == "__main__":
    test_frontend_auth_flow()
