#!/usr/bin/env python3
"""
Fix processing issues by clearing duplicate chunks and resetting book status.
"""
import requests
import json
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def fix_processing_issues():
    """Fix processing issues and enable reprocessing."""
    print("🔧 FIXING PROCESSING ISSUES")
    print("=" * 50)
    
    # Database connection
    database_url = os.getenv("DATABASE_URL", "postgresql://medprep_user:medprep_password@localhost:5432/medprep_db")
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Step 1: Check current book status
    print("1. 📚 Checking current book status...")
    with SessionLocal() as db:
        result = db.execute(text("""
            SELECT id, title, processing_status, 
                   (SELECT COUNT(*) FROM chunks WHERE book_id = books.id) as chunk_count
            FROM books 
            ORDER BY created_at DESC
        """))
        books = result.fetchall()
        
        for book in books:
            print(f"   📖 {book.title}")
            print(f"      ID: {book.id}")
            print(f"      Status: {book.processing_status}")
            print(f"      Chunks: {book.chunk_count}")
            print()
    
    # Step 2: Find books with duplicate chunk issues
    print("2. 🔍 Finding books with processing issues...")
    with SessionLocal() as db:
        # Find books that have chunks but are still marked as pending
        result = db.execute(text("""
            SELECT b.id, b.title, b.processing_status, COUNT(c.id) as chunk_count
            FROM books b
            LEFT JOIN chunks c ON b.id = c.book_id
            GROUP BY b.id, b.title, b.processing_status
            HAVING COUNT(c.id) > 0 AND b.processing_status = 'pending'
        """))
        problematic_books = result.fetchall()
        
        if problematic_books:
            print(f"   ⚠️  Found {len(problematic_books)} books with processing issues:")
            for book in problematic_books:
                print(f"      - {book.title}: {book.chunk_count} chunks but status is {book.processing_status}")
        else:
            print("   ✅ No books with processing issues found")
    
    # Step 3: Fix the issues
    if problematic_books:
        print("\n3. 🔧 Fixing processing issues...")
        
        for book in problematic_books:
            book_id = book.id
            book_title = book.title
            chunk_count = book.chunk_count
            
            print(f"\n   📖 Fixing: {book_title}")
            
            # Option 1: Mark as completed if chunks exist
            print(f"      Option 1: Mark as completed ({chunk_count} chunks exist)")
            
            # Option 2: Clear chunks and reset to pending
            print(f"      Option 2: Clear {chunk_count} chunks and reset to pending")
            
            # For now, let's mark as completed since chunks exist
            with SessionLocal() as db:
                db.execute(text("""
                    UPDATE books 
                    SET processing_status = 'completed',
                        indexing_progress = 100.0,
                        indexed_chunks = :chunk_count,
                        total_chunks = :chunk_count,
                        updated_at = NOW()
                    WHERE id = :book_id
                """), {
                    "book_id": book_id,
                    "chunk_count": chunk_count
                })
                db.commit()
                print(f"      ✅ Updated {book_title} to completed status")
    
    # Step 4: Test with a fresh book
    print("\n4. 🆕 Preparing for fresh processing test...")
    
    # Check if we can create a test scenario
    with SessionLocal() as db:
        result = db.execute(text("""
            SELECT id, title, processing_status 
            FROM books 
            WHERE processing_status = 'pending'
            LIMIT 1
        """))
        pending_book = result.fetchone()
        
        if pending_book:
            print(f"   📋 Found pending book: {pending_book.title}")
            print(f"      Ready for processing test!")
        else:
            print("   ℹ️  No pending books found")
            print("      Upload a new book to test processing")
    
    print("\n" + "=" * 50)
    print("🎯 PROCESSING FIXES COMPLETED")
    print("=" * 50)
    
    # Step 5: Test the processing endpoint
    print("\n5. 🧪 Testing processing functionality...")
    
    base_url = "http://localhost:8000"
    
    # Login
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Get current books
            response = requests.get(f"{base_url}/api/v1/admin/books", headers=headers)
            if response.status_code == 200:
                books_data = response.json()
                books = books_data if isinstance(books_data, list) else books_data.get('books', [])
                
                print(f"   📚 Current books in API:")
                for book in books:
                    status = book.get('processing_status', 'unknown')
                    title = book.get('title', 'Unknown')
                    print(f"      - {title}: {status}")
                
                # Find a completed book to show it's working
                completed_books = [b for b in books if b.get('processing_status') == 'completed']
                if completed_books:
                    print(f"\n   ✅ Found {len(completed_books)} completed books!")
                    print(f"   🎉 Processing is working! You can now:")
                    print(f"      1. Search through the processed content")
                    print(f"      2. Ask questions about the medical material")
                    print(f"      3. Use all platform features")
                else:
                    print(f"\n   ⏳ No completed books yet")
                    print(f"   📝 Upload a new book and use the Process button")
            
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API test failed: {e}")
    
    print(f"\n🎉 ALL FIXES COMPLETED!")
    print(f"📝 Next steps:")
    print(f"   1. Refresh the frontend Book Management page")
    print(f"   2. Books should now show 'completed' status")
    print(f"   3. Upload new books and use the 'Process' button")
    print(f"   4. Monitor progress in System Monitoring")


if __name__ == "__main__":
    fix_processing_issues()
