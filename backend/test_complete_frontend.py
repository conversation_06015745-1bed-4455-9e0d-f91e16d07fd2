#!/usr/bin/env python3
"""
Complete test of frontend-backend integration to debug admin dashboard issues.
"""
import requests
import json
import time


def test_complete_frontend_integration():
    """Test the complete frontend integration flow."""
    base_url = "http://localhost:8000"
    
    print("🚀 COMPLETE FRONTEND-BACKEND INTEGRATION TEST")
    print("=" * 70)
    
    # Step 1: Test Authentication
    print("\n1. 🔐 AUTHENTICATION TEST")
    print("-" * 40)
    
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "http://localhost:3001"
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data, headers=headers)
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print("   ✅ Login successful")
            print(f"   Token type: {token_data.get('token_type', 'N/A')}")
            print(f"   Token length: {len(access_token)} chars")
        else:
            print(f"   ❌ Login failed: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return
    
    # Step 2: Test All Admin API Endpoints
    print("\n2. 📊 ADMIN API ENDPOINTS TEST")
    print("-" * 40)
    
    api_headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "Origin": "http://localhost:3001"
    }
    
    endpoints = [
        ("/api/v1/admin/stats", "Admin Stats"),
        ("/api/v1/users/?skip=0&limit=10", "Users List"),
        ("/api/v1/books/", "Books List"),
        ("/api/v1/search/analytics", "Search Analytics"),
        ("/api/v1/admin/health", "System Health"),
    ]
    
    all_success = True
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", headers=api_headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {name}: {response.status_code}")
                
                # Show key data points
                if "stats" in endpoint:
                    print(f"      Books: {data.get('total_books', 'N/A')}")
                    print(f"      Users: {data.get('total_users', 'N/A')}")
                elif "users" in endpoint:
                    users = data.get('users', data)
                    print(f"      Users count: {len(users) if isinstance(users, list) else 'N/A'}")
                    print(f"      Total: {data.get('total', 'N/A')}")
                elif "books" in endpoint:
                    books = data if isinstance(data, list) else data.get('books', [])
                    print(f"      Books count: {len(books)}")
                elif "analytics" in endpoint:
                    print(f"      Total searches: {data.get('total_searches', 'N/A')}")
                elif "health" in endpoint:
                    print(f"      Status: {data.get('status', 'N/A')}")
            else:
                print(f"   ❌ {name}: {response.status_code} - {response.text[:100]}")
                all_success = False
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
            all_success = False
    
    # Step 3: Test CORS
    print("\n3. 🌐 CORS TEST")
    print("-" * 40)
    
    cors_headers = {
        "Origin": "http://localhost:3001",
        "Access-Control-Request-Method": "GET",
        "Access-Control-Request-Headers": "authorization,content-type"
    }
    
    try:
        response = requests.options(f"{base_url}/api/v1/admin/stats", headers=cors_headers)
        if response.status_code == 200:
            print("   ✅ CORS preflight successful")
            cors_response_headers = response.headers
            print(f"   Allow-Origin: {cors_response_headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"   Allow-Methods: {cors_response_headers.get('Access-Control-Allow-Methods', 'Not set')}")
            print(f"   Allow-Headers: {cors_response_headers.get('Access-Control-Allow-Headers', 'Not set')}")
        else:
            print(f"   ❌ CORS preflight failed: {response.status_code}")
            all_success = False
    except Exception as e:
        print(f"   ❌ CORS error: {e}")
        all_success = False
    
    # Step 4: Test Frontend URL Structure
    print("\n4. 🔗 URL STRUCTURE TEST")
    print("-" * 40)
    
    # Test redirect behavior
    try:
        # Test without trailing slash (should redirect)
        response = requests.get(f"{base_url}/api/v1/users?skip=0&limit=10", 
                              headers=api_headers, allow_redirects=False)
        if response.status_code == 307:
            print("   ✅ /users redirects correctly (307)")
            redirect_url = response.headers.get('Location', '')
            print(f"   Redirect to: {redirect_url}")
        else:
            print(f"   ⚠️  /users returned {response.status_code} (expected 307)")
        
        # Test with trailing slash (should work directly)
        response = requests.get(f"{base_url}/api/v1/users/?skip=0&limit=10", headers=api_headers)
        if response.status_code == 200:
            print("   ✅ /users/ works directly (200)")
        else:
            print(f"   ❌ /users/ failed: {response.status_code}")
            all_success = False
    except Exception as e:
        print(f"   ❌ URL structure test error: {e}")
        all_success = False
    
    # Step 5: Summary and Recommendations
    print("\n" + "=" * 70)
    print("🎯 INTEGRATION TEST SUMMARY")
    print("=" * 70)
    
    if all_success:
        print("✅ ALL BACKEND TESTS PASSED!")
        print("\n🔧 If frontend still shows errors, check:")
        print("   1. Browser console for JavaScript errors")
        print("   2. Network tab in DevTools for failed requests")
        print("   3. Local storage for auth token")
        print("   4. React error boundaries")
        print("   5. Component state management")
        
        print("\n💡 DEBUGGING STEPS:")
        print("   1. Open browser DevTools (F12)")
        print("   2. Go to Console tab")
        print("   3. Refresh admin dashboard")
        print("   4. Look for error messages")
        print("   5. Check Network tab for failed API calls")
        
        print("\n🚀 EXPECTED BEHAVIOR:")
        print("   - Admin dashboard should show real data")
        print("   - User management should show 5 real users")
        print("   - Book management should show 1 real book")
        print("   - No more 'Admin stats API failed' errors")
    else:
        print("❌ SOME BACKEND TESTS FAILED!")
        print("   Fix backend issues before testing frontend")
    
    return all_success


if __name__ == "__main__":
    test_complete_frontend_integration()
