"""
Tests for search endpoints
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch

from app.main import app


client = TestClient(app)


class TestSearch:
    """Test search endpoints"""

    @patch('app.services.search_service.SearchService.semantic_search')
    def test_search_success(self, mock_search, auth_headers):
        """Test successful search"""
        # Mock search response
        mock_search.return_value = (
            [
                Mock(
                    chunk_id="test-chunk-1",
                    book_id="test-book-1",
                    content="Diabetes mellitus is a chronic condition...",
                    score=0.89,
                    page_number=156,
                    chapter_title="Endocrine Disorders",
                    topic_category="diagnosis",
                    medical_topics=["diabetes", "endocrine"],
                    word_count=150,
                    char_count=800,
                    book_title="Harrison's Internal Medicine",
                    book_authors=["<PERSON>", "<PERSON>"],
                    book_publisher="McGraw-Hill",
                    book_publication_year=2018
                )
            ],
            234  # search_time_ms
        )
        
        search_data = {
            "query": "diabetes management",
            "limit": 20,
            "score_threshold": 0.7
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["query"] == "diabetes management"
        assert data["total_results"] == 1
        assert len(data["results"]) == 1
        assert data["results"][0]["content"] == "Diabetes mellitus is a chronic condition..."
        assert data["search_time_ms"] == 234

    def test_search_unauthorized(self):
        """Test search without authentication"""
        search_data = {
            "query": "diabetes management"
        }
        
        response = client.post("/api/v1/search", json=search_data)
        
        assert response.status_code == 401

    def test_search_empty_query(self, auth_headers):
        """Test search with empty query"""
        search_data = {
            "query": ""
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422

    @patch('app.services.search_service.SearchService.semantic_search')
    def test_search_with_filters(self, mock_search, auth_headers):
        """Test search with filters"""
        mock_search.return_value = ([], 0)
        
        search_data = {
            "query": "hypertension",
            "limit": 10,
            "score_threshold": 0.8,
            "filters": {
                "topic_categories": ["diagnosis", "treatment"],
                "page_range": {"min": 1, "max": 100}
            }
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        # Verify filters were passed to search service
        mock_search.assert_called_once()
        call_args = mock_search.call_args
        assert call_args[1]["filters"]["topic_categories"] == ["diagnosis", "treatment"]

    @patch('app.services.search_service.SearchService.get_suggestions')
    def test_search_suggestions(self, mock_suggestions, auth_headers):
        """Test search suggestions"""
        mock_suggestions.return_value = [
            "diabetes mellitus",
            "diabetic ketoacidosis",
            "diabetic nephropathy"
        ]
        
        response = client.get(
            "/api/v1/search/suggestions?q=diab&limit=5",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "suggestions" in data
        assert len(data["suggestions"]) == 3
        assert "diabetes mellitus" in data["suggestions"]

    @patch('app.services.search_service.SearchService.find_similar_chunks')
    def test_similar_search(self, mock_similar, auth_headers):
        """Test finding similar chunks"""
        mock_similar.return_value = [
            Mock(
                chunk_id="similar-chunk-1",
                content="Related content about diabetes...",
                score=0.85
            )
        ]
        
        similar_data = {
            "chunk_id": "test-chunk-1",
            "limit": 10,
            "score_threshold": 0.8,
            "exclude_same_book": False
        }
        
        response = client.post(
            "/api/v1/search/similar",
            json=similar_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["content"] == "Related content about diabetes..."

    def test_search_history(self, auth_headers):
        """Test getting search history"""
        response = client.get(
            "/api/v1/search/history?limit=10",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data

    @patch('app.services.search_service.SearchService.semantic_search')
    def test_search_performance(self, mock_search, auth_headers):
        """Test search performance tracking"""
        mock_search.return_value = ([], 1500)  # 1.5 second search time
        
        search_data = {
            "query": "complex medical query"
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["search_time_ms"] == 1500

    @patch('app.services.search_service.SearchService.semantic_search')
    def test_search_error_handling(self, mock_search, auth_headers):
        """Test search error handling"""
        mock_search.side_effect = Exception("Search service error")
        
        search_data = {
            "query": "test query"
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 500

    def test_search_analytics(self, auth_headers):
        """Test search analytics endpoint"""
        response = client.get(
            "/api/v1/search/analytics",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        # Analytics should include search statistics
        assert isinstance(data, dict)

    @patch('app.services.search_service.SearchService.semantic_search')
    def test_search_pagination(self, mock_search, auth_headers):
        """Test search result pagination"""
        # Mock large result set
        mock_results = [Mock(chunk_id=f"chunk-{i}") for i in range(100)]
        mock_search.return_value = (mock_results, 100)
        
        search_data = {
            "query": "medical condition",
            "limit": 20
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_results"] == 100
        assert len(data["results"]) <= 20

    def test_search_validation(self, auth_headers):
        """Test search input validation"""
        # Test invalid limit
        search_data = {
            "query": "test",
            "limit": -1
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        
        # Test invalid score threshold
        search_data = {
            "query": "test",
            "score_threshold": 1.5
        }
        
        response = client.post(
            "/api/v1/search",
            json=search_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
