"""
Tests for authentication endpoints
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.main import app
from app.db.models import User
from app.core.security import get_password_hash


client = TestClient(app)


class TestAuth:
    """Test authentication endpoints"""

    def test_signup_success(self, db: Session):
        """Test successful user registration"""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User",
            "institution": "Test University",
            "specialization": "Internal Medicine"
        }
        
        response = client.post("/api/v1/auth/signup", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["message"] == "User created successfully"
        
        # Verify user was created in database
        user = db.query(User).filter(User.email == user_data["email"]).first()
        assert user is not None
        assert user.full_name == user_data["full_name"]
        assert user.institution == user_data["institution"]

    def test_signup_duplicate_email(self, db: Session):
        """Test registration with duplicate email"""
        # Create existing user
        existing_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password"),
            full_name="Existing User"
        )
        db.add(existing_user)
        db.commit()
        
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        
        response = client.post("/api/v1/auth/signup", json=user_data)
        
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]

    def test_signup_invalid_email(self):
        """Test registration with invalid email"""
        user_data = {
            "email": "invalid-email",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        
        response = client.post("/api/v1/auth/signup", json=user_data)
        
        assert response.status_code == 422

    def test_signup_weak_password(self):
        """Test registration with weak password"""
        user_data = {
            "email": "<EMAIL>",
            "password": "123",
            "full_name": "Test User"
        }
        
        response = client.post("/api/v1/auth/signup", json=user_data)
        
        assert response.status_code == 422

    def test_login_success(self, db: Session):
        """Test successful login"""
        # Create test user
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("testpassword123"),
            full_name="Login User",
            is_active=True,
            is_verified=True
        )
        db.add(user)
        db.commit()
        
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert data["user"]["email"] == "<EMAIL>"

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        response = client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]

    def test_login_inactive_user(self, db: Session):
        """Test login with inactive user"""
        # Create inactive user
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("testpassword123"),
            full_name="Inactive User",
            is_active=False
        )
        db.add(user)
        db.commit()
        
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 400
        assert "Inactive user" in response.json()["detail"]

    def test_get_current_user(self, db: Session, auth_headers):
        """Test getting current user info"""
        response = client.get("/api/v1/users/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "email" in data
        assert "full_name" in data
        assert "role" in data

    def test_get_current_user_unauthorized(self):
        """Test getting current user without authentication"""
        response = client.get("/api/v1/users/me")
        
        assert response.status_code == 401

    def test_update_profile(self, db: Session, auth_headers):
        """Test updating user profile"""
        update_data = {
            "full_name": "Updated Name",
            "institution": "Updated University",
            "specialization": "Updated Specialization"
        }
        
        response = client.put(
            "/api/v1/users/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "Updated Name"
        assert data["institution"] == "Updated University"

    def test_change_password(self, db: Session, auth_headers):
        """Test changing password"""
        password_data = {
            "current_password": "testpassword123",
            "new_password": "newpassword456"
        }
        
        response = client.post(
            "/api/v1/users/me/change-password",
            json=password_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        assert response.json()["message"] == "Password updated successfully"

    def test_change_password_wrong_current(self, db: Session, auth_headers):
        """Test changing password with wrong current password"""
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newpassword456"
        }
        
        response = client.post(
            "/api/v1/users/me/change-password",
            json=password_data,
            headers=auth_headers
        )
        
        assert response.status_code == 400
        assert "Incorrect password" in response.json()["detail"]

    def test_logout(self, auth_headers):
        """Test user logout"""
        response = client.post("/api/v1/auth/logout", headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json()["message"] == "Successfully logged out"
