"""
Test configuration and fixtures
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.base import Base
from app.db.session import get_db
from app.db.models import User
from app.core.security import get_password_hash, create_access_token
from app.core.config import settings


# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine"""
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db(db_engine):
    """Create test database session"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client(db):
    """Create test client with database dependency override"""
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db):
    """Create test user"""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword123"),
        full_name="Test User",
        role="user",
        is_active=True,
        is_verified=True,
        institution="Test University",
        specialization="Internal Medicine"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def admin_user(db):
    """Create admin user"""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("adminpassword123"),
        full_name="Admin User",
        role="admin",
        is_active=True,
        is_verified=True
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user"""
    access_token = create_access_token(subject=test_user.id)
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_headers(admin_user):
    """Create authentication headers for admin user"""
    access_token = create_access_token(subject=admin_user.id)
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def sample_book_data():
    """Sample book data for testing"""
    return {
        "title": "Test Medical Book",
        "authors": ["Dr. Test Author", "Dr. Another Author"],
        "isbn": "978-**********",
        "publisher": "Test Publisher",
        "publication_year": 2023,
        "edition": "1st",
        "description": "A comprehensive test medical book",
        "tags": ["medicine", "test", "education"],
        "language": "en"
    }


@pytest.fixture
def sample_search_data():
    """Sample search data for testing"""
    return {
        "query": "diabetes management",
        "limit": 20,
        "score_threshold": 0.7,
        "filters": {
            "topic_categories": ["diagnosis", "treatment"],
            "page_range": {"min": 1, "max": 500}
        }
    }


@pytest.fixture
def sample_qa_data():
    """Sample Q&A data for testing"""
    return {
        "question": "What are the main causes of hypertension?",
        "context_limit": 5,
        "include_citations": True,
        "filters": {
            "topic_categories": ["diagnosis", "pathophysiology"]
        }
    }


@pytest.fixture(autouse=True)
def mock_external_services(monkeypatch):
    """Mock external services for testing"""
    # Mock Qdrant client
    class MockQdrantClient:
        def search(self, *args, **kwargs):
            return []
        
        def upsert(self, *args, **kwargs):
            return True
        
        def delete(self, *args, **kwargs):
            return True
    
    # Mock OpenAI client
    class MockOpenAIClient:
        class ChatCompletion:
            @staticmethod
            async def acreate(*args, **kwargs):
                class MockResponse:
                    choices = [
                        type('obj', (object,), {
                            'message': type('obj', (object,), {
                                'content': 'Mock AI response'
                            })()
                        })()
                    ]
                return MockResponse()
    
    # Mock Cohere client
    class MockCohereClient:
        def embed(self, *args, **kwargs):
            return type('obj', (object,), {
                'embeddings': [[0.1] * 1536]
            })()
    
    monkeypatch.setattr("app.services.vector_service.QdrantClient", MockQdrantClient)
    monkeypatch.setattr("app.services.llm_service.openai.ChatCompletion", MockOpenAIClient.ChatCompletion)
    monkeypatch.setattr("app.services.embedding_service.cohere.Client", MockCohereClient)


@pytest.fixture
def mock_file_upload():
    """Mock file upload for testing"""
    class MockFile:
        def __init__(self, filename="test.pdf", content=b"test content"):
            self.filename = filename
            self.content = content
            self.size = len(content)
        
        async def read(self):
            return self.content
    
    return MockFile


# Test configuration overrides
@pytest.fixture(autouse=True)
def test_settings(monkeypatch):
    """Override settings for testing"""
    monkeypatch.setattr(settings, "TESTING", True)
    monkeypatch.setattr(settings, "DATABASE_URL", SQLALCHEMY_DATABASE_URL)
    monkeypatch.setattr(settings, "SECRET_KEY", "test-secret-key")
    monkeypatch.setattr(settings, "ACCESS_TOKEN_EXPIRE_MINUTES", 30)


# Async test support
@pytest.fixture
def anyio_backend():
    """Configure anyio backend for async tests"""
    return "asyncio"


# Custom markers for test categories
def pytest_configure(config):
    """Configure custom pytest markers"""
    config.addinivalue_line("markers", "unit: mark test as unit test")
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "slow: mark test as slow running")
    config.addinivalue_line("markers", "auth: mark test as authentication related")
    config.addinivalue_line("markers", "search: mark test as search related")
    config.addinivalue_line("markers", "qa: mark test as Q&A related")
    config.addinivalue_line("markers", "admin: mark test as admin related")


# Test data cleanup
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Clean up test data after each test"""
    yield
    # Cleanup logic here if needed
    pass
