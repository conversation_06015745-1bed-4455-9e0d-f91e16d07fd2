#!/usr/bin/env python3
"""
Test script for admin dashboard functionality.
Tests all admin endpoints and features.
"""
import requests
import json


def get_admin_token():
    """Get admin token for testing."""
    print("🔐 Getting admin token...")
    
    # Login as admin user
    login_data = {
        "username": "<EMAIL>",  # This user has admin role
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            data=login_data
        )
        
        if response.status_code == 200:
            token_data = response.json()
            print("✅ Admin login successful")
            return token_data["access_token"]
        else:
            print(f"❌ Admin login failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return None


def test_admin_stats(token):
    """Test admin statistics endpoint."""
    print("\n📊 Testing Admin Stats...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/admin/stats",
            headers=headers
        )
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ Admin stats successful")
            print(f"   Total users: {stats.get('total_users', 'Unknown')}")
            print(f"   Total books: {stats.get('total_books', 'Unknown')}")
            print(f"   Storage used: {stats.get('storage_used', 'Unknown')}")
            print(f"   System health: {stats.get('system_health', 'Unknown')}")
            return True
        else:
            print(f"❌ Admin stats failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Admin stats error: {e}")
        return False


def test_user_management(token):
    """Test user management endpoints."""
    print("\n👥 Testing User Management...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test get users with pagination
        response = requests.get(
            "http://localhost:8000/api/v1/users/?skip=0&limit=10",
            headers=headers
        )
        
        if response.status_code == 200:
            users_data = response.json()
            print("✅ User list retrieval successful")
            print(f"   Total users: {users_data.get('total', 'Unknown')}")
            print(f"   Users in page: {len(users_data.get('users', []))}")
            print(f"   Current page: {users_data.get('page', 'Unknown')}")
            
            # Show sample user data
            users = users_data.get('users', [])
            if users:
                sample_user = users[0]
                print(f"   Sample user: {sample_user.get('email', 'Unknown')} ({sample_user.get('role', 'Unknown')})")
            
            return True
        else:
            print(f"❌ User management failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ User management error: {e}")
        return False


def test_book_management(token):
    """Test book management endpoints."""
    print("\n📚 Testing Book Management...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test get books
        response = requests.get(
            "http://localhost:8000/api/v1/books/",
            headers=headers
        )
        
        if response.status_code == 200:
            books = response.json()
            print("✅ Book list retrieval successful")
            print(f"   Total books: {len(books)}")
            
            if books:
                sample_book = books[0]
                print(f"   Sample book: {sample_book.get('title', 'Unknown')}")
                print(f"   Author: {sample_book.get('author', 'Unknown')}")
                print(f"   Status: {sample_book.get('status', 'Unknown')}")
            
            return True
        else:
            print(f"❌ Book management failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Book management error: {e}")
        return False


def test_search_analytics(token):
    """Test search analytics endpoints."""
    print("\n🔍 Testing Search Analytics...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test search analytics
        response = requests.get(
            "http://localhost:8000/api/v1/search/analytics",
            headers=headers
        )
        
        if response.status_code == 200:
            analytics = response.json()
            print("✅ Search analytics successful")
            print(f"   Total searches: {analytics.get('total_searches', 'Unknown')}")
            print(f"   Popular queries: {len(analytics.get('popular_queries', []))}")
            print(f"   Popular topics: {len(analytics.get('popular_topics', []))}")
            
            # Show sample popular queries
            popular_queries = analytics.get('popular_queries', [])
            if popular_queries:
                print(f"   Top query: {popular_queries[0].get('query', 'Unknown')} ({popular_queries[0].get('count', 0)} searches)")
            
            return True
        else:
            print(f"❌ Search analytics failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Search analytics error: {e}")
        return False


def test_system_health(token):
    """Test system health endpoints."""
    print("\n🏥 Testing System Health...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test system health
        response = requests.get(
            "http://localhost:8000/api/v1/admin/health",
            headers=headers
        )
        
        if response.status_code == 200:
            health = response.json()
            print("✅ System health check successful")
            print(f"   Database: {health.get('database', 'Unknown')}")
            print(f"   Vector DB: {health.get('vector_db', 'Unknown')}")
            print(f"   Storage: {health.get('storage', 'Unknown')}")
            print(f"   Overall status: {health.get('status', 'Unknown')}")
            return True
        else:
            print(f"❌ System health failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ System health error: {e}")
        return False


def test_service_info(token):
    """Test service information endpoints."""
    print("\n⚙️ Testing Service Info...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test search service info
        response = requests.get(
            "http://localhost:8000/api/v1/search/service-info",
            headers=headers
        )
        
        if response.status_code == 200:
            service_info = response.json()
            print("✅ Service info successful")
            print(f"   Status: {service_info.get('status', 'Unknown')}")
            
            info = service_info.get('service_info', {})
            print(f"   Service type: {info.get('service_type', 'Unknown')}")
            print(f"   Total vectors: {info.get('total_vectors', 'Unknown')}")
            
            features = service_info.get('features', {})
            print(f"   Offline search: {features.get('offline_search', False)}")
            print(f"   Local embeddings: {features.get('local_embeddings', False)}")
            
            return True
        else:
            print(f"❌ Service info failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Service info error: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Testing Admin Dashboard Functionality\n")
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("\n❌ Could not get admin token, cannot continue")
        exit(1)
    
    # Run all admin tests
    tests = [
        ("Admin Stats", lambda: test_admin_stats(token)),
        ("User Management", lambda: test_user_management(token)),
        ("Book Management", lambda: test_book_management(token)),
        ("Search Analytics", lambda: test_search_analytics(token)),
        ("System Health", lambda: test_system_health(token)),
        ("Service Info", lambda: test_service_info(token)),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ADMIN DASHBOARD TEST SUMMARY")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All admin dashboard tests passed!")
        print("\n💡 Admin dashboard features working:")
        print("   - User management with pagination")
        print("   - Book management and statistics")
        print("   - Search analytics and monitoring")
        print("   - System health monitoring")
        print("   - Service configuration info")
    else:
        failed_tests = [name for name, success in results.items() if not success]
        print(f"\n❌ Failed tests: {', '.join(failed_tests)}")
    
    exit(0 if all_passed else 1)
