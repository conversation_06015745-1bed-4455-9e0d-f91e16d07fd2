#!/usr/bin/env python3
"""
Test script for the topic search functionality.
Tests the local embedding service and enhanced Qdrant service.
"""
import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.local_embedding_service import get_embedding_service
from app.services.enhanced_qdrant_service import get_qdrant_service


async def test_embedding_service():
    """Test the local embedding service."""
    print("🧪 Testing Local Embedding Service...")
    
    try:
        embedding_service = get_embedding_service()
        
        # Test model info
        model_info = embedding_service.get_model_info()
        print(f"✅ Model loaded: {model_info['model_name']}")
        print(f"✅ Embedding dimension: {model_info['embedding_dimension']}")
        print(f"✅ Device: {model_info['device']}")
        
        # Test single encoding
        test_query = "diabetes mellitus diagnosis"
        print(f"\n🔍 Testing query: '{test_query}'")
        
        embedding = embedding_service.encode_single(test_query)
        print(f"✅ Generated embedding shape: {embedding.shape}")
        print(f"✅ Embedding type: {type(embedding)}")
        print(f"✅ First 5 values: {embedding[:5]}")
        
        # Test batch encoding
        test_texts = [
            "diabetes mellitus diagnosis",
            "kawasaki disease treatment", 
            "cardiac anatomy structure"
        ]
        print(f"\n📦 Testing batch encoding with {len(test_texts)} texts...")
        
        batch_embeddings = embedding_service.encode_batch(test_texts)
        print(f"✅ Generated {len(batch_embeddings)} embeddings")
        
        # Test similarity
        similarity = embedding_service.similarity(batch_embeddings[0], batch_embeddings[1])
        print(f"✅ Similarity between first two texts: {similarity:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Embedding service test failed: {e}")
        return False


async def test_qdrant_service():
    """Test the enhanced Qdrant service."""
    print("\n🧪 Testing Enhanced Qdrant Service...")
    
    try:
        qdrant_service = get_qdrant_service()
        
        # Test service info
        service_info = qdrant_service.get_service_info()
        print(f"✅ Service type: {service_info['service_type']}")
        print(f"✅ Collection: {service_info['collection_name']}")
        print(f"✅ Total vectors: {service_info['total_vectors']}")
        
        # Test topic search
        test_queries = [
            "diabetes mellitus diagnosis",
            "kawasaki disease treatment",
            "cardiac anatomy",
            "respiratory system pathophysiology"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing search: '{query}'")
            
            results = qdrant_service.search_topic(
                query=query,
                limit=5,
                score_threshold=0.5
            )
            
            if results:
                total_results = sum(len(section_results) for section_results in results.values())
                print(f"✅ Found {total_results} results in {len(results)} sections")
                
                for section_type, section_results in results.items():
                    print(f"  📋 {section_type}: {len(section_results)} results")
                    if section_results:
                        best_result = section_results[0]
                        print(f"    🏆 Best match (score: {best_result['score']:.3f})")
                        print(f"    📖 Book: {best_result['metadata'].get('book_title', 'Unknown')}")
                        print(f"    📄 Text preview: {best_result['text'][:100]}...")
            else:
                print("❌ No results found")
        
        return True
        
    except Exception as e:
        print(f"❌ Qdrant service test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Topic Search System Tests\n")
    
    # Test embedding service
    embedding_success = await test_embedding_service()
    
    # Test Qdrant service
    qdrant_success = await test_qdrant_service()
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"Embedding Service: {'✅ PASS' if embedding_success else '❌ FAIL'}")
    print(f"Qdrant Service: {'✅ PASS' if qdrant_success else '❌ FAIL'}")
    
    if embedding_success and qdrant_success:
        print("\n🎉 All tests passed! Topic search system is ready.")
        print("\n💡 Next steps:")
        print("   1. Open http://localhost:3001 in your browser")
        print("   2. Log in to the application")
        print("   3. Navigate to 'Topic Search' in the sidebar")
        print("   4. Try searching for medical topics like:")
        print("      - 'diabetes mellitus diagnosis'")
        print("      - 'kawasaki disease treatment'")
        print("      - 'cardiac anatomy structure'")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
    
    return embedding_success and qdrant_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
