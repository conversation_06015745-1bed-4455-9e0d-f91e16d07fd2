#!/usr/bin/env python3
"""
Test script for the topic search API endpoint with authentication.
"""
import requests
import json


def test_topic_search_api():
    """Test the topic search API endpoint."""
    base_url = "http://localhost:8000"
    
    # First, login to get a token
    print("🔐 Logging in...")
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
            return False
        
        token = login_response.json()["access_token"]
        print("✅ Login successful")
        
        # Test topic search
        headers = {"Authorization": f"Bearer {token}"}
        
        test_queries = [
            "diabetes mellitus diagnosis",
            "kawasaki disease treatment",
            "cardiac anatomy"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing topic search: '{query}'")
            
            search_data = {
                "query": query,
                "limit": 10,
                "score_threshold": 0.6
            }
            
            response = requests.post(
                f"{base_url}/api/v1/search/topic",
                json=search_data,
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Search successful!")
                print(f"   Total results: {result['total_results']}")
                print(f"   Sections found: {list(result['grouped_results'].keys())}")
                print(f"   Message: {result['message']}")
                
                # Show sample results
                for section_type, results in result['grouped_results'].items():
                    if results:
                        best_result = results[0]
                        print(f"   📋 {section_type}: {len(results)} results")
                        print(f"      🏆 Best match (score: {best_result['score']:.3f})")
                        print(f"      📖 Book: {best_result['metadata'].get('book_title', 'Unknown')}")
                        print(f"      📄 Preview: {best_result['text'][:100]}...")
            else:
                print(f"❌ Search failed: {response.status_code} - {response.text}")
                return False
        
        print("\n🎉 All topic search tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False


if __name__ == "__main__":
    success = test_topic_search_api()
    if success:
        print("\n💡 Topic search API is working! You can now:")
        print("   1. Open http://localhost:3001 in your browser")
        print("   2. Log <NAME_EMAIL> / admin123")
        print("   3. Navigate to 'Topic Search' in the sidebar")
        print("   4. Try searching for medical topics!")
    else:
        print("\n❌ Topic search API test failed.")
