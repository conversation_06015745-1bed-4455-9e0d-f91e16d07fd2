# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build
frontend/build/
frontend/dist/

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Uploads
uploads/
temp/

# Docker
.dockerignore

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.tmp

# SSL certificates
*.pem
*.key
*.crt

# Alembic
alembic/versions/*.py
!alembic/versions/__init__.py

# Test files
test_uploads/
test_data/
backend/test_*.py
backend/fix_*.py
backend/quick_*.py
process-books.py

# Qdrant storage (local development)
qdrant_storage/

# Backend uploads
backend/uploads/

# IDE
*.swp
*.swo
*~

# Coverage reports
.coverage
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Documentation builds
docs/_build/
site/
