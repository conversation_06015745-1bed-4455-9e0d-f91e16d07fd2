# Testing Guide

This guide covers testing strategies, setup, and execution for the MedPrep platform.

## 🧪 Testing Strategy

### Testing Pyramid
- **Unit Tests (70%)**: Test individual functions and components
- **Integration Tests (20%)**: Test API endpoints and service interactions
- **End-to-End Tests (10%)**: Test complete user workflows

### Test Categories
- **Backend Tests**: API endpoints, services, database operations
- **Frontend Tests**: Components, hooks, user interactions
- **Integration Tests**: Full stack workflows
- **Performance Tests**: Load testing and benchmarks

## 🔧 Backend Testing

### Setup
```bash
cd backend
pip install pytest pytest-asyncio pytest-cov
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_auth.py

# Run tests with specific marker
pytest -m "auth"

# Run tests in parallel
pytest -n auto
```

### Test Configuration
```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    auth: Authentication tests
    search: Search tests
    qa: Q&A tests
    admin: Admin tests
addopts = 
    --strict-markers
    --disable-warnings
    --tb=short
```

### Test Structure
```
backend/tests/
├── conftest.py              # Test configuration and fixtures
├── test_auth.py            # Authentication tests
├── test_search.py          # Search functionality tests
├── test_qa.py              # Q&A functionality tests
├── test_admin.py           # Admin functionality tests
├── test_models.py          # Database model tests
├── test_services.py        # Service layer tests
└── integration/
    ├── test_api_integration.py
    └── test_workflow.py
```

### Writing Tests

#### Unit Test Example
```python
def test_password_hashing():
    """Test password hashing functionality"""
    password = "testpassword123"
    hashed = get_password_hash(password)
    
    assert hashed != password
    assert verify_password(password, hashed)
    assert not verify_password("wrongpassword", hashed)
```

#### API Test Example
```python
def test_search_endpoint(client, auth_headers):
    """Test search API endpoint"""
    search_data = {
        "query": "diabetes management",
        "limit": 10
    }
    
    response = client.post(
        "/api/v1/search",
        json=search_data,
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "results" in data
    assert "total_results" in data
```

#### Database Test Example
```python
def test_user_creation(db):
    """Test user model creation"""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User"
    )
    db.add(user)
    db.commit()
    
    saved_user = db.query(User).filter(User.email == "<EMAIL>").first()
    assert saved_user is not None
    assert saved_user.full_name == "Test User"
```

### Mocking External Services
```python
@patch('app.services.llm_service.openai.ChatCompletion.acreate')
def test_ai_answer_generation(mock_openai, client, auth_headers):
    """Test AI answer generation with mocked OpenAI"""
    mock_openai.return_value = Mock(
        choices=[Mock(message=Mock(content="Mock AI response"))]
    )
    
    qa_data = {
        "question": "What causes diabetes?",
        "context_limit": 5
    }
    
    response = client.post("/api/v1/qa/ask", json=qa_data, headers=auth_headers)
    assert response.status_code == 200
```

## 🎨 Frontend Testing

### Setup
```bash
cd frontend
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event
```

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test SearchPage.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="search"
```

### Test Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx',
    '!src/reportWebVitals.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};
```

### Test Structure
```
frontend/src/
├── components/
│   └── __tests__/
│       ├── SearchPage.test.tsx
│       ├── LoginPage.test.tsx
│       └── CitationViewer.test.tsx
├── hooks/
│   └── __tests__/
│       └── useApi.test.ts
├── services/
│   └── __tests__/
│       ├── authService.test.ts
│       └── searchService.test.ts
└── utils/
    └── __tests__/
        └── helpers.test.ts
```

### Writing Frontend Tests

#### Component Test Example
```typescript
test('renders search page with initial state', () => {
  render(
    <TestWrapper>
      <SearchPage />
    </TestWrapper>
  );

  expect(screen.getByText('Medical Literature Search')).toBeInTheDocument();
  expect(screen.getByPlaceholderText('Search medical literature...')).toBeInTheDocument();
});
```

#### User Interaction Test
```typescript
test('performs search on button click', async () => {
  const mockSearch = jest.fn().mockResolvedValue({ results: [] });
  jest.spyOn(searchService, 'search').mockImplementation(mockSearch);

  render(<TestWrapper><SearchPage /></TestWrapper>);

  const input = screen.getByPlaceholderText('Search medical literature...');
  const button = screen.getByText('Search');

  fireEvent.change(input, { target: { value: 'diabetes' } });
  fireEvent.click(button);

  await waitFor(() => {
    expect(mockSearch).toHaveBeenCalledWith(
      expect.objectContaining({ query: 'diabetes' })
    );
  });
});
```

#### Hook Test Example
```typescript
test('useApi hook handles loading states', async () => {
  const mockApiCall = jest.fn().mockResolvedValue({ data: 'test' });
  
  const { result } = renderHook(() => useApi(mockApiCall));
  
  expect(result.current.loading).toBe(false);
  
  act(() => {
    result.current.execute();
  });
  
  expect(result.current.loading).toBe(true);
  
  await waitFor(() => {
    expect(result.current.loading).toBe(false);
    expect(result.current.data).toEqual({ data: 'test' });
  });
});
```

## 🔗 Integration Testing

### API Integration Tests
```python
def test_complete_search_workflow(client, auth_headers, sample_book):
    """Test complete search workflow"""
    # Upload and process book
    upload_response = client.post(
        "/api/v1/admin/books/upload",
        files={"file": ("test.pdf", b"test content")},
        data={"title": "Test Book", "authors": "Test Author"},
        headers=auth_headers
    )
    assert upload_response.status_code == 200
    
    book_id = upload_response.json()["book_id"]
    
    # Process book
    process_response = client.post(
        f"/api/v1/admin/books/{book_id}/process",
        headers=auth_headers
    )
    assert process_response.status_code == 200
    
    # Search for content
    search_response = client.post(
        "/api/v1/search",
        json={"query": "test content"},
        headers=auth_headers
    )
    assert search_response.status_code == 200
    assert search_response.json()["total_results"] > 0
```

### End-to-End Tests
```typescript
// Using Playwright or Cypress
test('complete user journey', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  
  // Navigate to search
  await page.click('text=Search');
  
  // Perform search
  await page.fill('[placeholder*="Search"]', 'diabetes');
  await page.click('text=Search');
  
  // Verify results
  await expect(page.locator('text=Search Results')).toBeVisible();
});
```

## 📊 Performance Testing

### Load Testing with Locust
```python
# locustfile.py
from locust import HttpUser, task, between

class MedPrepUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Login
        response = self.client.post("/api/v1/auth/login", data={
            "username": "<EMAIL>",
            "password": "password123"
        })
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}
    
    @task(3)
    def search(self):
        self.client.post("/api/v1/search", json={
            "query": "diabetes management",
            "limit": 20
        }, headers=self.headers)
    
    @task(1)
    def ask_question(self):
        self.client.post("/api/v1/qa/ask", json={
            "question": "What causes diabetes?",
            "context_limit": 5
        }, headers=self.headers)
```

### Database Performance Tests
```python
def test_search_performance(db, benchmark):
    """Benchmark search query performance"""
    def search_query():
        return db.query(Chunk).filter(
            Chunk.content.contains("diabetes")
        ).limit(20).all()
    
    result = benchmark(search_query)
    assert len(result) > 0
```

## 🔍 Test Coverage

### Coverage Goals
- **Backend**: 85%+ line coverage
- **Frontend**: 80%+ line coverage
- **Critical paths**: 95%+ coverage

### Generating Coverage Reports
```bash
# Backend coverage
pytest --cov=app --cov-report=html --cov-report=term

# Frontend coverage
npm test -- --coverage --watchAll=false

# Combined coverage report
npm run test:coverage
```

### Coverage Configuration
```python
# .coveragerc
[run]
source = app
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */conftest.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

## 🚀 Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          cd backend
          pip install -r requirements.txt
      
      - name: Run tests
        run: |
          cd backend
          pytest --cov=app --cov-report=xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      
      - name: Run tests
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false
```

## 🐛 Debugging Tests

### Common Issues
1. **Async test failures**: Use proper async/await patterns
2. **Database state**: Ensure proper cleanup between tests
3. **Mock conflicts**: Clear mocks between tests
4. **Timing issues**: Use waitFor for async operations

### Debugging Tools
```bash
# Run single test with verbose output
pytest tests/test_auth.py::test_login_success -v -s

# Debug frontend tests
npm test -- --no-coverage --verbose

# Run tests with debugger
pytest --pdb tests/test_auth.py
```

## 📝 Best Practices

### Test Organization
- Group related tests in classes
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests independent and isolated

### Test Data
- Use factories for test data generation
- Clean up test data after each test
- Use realistic but minimal test data

### Assertions
- Use specific assertions
- Test both positive and negative cases
- Verify error conditions
- Check edge cases

### Maintenance
- Update tests when code changes
- Remove obsolete tests
- Refactor test code regularly
- Monitor test performance
