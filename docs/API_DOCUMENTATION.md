# API Documentation

## Overview

The MedPrep API provides endpoints for medical literature search, AI-powered Q&A, user management, and administrative functions. All endpoints return JSON responses and use standard HTTP status codes.

## Base URL
```
http://localhost:8000/api/v1
```

## Authentication

The API uses <PERSON>WT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Getting a Token
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=yourpassword
```

## Response Format

All API responses follow this structure:

```json
{
  "success": true,
  "data": { ... },
  "message": "Optional message",
  "error": null
}
```

Error responses:
```json
{
  "success": false,
  "data": null,
  "message": null,
  "error": "Error description"
}
```

## Authentication Endpoints

### Login
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=password123
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "role": "user",
    "is_active": true,
    "is_verified": true
  }
}
```

### Register
```http
POST /auth/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "John Doe",
  "institution": "Medical University",
  "specialization": "Internal Medicine",
  "year_of_study": 3
}
```

### Logout
```http
POST /auth/logout
Authorization: Bearer <token>
```

## Search Endpoints

### Semantic Search
```http
POST /search
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "diabetes management",
  "limit": 20,
  "score_threshold": 0.7,
  "filters": {
    "topic_categories": ["treatment", "diagnosis"],
    "book_ids": ["uuid1", "uuid2"],
    "page_range": {"min": 1, "max": 100}
  }
}
```

**Response:**
```json
{
  "query": "diabetes management",
  "total_results": 45,
  "search_time_ms": 234,
  "results": [
    {
      "chunk_id": "uuid",
      "book_id": "uuid",
      "content": "Diabetes management involves...",
      "score": 0.89,
      "page_number": 156,
      "chapter_title": "Endocrine Disorders",
      "topic_category": "treatment",
      "medical_topics": ["diabetes", "insulin", "glucose"],
      "book_title": "Harrison's Internal Medicine",
      "book_authors": ["Dennis Kasper", "Anthony Fauci"]
    }
  ]
}
```

### Search Suggestions
```http
GET /search/suggestions?q=diab&limit=10
Authorization: Bearer <token>
```

**Response:**
```json
{
  "suggestions": [
    "diabetes mellitus",
    "diabetic ketoacidosis",
    "diabetic nephropathy",
    "diabetic retinopathy"
  ]
}
```

### Similar Content
```http
POST /search/similar
Authorization: Bearer <token>
Content-Type: application/json

{
  "chunk_id": "uuid",
  "limit": 10,
  "score_threshold": 0.8,
  "exclude_same_book": false
}
```

### Search History
```http
GET /search/history?limit=50
Authorization: Bearer <token>
```

## Q&A Endpoints

### Ask Question
```http
POST /qa/ask
Authorization: Bearer <token>
Content-Type: application/json

{
  "question": "What are the main causes of hypertension?",
  "context_limit": 5,
  "include_citations": true,
  "filters": {
    "topic_categories": ["diagnosis", "pathophysiology"]
  }
}
```

**Response:**
```json
{
  "question": "What are the main causes of hypertension?",
  "answer": "Hypertension has multiple causes including...",
  "answer_id": "uuid",
  "citations": [
    {
      "chunk_id": "uuid",
      "book_id": "uuid",
      "book_title": "Braunwald's Heart Disease",
      "book_authors": ["Douglas Zipes", "Peter Libby"],
      "page_number": 945,
      "chapter_title": "Hypertension",
      "cited_text": "Primary hypertension accounts for 90-95%...",
      "relevance_score": 0.92,
      "citation_order": 1
    }
  ],
  "confidence_score": 0.87,
  "response_time_ms": 1234,
  "model_used": "gpt-4",
  "context_chunks_used": 5
}
```

### Citation Details
```http
GET /qa/citation/{citation_id}
Authorization: Bearer <token>
```

### Q&A History
```http
GET /qa/history?limit=50
Authorization: Bearer <token>
```

### Submit Feedback
```http
POST /qa/feedback
Authorization: Bearer <token>
Content-Type: application/json

{
  "answer_id": "uuid",
  "rating": 4,
  "feedback_text": "Very helpful answer",
  "helpful_citations": ["citation_id_1", "citation_id_2"]
}
```

## User Endpoints

### Get Current User
```http
GET /users/me
Authorization: Bearer <token>
```

### Update Profile
```http
PUT /users/me
Authorization: Bearer <token>
Content-Type: application/json

{
  "full_name": "John Smith",
  "institution": "Harvard Medical School",
  "specialization": "Cardiology",
  "year_of_study": 4
}
```

### Change Password
```http
POST /users/me/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

## Admin Endpoints

### Upload Book
```http
POST /admin/books/upload
Authorization: Bearer <admin_token>
Content-Type: multipart/form-data

file: <PDF file>
title: "Harrison's Principles of Internal Medicine"
authors: "Dennis Kasper, Anthony Fauci, Stephen Hauser"
isbn: "978-**********"
publisher: "McGraw-Hill Education"
publication_year: 2018
edition: "20th"
description: "Comprehensive internal medicine textbook"
tags: "internal medicine, diagnosis, treatment"
language: "en"
```

### List Books
```http
GET /admin/books?page=1&page_size=20&status_filter=completed&search=harrison
Authorization: Bearer <admin_token>
```

### Get Book Details
```http
GET /admin/books/{book_id}
Authorization: Bearer <admin_token>
```

### Process Book
```http
POST /admin/books/{book_id}/process
Authorization: Bearer <admin_token>
```

### System Statistics
```http
GET /admin/stats
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "total_books": 150,
  "total_chunks": 50000,
  "total_pages": 75000,
  "processing_stats": {
    "completed": 140,
    "processing": 5,
    "failed": 3,
    "pending": 2
  },
  "storage_stats": {
    "upload_directory_size_bytes": 5368709120,
    "upload_directory_size_mb": 5120
  },
  "recent_activity": [
    {
      "type": "book_upload",
      "title": "Gray's Anatomy",
      "status": "completed",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 422  | Validation Error |
| 429  | Rate Limited |
| 500  | Internal Server Error |

## Rate Limiting

API endpoints are rate limited:
- Search endpoints: 100 requests per minute
- Q&A endpoints: 20 requests per minute
- Upload endpoints: 5 requests per minute
- Other endpoints: 200 requests per minute

## Pagination

List endpoints support pagination:

```http
GET /admin/books?page=2&page_size=20
```

Response includes pagination metadata:
```json
{
  "items": [...],
  "total": 150,
  "page": 2,
  "page_size": 20
}
```

## Filtering and Sorting

Many endpoints support filtering and sorting:

```http
GET /admin/books?status=completed&sort=created_at&order=desc
```

## Health Check

```http
GET /health
```

Returns system health status and service availability.
