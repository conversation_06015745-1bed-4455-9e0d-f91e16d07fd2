# Deployment Guide

This guide covers deploying the MedPrep platform to production environments.

## 🚀 Production Deployment Options

### Option 1: Docker Deployment (Recommended)

#### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 50GB+ storage

#### Quick Deploy
```bash
# Clone repository
git clone <repository-url>
cd MedPrep

# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

#### Production Docker Compose
Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: medprep
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/medprep
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
    volumes:
      - uploads:/app/uploads
    depends_on:
      - postgres
      - redis
      - qdrant
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - backend
    restart: unless-stopped

  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/medprep
      - REDIS_URL=redis://redis:6379
    volumes:
      - uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
  qdrant_data:
  uploads:
```

### Option 2: Cloud Deployment

#### AWS Deployment

**Using AWS ECS with Fargate:**

1. **Setup Infrastructure**
```bash
# Install AWS CLI and configure
aws configure

# Create ECS cluster
aws ecs create-cluster --cluster-name medprep-cluster

# Create task definitions
aws ecs register-task-definition --cli-input-json file://task-definition.json
```

2. **Database Setup**
```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier medprep-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username admin \
  --master-user-password ${DB_PASSWORD} \
  --allocated-storage 20
```

3. **Vector Database**
```bash
# Deploy Qdrant on EC2 or use Qdrant Cloud
# For EC2 deployment:
aws ec2 run-instances \
  --image-id ami-0abcdef1234567890 \
  --count 1 \
  --instance-type t3.small \
  --key-name my-key-pair \
  --security-groups qdrant-sg
```

#### Google Cloud Platform

**Using Google Cloud Run:**

```bash
# Build and push images
gcloud builds submit --tag gcr.io/PROJECT_ID/medprep-backend backend/
gcloud builds submit --tag gcr.io/PROJECT_ID/medprep-frontend frontend/

# Deploy backend
gcloud run deploy medprep-backend \
  --image gcr.io/PROJECT_ID/medprep-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated

# Deploy frontend
gcloud run deploy medprep-frontend \
  --image gcr.io/PROJECT_ID/medprep-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 🔧 Environment Configuration

### Production Environment Variables

Create `.env` file with production values:

```env
# Environment
ENVIRONMENT=production
DEBUG=false

# Database
DATABASE_URL=************************************/medprep
REDIS_URL=redis://host:6379

# Vector Database
QDRANT_URL=https://your-qdrant-cluster.qdrant.io
QDRANT_API_KEY=your_qdrant_api_key

# Security
SECRET_KEY=your_super_secret_key_here_64_chars_minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Services
OPENAI_API_KEY=sk-your_openai_key
COHERE_API_KEY=your_cohere_key
OPENROUTER_API_KEY=sk-or-your_openrouter_key

# File Storage
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=104857600

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Monitoring
SENTRY_DSN=https://your_sentry_dsn
```

### Frontend Environment Variables

Create `.env.production` in frontend directory:

```env
REACT_APP_API_URL=https://api.yourdomain.com
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=https://your_frontend_sentry_dsn
```

## 🔒 Security Configuration

### SSL/TLS Setup

**Using Let's Encrypt with Nginx:**

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Database Security

```sql
-- Create dedicated database user
CREATE USER medprep_app WITH PASSWORD 'strong_password';
GRANT CONNECT ON DATABASE medprep TO medprep_app;
GRANT USAGE ON SCHEMA public TO medprep_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO medprep_app;

-- Enable SSL
ALTER SYSTEM SET ssl = on;
SELECT pg_reload_conf();
```

## 📊 Monitoring & Logging

### Application Monitoring

**Using Sentry:**

```python
# In backend/app/main.py
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

sentry_sdk.init(
    dsn=settings.SENTRY_DSN,
    integrations=[FastApiIntegration()],
    traces_sample_rate=0.1,
)
```

### Health Checks

```yaml
# docker-compose.prod.yml
services:
  backend:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

### Log Management

```yaml
# docker-compose.prod.yml
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔄 CI/CD Pipeline

### GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          cd backend && python -m pytest
          cd ../frontend && npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          # Your deployment script here
          ./scripts/deploy.sh
```

## 🚨 Backup Strategy

### Database Backup

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### File Storage Backup

```bash
#!/bin/bash
# Backup uploads directory
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /app/uploads
aws s3 cp uploads_backup_$(date +%Y%m%d).tar.gz s3://your-backup-bucket/
```

## 📈 Performance Optimization

### Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX idx_chunks_book_id ON chunks(book_id);
CREATE INDEX idx_chunks_topic_category ON chunks(topic_category);
CREATE INDEX idx_search_history_user_id ON search_history(user_id);
CREATE INDEX idx_citations_answer_id ON citations(answer_id);
```

### Caching Strategy

```python
# Redis caching configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## 🔧 Maintenance

### Regular Tasks

```bash
# Weekly maintenance script
#!/bin/bash

# Update system packages
apt update && apt upgrade -y

# Clean up old logs
find /var/log -name "*.log" -mtime +30 -delete

# Database maintenance
psql $DATABASE_URL -c "VACUUM ANALYZE;"

# Clean up old backups
find /backups -name "*.sql" -mtime +30 -delete
```

### Scaling Considerations

- **Horizontal scaling**: Use load balancers for multiple backend instances
- **Database scaling**: Consider read replicas for heavy read workloads
- **Vector database scaling**: Qdrant supports clustering for large datasets
- **File storage**: Move to cloud storage (S3, GCS) for better scalability

## 🆘 Troubleshooting

### Common Issues

1. **High memory usage**: Increase container memory limits
2. **Slow search**: Check Qdrant performance and indexing
3. **Database connections**: Monitor connection pool usage
4. **File upload failures**: Check disk space and permissions

### Monitoring Commands

```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs -f backend

# Monitor resource usage
docker stats

# Database connections
psql $DATABASE_URL -c "SELECT count(*) FROM pg_stat_activity;"
```
