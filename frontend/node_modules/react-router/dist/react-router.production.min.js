/**
 * React Router v6.8.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import{invariant as e,joinPaths as t,matchPath as r,UNSAFE_getPathContributingMatches as n,resolveTo as a,parsePath as o,matchRoutes as i,Action as l,isRouteErrorResponse as s,createMemoryHistory as u,stripBasename as c,AbortedDeferredError as d,createRouter as p}from"@remix-run/router";export{AbortedDeferredError,Action as NavigationType,createPath,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,resolvePath}from"@remix-run/router";import*as m from"react";const h="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:f,useEffect:v,useLayoutEffect:g,useDebugValue:E}=m;function x(e){const t=e.getSnapshot,r=e.value;try{const e=t();return!h(r,e)}catch(n){return!0}}const C=!!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?function(e,t,r){return t()}:function(e,t,r){const n=t(),[{inst:a},o]=f({inst:{value:n,getSnapshot:t}});return g((()=>{a.value=n,a.getSnapshot=t,x(a)&&o({inst:a})}),[e,n,t]),v((()=>{x(a)&&o({inst:a});return e((()=>{x(a)&&o({inst:a})}))}),[e]),E(n),n},y="useSyncExternalStore"in m?m.useSyncExternalStore:C,b=m.createContext(null),R=m.createContext(null),S=m.createContext(null),P=m.createContext(null),U=m.createContext(null),D=m.createContext({outlet:null,matches:[]}),_=m.createContext(null);function k(r,{relative:n}={}){O()||e(!1);let{basename:a,navigator:o}=m.useContext(P),{hash:i,pathname:l,search:s}=T(r,{relative:n}),u=l;return"/"!==a&&(u="/"===l?a:t([a,l])),o.createHref({pathname:u,search:s,hash:i})}function O(){return null!=m.useContext(U)}function B(){return O()||e(!1),m.useContext(U).location}function L(){return m.useContext(U).navigationType}function j(t){O()||e(!1);let{pathname:n}=B();return m.useMemo((()=>r(t,n)),[n,t])}function N(){O()||e(!1);let{basename:r,navigator:o}=m.useContext(P),{matches:i}=m.useContext(D),{pathname:l}=B(),s=JSON.stringify(n(i).map((e=>e.pathnameBase))),u=m.useRef(!1);return m.useEffect((()=>{u.current=!0})),m.useCallback(((e,n={})=>{if(!u.current)return;if("number"==typeof e)return void o.go(e);let i=a(e,JSON.parse(s),l,"path"===n.relative);"/"!==r&&(i.pathname="/"===i.pathname?r:t([r,i.pathname])),(n.replace?o.replace:o.push)(i,n.state,n)}),[r,o,s,l])}const A=m.createContext(null);function F(){return m.useContext(A)}function w(e){let t=m.useContext(D).outlet;return t?m.createElement(A.Provider,{value:e},t):t}function M(){let{matches:e}=m.useContext(D),t=e[e.length-1];return t?t.params:{}}function T(e,{relative:t}={}){let{matches:r}=m.useContext(D),{pathname:o}=B(),i=JSON.stringify(n(r).map((e=>e.pathnameBase)));return m.useMemo((()=>a(e,JSON.parse(i),o,"path"===t)),[e,i,o,t])}function I(r,n){O()||e(!1);let{navigator:a}=m.useContext(P),s=m.useContext(R),{matches:u}=m.useContext(D),c=u[u.length-1],d=c?c.params:{};!c||c.pathname;let p=c?c.pathnameBase:"/";c&&c.route;let h,f=B();if(n){let t="string"==typeof n?o(n):n;"/"===p||t.pathname?.startsWith(p)||e(!1),h=t}else h=f;let v=h.pathname||"/",g="/"===p?v:v.slice(p.length)||"/",E=i(r,{pathname:g}),x=z(E&&E.map((e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:t([p,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:t([p,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),u,s||void 0);return n&&x?m.createElement(U.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:l.Pop}},x):x}function J(){let e=re(),t=s(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),r?m.createElement("pre",{style:n},r):null,null)}class H extends m.Component{constructor(e){super(e),this.state={location:e.location,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error,location:e.location}:{error:e.error||t.error,location:t.location}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error?m.createElement(D.Provider,{value:this.props.routeContext},m.createElement(_.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function $({routeContext:e,match:t,children:r}){let n=m.useContext(b);return n&&n.static&&n.staticContext&&t.route.errorElement&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),m.createElement(D.Provider,{value:e},r)}function z(t,r=[],n){if(null==t){if(!n?.errors)return null;t=n.matches}let a=t,o=n?.errors;if(null!=o){let t=a.findIndex((e=>e.route.id&&o?.[e.route.id]));t>=0||e(!1),a=a.slice(0,Math.min(a.length,t+1))}return a.reduceRight(((e,t,i)=>{let l=t.route.id?o?.[t.route.id]:null,s=n?t.route.errorElement||m.createElement(J,null):null,u=r.concat(a.slice(0,i+1)),c=()=>m.createElement($,{match:t,routeContext:{outlet:e,matches:u}},l?s:void 0!==t.route.element?t.route.element:e);return n&&(t.route.errorElement||0===i)?m.createElement(H,{location:n.location,component:s,error:l,children:c(),routeContext:{outlet:null,matches:u}}):c()}),null)}var V,W;function Y(t){let r=m.useContext(b);return r||e(!1),r}function q(t){let r=m.useContext(R);return r||e(!1),r}function G(t){let r=function(t){let r=m.useContext(D);return r||e(!1),r}(),n=r.matches[r.matches.length-1];return n.route.id||e(!1),n.route.id}function K(){return q(W.UseNavigation).navigation}function Q(){let e=Y(V.UseRevalidator),t=q(W.UseRevalidator);return{revalidate:e.router.revalidate,state:t.revalidation}}function X(){let{matches:e,loaderData:t}=q(W.UseMatches);return m.useMemo((()=>e.map((e=>{let{pathname:r,params:n}=e;return{id:e.route.id,pathname:r,params:n,data:t[e.route.id],handle:e.route.handle}}))),[e,t])}function Z(){let e=q(W.UseLoaderData),t=G(W.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error(`You cannot \`useLoaderData\` in an errorElement (routeId: ${t})`)}function ee(e){return q(W.UseRouteLoaderData).loaderData[e]}function te(){let t=q(W.UseActionData);return m.useContext(D)||e(!1),Object.values(t?.actionData||{})[0]}function re(){let e=m.useContext(_),t=q(W.UseRouteError),r=G(W.UseRouteError);return e||t.errors?.[r]}function ne(){return m.useContext(S)?._data}function ae(){return m.useContext(S)?._error}!function(e){e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator"}(V||(V={})),function(e){e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator"}(W||(W={}));let oe=0;function ie(e){let{router:t}=Y(V.UseBlocker),[r]=m.useState((()=>String(++oe))),n=m.useCallback((t=>"function"==typeof e?!!e(t):!!e),[e]),a=t.getBlocker(r,n);return m.useEffect((()=>()=>t.deleteBlocker(r)),[t,r]),a}function le({fallbackElement:e,router:t}){let r=y(t.subscribe,(()=>t.state),(()=>t.state)),n=m.useMemo((()=>({createHref:t.createHref,encodeLocation:t.encodeLocation,go:e=>t.navigate(e),push:(e,r,n)=>t.navigate(e,{state:r,preventScrollReset:n?.preventScrollReset}),replace:(e,r,n)=>t.navigate(e,{replace:!0,state:r,preventScrollReset:n?.preventScrollReset})})),[t]),a=t.basename||"/";return m.createElement(m.Fragment,null,m.createElement(b.Provider,{value:{router:t,navigator:n,static:!1,basename:a}},m.createElement(R.Provider,{value:r},m.createElement(pe,{basename:t.basename,location:t.state.location,navigationType:t.state.historyAction,navigator:n},t.state.initialized?m.createElement(me,null):e))),null)}function se({basename:e,children:t,initialEntries:r,initialIndex:n}){let a=m.useRef();null==a.current&&(a.current=u({initialEntries:r,initialIndex:n,v5Compat:!0}));let o=a.current,[i,l]=m.useState({action:o.action,location:o.location});return m.useLayoutEffect((()=>o.listen(l)),[o]),m.createElement(pe,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}function ue({to:t,replace:r,state:n,relative:a}){O()||e(!1);let o=m.useContext(R),i=N();return m.useEffect((()=>{o&&"idle"!==o.navigation.state||i(t,{replace:r,state:n,relative:a})})),null}function ce(e){return w(e.context)}function de(t){e(!1)}function pe({basename:t="/",children:r=null,location:n,navigationType:a=l.Pop,navigator:i,static:s=!1}){O()&&e(!1);let u=t.replace(/^\/*/,"/"),d=m.useMemo((()=>({basename:u,navigator:i,static:s})),[u,i,s]);"string"==typeof n&&(n=o(n));let{pathname:p="/",search:h="",hash:f="",state:v=null,key:g="default"}=n,E=m.useMemo((()=>{let e=c(p,u);return null==e?null:{pathname:e,search:h,hash:f,state:v,key:g}}),[u,p,h,f,v,g]);return null==E?null:m.createElement(P.Provider,{value:d},m.createElement(U.Provider,{children:r,value:{location:E,navigationType:a}}))}function me({children:e,location:t}){let r=m.useContext(b);return I(r&&!e?r.router.routes:xe(e),t)}function he({children:e,errorElement:t,resolve:r}){return m.createElement(ge,{resolve:r,errorElement:t},m.createElement(Ee,null,e))}var fe;!function(e){e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error"}(fe||(fe={}));const ve=new Promise((()=>{}));class ge extends m.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:e,errorElement:t,resolve:r}=this.props,n=null,a=fe.pending;if(r instanceof Promise)if(this.state.error){a=fe.error;let e=this.state.error;n=Promise.reject().catch((()=>{})),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_error",{get:()=>e})}else r._tracked?(n=r,a=void 0!==n._error?fe.error:void 0!==n._data?fe.success:fe.pending):(a=fe.pending,Object.defineProperty(r,"_tracked",{get:()=>!0}),n=r.then((e=>Object.defineProperty(r,"_data",{get:()=>e})),(e=>Object.defineProperty(r,"_error",{get:()=>e}))));else a=fe.success,n=Promise.resolve(),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_data",{get:()=>r});if(a===fe.error&&n._error instanceof d)throw ve;if(a===fe.error&&!t)throw n._error;if(a===fe.error)return m.createElement(S.Provider,{value:n,children:t});if(a===fe.success)return m.createElement(S.Provider,{value:n,children:e});throw n}}function Ee({children:e}){let t=ne(),r="function"==typeof e?e(t):e;return m.createElement(m.Fragment,null,r)}function xe(t,r=[]){let n=[];return m.Children.forEach(t,((t,a)=>{if(!m.isValidElement(t))return;if(t.type===m.Fragment)return void n.push.apply(n,xe(t.props.children,r));t.type!==de&&e(!1),t.props.index&&t.props.children&&e(!1);let o=[...r,a],i={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,hasErrorBoundary:null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle};t.props.children&&(i.children=xe(t.props.children,o)),n.push(i)})),n}function Ce(e){return z(e)}function ye(e){return e.map((e=>{let t={...e};return null==t.hasErrorBoundary&&(t.hasErrorBoundary=null!=t.errorElement),t.children&&(t.children=ye(t.children)),t}))}function be(e,t){return p({basename:t?.basename,history:u({initialEntries:t?.initialEntries,initialIndex:t?.initialIndex}),hydrationData:t?.hydrationData,routes:ye(e)}).initialize()}export{he as Await,se as MemoryRouter,ue as Navigate,ce as Outlet,de as Route,pe as Router,le as RouterProvider,me as Routes,b as UNSAFE_DataRouterContext,R as UNSAFE_DataRouterStateContext,U as UNSAFE_LocationContext,P as UNSAFE_NavigationContext,D as UNSAFE_RouteContext,ye as UNSAFE_enhanceManualRouteObjects,be as createMemoryRouter,xe as createRoutesFromChildren,xe as createRoutesFromElements,Ce as renderMatches,ie as unstable_useBlocker,te as useActionData,ae as useAsyncError,ne as useAsyncValue,k as useHref,O as useInRouterContext,Z as useLoaderData,B as useLocation,j as useMatch,X as useMatches,N as useNavigate,K as useNavigation,L as useNavigationType,w as useOutlet,F as useOutletContext,M as useParams,T as useResolvedPath,Q as useRevalidator,re as useRouteError,ee as useRouteLoaderData,I as useRoutes};
//# sourceMappingURL=react-router.production.min.js.map
