/**
 * React Router v6.8.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@remix-run/router"),require("react")):"function"==typeof define&&define.amd?define(["exports","@remix-run/router","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactRouter={},e.RemixRouter,e.React)}(this,(function(e,t,r){"use strict";function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=n(r);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(this,arguments)}const i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:u,useEffect:l,useLayoutEffect:s,useDebugValue:c}=a;function d(e){const t=e.getSnapshot,r=e.value;try{const e=t();return!i(r,e)}catch(e){return!0}}const p=!!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?function(e,t,r){return t()}:function(e,t,r){const n=t(),[{inst:a},o]=u({inst:{value:n,getSnapshot:t}});return s((()=>{a.value=n,a.getSnapshot=t,d(a)&&o({inst:a})}),[e,n,t]),l((()=>{d(a)&&o({inst:a});return e((()=>{d(a)&&o({inst:a})}))}),[e]),c(n),n},h="useSyncExternalStore"in a?(e=>e.useSyncExternalStore)(a):p,f=a.createContext(null),m=a.createContext(null),v=a.createContext(null),g=a.createContext(null),E=a.createContext(null),b=a.createContext({outlet:null,matches:[]}),y=a.createContext(null);function x(){return null!=a.useContext(E)}function P(){return x()||t.invariant(!1),a.useContext(E).location}function R(){x()||t.invariant(!1);let{basename:e,navigator:r}=a.useContext(g),{matches:n}=a.useContext(b),{pathname:o}=P(),i=JSON.stringify(t.UNSAFE_getPathContributingMatches(n).map((e=>e.pathnameBase))),u=a.useRef(!1);return a.useEffect((()=>{u.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!u.current)return;if("number"==typeof n)return void r.go(n);let l=t.resolveTo(n,JSON.parse(i),o,"path"===a.relative);"/"!==e&&(l.pathname="/"===l.pathname?e:t.joinPaths([e,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)}),[e,r,i,o])}const C=a.createContext(null);function O(e){let t=a.useContext(b).outlet;return t?a.createElement(C.Provider,{value:e},t):t}function j(e,r){let{relative:n}=void 0===r?{}:r,{matches:o}=a.useContext(b),{pathname:i}=P(),u=JSON.stringify(t.UNSAFE_getPathContributingMatches(o).map((e=>e.pathnameBase)));return a.useMemo((()=>t.resolveTo(e,JSON.parse(u),i,"path"===n)),[e,u,i,n])}function S(e,r){x()||t.invariant(!1);let{navigator:n}=a.useContext(g),i=a.useContext(m),{matches:u}=a.useContext(b),l=u[u.length-1],s=l?l.params:{};!l||l.pathname;let c=l?l.pathnameBase:"/";l&&l.route;let d,p=P();if(r){var h;let e="string"==typeof r?t.parsePath(r):r;"/"===c||(null==(h=e.pathname)?void 0:h.startsWith(c))||t.invariant(!1),d=e}else d=p;let f=d.pathname||"/",v="/"===c?f:f.slice(c.length)||"/",y=t.matchRoutes(e,{pathname:v}),R=A(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:t.joinPaths([c,n.encodeLocation?n.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:t.joinPaths([c,n.encodeLocation?n.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),u,i||void 0);return r&&R?a.createElement(E.Provider,{value:{location:o({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:t.Action.Pop}},R):R}function D(){let e=F(),r=t.isRouteErrorResponse(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},r),n?a.createElement("pre",{style:o},n):null,null)}class U extends a.Component{constructor(e){super(e),this.state={location:e.location,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error,location:e.location}:{error:e.error||t.error,location:t.location}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error?a.createElement(b.Provider,{value:this.props.routeContext},a.createElement(y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function _(e){let{routeContext:t,match:r,children:n}=e,o=a.useContext(f);return o&&o.static&&o.staticContext&&r.route.errorElement&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),a.createElement(b.Provider,{value:t},n)}function A(e,r,n){if(void 0===r&&(r=[]),null==e){if(null==n||!n.errors)return null;e=n.matches}let o=e,i=null==n?void 0:n.errors;if(null!=i){let e=o.findIndex((e=>e.route.id&&(null==i?void 0:i[e.route.id])));e>=0||t.invariant(!1),o=o.slice(0,Math.min(o.length,e+1))}return o.reduceRight(((e,t,u)=>{let l=t.route.id?null==i?void 0:i[t.route.id]:null,s=n?t.route.errorElement||a.createElement(D,null):null,c=r.concat(o.slice(0,u+1)),d=()=>a.createElement(_,{match:t,routeContext:{outlet:e,matches:c}},l?s:void 0!==t.route.element?t.route.element:e);return n&&(t.route.errorElement||0===u)?a.createElement(U,{location:n.location,component:s,error:l,children:d(),routeContext:{outlet:null,matches:c}}):d()}),null)}var M,N;function L(e){let r=a.useContext(f);return r||t.invariant(!1),r}function k(e){let r=a.useContext(m);return r||t.invariant(!1),r}function B(e){let r=function(e){let r=a.useContext(b);return r||t.invariant(!1),r}(),n=r.matches[r.matches.length-1];return n.route.id||t.invariant(!1),n.route.id}function F(){var e;let t=a.useContext(y),r=k(N.UseRouteError),n=B(N.UseRouteError);return t||(null==(e=r.errors)?void 0:e[n])}function w(){let e=a.useContext(v);return null==e?void 0:e._data}!function(e){e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator"}(M||(M={})),function(e){e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator"}(N||(N={}));let T=0;function I(e){t.invariant(!1)}function H(e){let{basename:r="/",children:n=null,location:o,navigationType:i=t.Action.Pop,navigator:u,static:l=!1}=e;x()&&t.invariant(!1);let s=r.replace(/^\/*/,"/"),c=a.useMemo((()=>({basename:s,navigator:u,static:l})),[s,u,l]);"string"==typeof o&&(o=t.parsePath(o));let{pathname:d="/",search:p="",hash:h="",state:f=null,key:m="default"}=o,v=a.useMemo((()=>{let e=t.stripBasename(d,s);return null==e?null:{pathname:e,search:p,hash:h,state:f,key:m}}),[s,d,p,h,f,m]);return null==v?null:a.createElement(g.Provider,{value:c},a.createElement(E.Provider,{children:n,value:{location:v,navigationType:i}}))}function J(e){let{children:t,location:r}=e,n=a.useContext(f);return S(n&&!t?n.router.routes:Y(t),r)}var z;!function(e){e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error"}(z||(z={}));const V=new Promise((()=>{}));class q extends a.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:e,errorElement:r,resolve:n}=this.props,o=null,i=z.pending;if(n instanceof Promise)if(this.state.error){i=z.error;let e=this.state.error;o=Promise.reject().catch((()=>{})),Object.defineProperty(o,"_tracked",{get:()=>!0}),Object.defineProperty(o,"_error",{get:()=>e})}else n._tracked?(o=n,i=void 0!==o._error?z.error:void 0!==o._data?z.success:z.pending):(i=z.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),o=n.then((e=>Object.defineProperty(n,"_data",{get:()=>e})),(e=>Object.defineProperty(n,"_error",{get:()=>e}))));else i=z.success,o=Promise.resolve(),Object.defineProperty(o,"_tracked",{get:()=>!0}),Object.defineProperty(o,"_data",{get:()=>n});if(i===z.error&&o._error instanceof t.AbortedDeferredError)throw V;if(i===z.error&&!r)throw o._error;if(i===z.error)return a.createElement(v.Provider,{value:o,children:r});if(i===z.success)return a.createElement(v.Provider,{value:o,children:e});throw o}}function W(e){let{children:t}=e,r=w(),n="function"==typeof t?t(r):t;return a.createElement(a.Fragment,null,n)}function Y(e,r){void 0===r&&(r=[]);let n=[];return a.Children.forEach(e,((e,o)=>{if(!a.isValidElement(e))return;if(e.type===a.Fragment)return void n.push.apply(n,Y(e.props.children,r));e.type!==I&&t.invariant(!1),e.props.index&&e.props.children&&t.invariant(!1);let i=[...r,o],u={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,hasErrorBoundary:null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle};e.props.children&&(u.children=Y(e.props.children,i)),n.push(u)})),n}function G(e){return e.map((e=>{let t=o({},e);return null==t.hasErrorBoundary&&(t.hasErrorBoundary=null!=t.errorElement),t.children&&(t.children=G(t.children)),t}))}Object.defineProperty(e,"AbortedDeferredError",{enumerable:!0,get:function(){return t.AbortedDeferredError}}),Object.defineProperty(e,"NavigationType",{enumerable:!0,get:function(){return t.Action}}),Object.defineProperty(e,"createPath",{enumerable:!0,get:function(){return t.createPath}}),Object.defineProperty(e,"defer",{enumerable:!0,get:function(){return t.defer}}),Object.defineProperty(e,"generatePath",{enumerable:!0,get:function(){return t.generatePath}}),Object.defineProperty(e,"isRouteErrorResponse",{enumerable:!0,get:function(){return t.isRouteErrorResponse}}),Object.defineProperty(e,"json",{enumerable:!0,get:function(){return t.json}}),Object.defineProperty(e,"matchPath",{enumerable:!0,get:function(){return t.matchPath}}),Object.defineProperty(e,"matchRoutes",{enumerable:!0,get:function(){return t.matchRoutes}}),Object.defineProperty(e,"parsePath",{enumerable:!0,get:function(){return t.parsePath}}),Object.defineProperty(e,"redirect",{enumerable:!0,get:function(){return t.redirect}}),Object.defineProperty(e,"resolvePath",{enumerable:!0,get:function(){return t.resolvePath}}),e.Await=function(e){let{children:t,errorElement:r,resolve:n}=e;return a.createElement(q,{resolve:n,errorElement:r},a.createElement(W,null,t))},e.MemoryRouter=function(e){let{basename:r,children:n,initialEntries:o,initialIndex:i}=e,u=a.useRef();null==u.current&&(u.current=t.createMemoryHistory({initialEntries:o,initialIndex:i,v5Compat:!0}));let l=u.current,[s,c]=a.useState({action:l.action,location:l.location});return a.useLayoutEffect((()=>l.listen(c)),[l]),a.createElement(H,{basename:r,children:n,location:s.location,navigationType:s.action,navigator:l})},e.Navigate=function(e){let{to:r,replace:n,state:o,relative:i}=e;x()||t.invariant(!1);let u=a.useContext(m),l=R();return a.useEffect((()=>{u&&"idle"!==u.navigation.state||l(r,{replace:n,state:o,relative:i})})),null},e.Outlet=function(e){return O(e.context)},e.Route=I,e.Router=H,e.RouterProvider=function(e){let{fallbackElement:t,router:r}=e,n=h(r.subscribe,(()=>r.state),(()=>r.state)),o=a.useMemo((()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,n)=>r.navigate(e,{state:t,preventScrollReset:null==n?void 0:n.preventScrollReset}),replace:(e,t,n)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:null==n?void 0:n.preventScrollReset})})),[r]),i=r.basename||"/";return a.createElement(a.Fragment,null,a.createElement(f.Provider,{value:{router:r,navigator:o,static:!1,basename:i}},a.createElement(m.Provider,{value:n},a.createElement(H,{basename:r.basename,location:r.state.location,navigationType:r.state.historyAction,navigator:o},r.state.initialized?a.createElement(J,null):t))),null)},e.Routes=J,e.UNSAFE_DataRouterContext=f,e.UNSAFE_DataRouterStateContext=m,e.UNSAFE_LocationContext=E,e.UNSAFE_NavigationContext=g,e.UNSAFE_RouteContext=b,e.UNSAFE_enhanceManualRouteObjects=G,e.createMemoryRouter=function(e,r){return t.createRouter({basename:null==r?void 0:r.basename,history:t.createMemoryHistory({initialEntries:null==r?void 0:r.initialEntries,initialIndex:null==r?void 0:r.initialIndex}),hydrationData:null==r?void 0:r.hydrationData,routes:G(e)}).initialize()},e.createRoutesFromChildren=Y,e.createRoutesFromElements=Y,e.renderMatches=function(e){return A(e)},e.unstable_useBlocker=function(e){let{router:t}=L(M.UseBlocker),[r]=a.useState((()=>String(++T))),n=a.useCallback((t=>"function"==typeof e?!!e(t):!!e),[e]),o=t.getBlocker(r,n);return a.useEffect((()=>()=>t.deleteBlocker(r)),[t,r]),o},e.useActionData=function(){let e=k(N.UseActionData);return a.useContext(b)||t.invariant(!1),Object.values((null==e?void 0:e.actionData)||{})[0]},e.useAsyncError=function(){let e=a.useContext(v);return null==e?void 0:e._error},e.useAsyncValue=w,e.useHref=function(e,r){let{relative:n}=void 0===r?{}:r;x()||t.invariant(!1);let{basename:o,navigator:i}=a.useContext(g),{hash:u,pathname:l,search:s}=j(e,{relative:n}),c=l;return"/"!==o&&(c="/"===l?o:t.joinPaths([o,l])),i.createHref({pathname:c,search:s,hash:u})},e.useInRouterContext=x,e.useLoaderData=function(){let e=k(N.UseLoaderData),t=B(N.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error("You cannot `useLoaderData` in an errorElement (routeId: "+t+")")},e.useLocation=P,e.useMatch=function(e){x()||t.invariant(!1);let{pathname:r}=P();return a.useMemo((()=>t.matchPath(e,r)),[r,e])},e.useMatches=function(){let{matches:e,loaderData:t}=k(N.UseMatches);return a.useMemo((()=>e.map((e=>{let{pathname:r,params:n}=e;return{id:e.route.id,pathname:r,params:n,data:t[e.route.id],handle:e.route.handle}}))),[e,t])},e.useNavigate=R,e.useNavigation=function(){return k(N.UseNavigation).navigation},e.useNavigationType=function(){return a.useContext(E).navigationType},e.useOutlet=O,e.useOutletContext=function(){return a.useContext(C)},e.useParams=function(){let{matches:e}=a.useContext(b),t=e[e.length-1];return t?t.params:{}},e.useResolvedPath=j,e.useRevalidator=function(){let e=L(M.UseRevalidator),t=k(N.UseRevalidator);return{revalidate:e.router.revalidate,state:t.revalidation}},e.useRouteError=F,e.useRouteLoaderData=function(e){return k(N.UseRouteLoaderData).loaderData[e]},e.useRoutes=S,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-router.production.min.js.map
