{"version": 3, "file": "react-router.production.min.js", "sources": ["../../lib/use-sync-external-store-shim/useSyncExternalStoreShimClient.ts", "../../lib/use-sync-external-store-shim/index.ts", "../../lib/use-sync-external-store-shim/useSyncExternalStoreShimServer.ts", "../../lib/context.ts", "../../lib/hooks.tsx", "../../lib/components.tsx", "../../index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as React from \"react\";\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction isPolyfill(x: any, y: any) {\n  return (\n    (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y) // eslint-disable-line no-self-compare\n  );\n}\n\nconst is: (x: any, y: any) => boolean =\n  typeof Object.is === \"function\" ? Object.is : isPolyfill;\n\n// Intentionally not using named imports because Roll<PERSON> uses dynamic\n// dispatch for CommonJS interop named imports.\nconst { useState, useEffect, useLayoutEffect, useDebugValue } = React;\n\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false;\n\n// Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  getServerSnapshot?: () => T\n): T {\n  if (__DEV__) {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that \" +\n            \"does not support useSyncExternalStore. The \" +\n            \"use-sync-external-store shim will not work correctly. Upgrade \" +\n            \"to a newer pre-release.\"\n        );\n      }\n    }\n  }\n\n  // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n  const value = getSnapshot();\n  if (__DEV__) {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n      if (!is(value, cachedValue)) {\n        console.error(\n          \"The result of getSnapshot should be cached to avoid an infinite loop\"\n        );\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  }\n\n  // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n  const [{ inst }, forceUpdate] = useState({ inst: { value, getSnapshot } });\n\n  // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot;\n\n    // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe, value, getSnapshot]);\n\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({ inst });\n      }\n    };\n    // Subscribe to the store and return a clean-up function.\n    return subscribe(handleStoreChange);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst: any) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n", "/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport * as React from \"react\";\n\nimport { useSyncExternalStore as client } from \"./useSyncExternalStoreShimClient\";\nimport { useSyncExternalStore as server } from \"./useSyncExternalStoreShimServer\";\n\nconst canUseDOM: boolean = !!(\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\"\n);\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? server : client;\n\nexport const useSyncExternalStore =\n  \"useSyncExternalStore\" in React\n    ? ((module) => module.useSyncExternalStore)(React)\n    : shim;\n", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  getServerSnapshot?: () => T\n): T {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  History,\n  Location,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport type { Action as NavigationType } from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject extends NavigationContextObject {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  Router as RemixRouter,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  warning,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n} from \"@remix-run/router\";\n\nimport type {\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n  DataRouteMatch,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n  AwaitContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history.  If this is a root navigation, then we\n      // navigate to the raw basename which allows the basename to have full\n      // control over the presence of a trailing slash on root links\n      if (basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterStateContext || undefined\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own&nbsp;\n          <code style={codeStyles}>errorElement</code> props on&nbsp;\n          <code style={codeStyles}>&lt;Route&gt;</code>\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    match.route.errorElement\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState?: RemixRouter[\"state\"]\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for the current errors: ${errors}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors?.[match.route.id] : null;\n    // Only data routers handle errors\n    let errorElement = dataRouterState\n      ? match.route.errorElement || <DefaultErrorElement />\n      : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => (\n      <RenderedRoute match={match} routeContext={{ outlet, matches }}>\n        {error\n          ? errorElement\n          : match.route.element !== undefined\n          ? match.route.element\n          : outlet}\n      </RenderedRoute>\n    );\n    // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n    return dataRouterState && (match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nenum DataRouterStateHook {\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation,\n  };\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () =>\n      matches.map((match) => {\n        let { pathname, params } = match;\n        // Note: This structure matches that created by createUseMatchesMatch\n        // in the @remix-run/router , so if you change this please also change\n        // that :)  Eventually we'll DRY this up\n        return {\n          id: match.route.id,\n          pathname,\n          params,\n          data: loaderData[match.route.id] as unknown,\n          handle: match.route.handle as unknown,\n        };\n      }),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useActionData must be used inside a RouteContext`);\n\n  return Object.values(state?.actionData || {})[0];\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (args) => {\n      return typeof shouldBlock === \"function\"\n        ? !!shouldBlock(args)\n        : !!shouldBlock;\n    },\n    [shouldBlock]\n  );\n\n  let blocker = router.getBlocker(blockerKey, blockerFunction);\n\n  // Cleanup on unmount\n  React.useEffect(\n    () => () => router.deleteBlocker(blockerKey),\n    [router, blockerKey]\n  );\n\n  return blocker;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import * as React from \"react\";\nimport type {\n  TrackedPromise,\n  InitialEntry,\n  Location,\n  MemoryHistory,\n  Router as RemixRouter,\n  RouterState,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  AbortedDeferredError,\n  createMemoryHistory,\n  invariant,\n  parsePath,\n  stripBasename,\n  warning,\n} from \"@remix-run/router\";\nimport { useSyncExternalStore as useSyncExternalStoreShim } from \"./use-sync-external-store-shim\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  LocationContext,\n  NavigationContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  AwaitContext,\n} from \"./context\";\nimport {\n  useAsyncValue,\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n}: RouterProviderProps): React.ReactElement {\n  // Sync router state to our component state to force re-renders\n  let state: RouterState = useSyncExternalStoreShim(\n    router.subscribe,\n    () => router.state,\n    // We have to provide this so React@18 doesn't complain during hydration,\n    // but we pass our serialized hydration data into the router so state here\n    // is already synced with what the server saw\n    () => router.state\n  );\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider\n        value={{\n          router,\n          navigator,\n          static: false,\n          // Do we need this?\n          basename,\n        }}\n      >\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={router.basename}\n            location={router.state.location}\n            navigationType={router.state.historyAction}\n            navigator={navigator}\n          >\n            {router.state.initialized ? <Routes /> : fallbackElement}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n    navigate(to, { replace, state, relative });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  let dataRouterContext = React.useContext(DataRouterContext);\n  // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n  let routes =\n    dataRouterContext && !children\n      ? (dataRouterContext.router.routes as DataRouteObject[])\n      : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, parentPath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let treePath = [...parentPath, index];\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\nexport function enhanceManualRouteObjects(\n  routes: RouteObject[]\n): RouteObject[] {\n  return routes.map((route) => {\n    let routeClone = { ...route };\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n    return routeClone;\n  });\n}\n", "import type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  Fetcher,\n  HydrationState,\n  JsonFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  PathPattern,\n  RedirectFunction,\n  Router as RemixRouter,\n  ShouldRevalidateFunction,\n  To,\n  InitialEntry,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  resolvePath,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  RoutesProps,\n  RouterProviderProps,\n} from \"./lib/components\";\nimport {\n  enhanceManualRouteObjects,\n  createRoutesFromChildren,\n  renderMatches,\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NavigateOptions,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  RelativeRoutingType,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigationType,\n  useNavigate,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useLoaderData,\n  useMatches,\n  useNavigation,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker as unstable_Blocker,\n  BlockerFunction as unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker as unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  NavigationContext as UNSAFE_NavigationContext,\n  LocationContext as UNSAFE_LocationContext,\n  RouteContext as UNSAFE_RouteContext,\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects,\n};\n"], "names": ["is", "Object", "x", "y", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "React", "checkIfSnapshotChanged", "inst", "latestGetSnapshot", "getSnapshot", "prevValue", "value", "nextValue", "error", "shim", "window", "document", "createElement", "subscribe", "getServerSnapshot", "forceUpdate", "useSyncExternalStore", "module", "DataRouterContext", "createContext", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "RouteErrorContext", "useInRouterContext", "useContext", "useLocation", "invariant", "location", "useNavigate", "basename", "navigator", "pathname", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getPathContributingMatches", "map", "match", "pathnameBase", "activeRef", "useRef", "current", "useCallback", "to", "options", "go", "path", "resolveTo", "parse", "relative", "joinPaths", "replace", "push", "state", "OutletContext", "useOutlet", "context", "Provider", "useResolvedPath", "_temp2", "useMemo", "useRoutes", "routes", "locationArg", "dataRouterStateContext", "parentMatches", "routeMatch", "length", "parentParams", "params", "parentPathnameBase", "route", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "slice", "matchRoutes", "renderedMatches", "_renderMatches", "assign", "encodeLocation", "undefined", "_extends", "search", "hash", "key", "navigationType", "NavigationType", "Action", "Pop", "DefaultErrorElement", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "preStyles", "padding", "backgroundColor", "Fragment", "style", "fontStyle", "RenderErrorBoundary", "Component", "constructor", "props", "super", "this", "static", "componentDidCatch", "errorInfo", "console", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "dataRouterContext", "staticContext", "errorElement", "_deepestRenderedBoundaryId", "id", "dataRouterState", "errors", "errorIndex", "findIndex", "m", "Math", "min", "reduceRight", "index", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "element", "DataRouterHook", "DataRouterStateHook", "useDataRouterContext", "<PERSON><PERSON><PERSON>", "ctx", "useDataRouterState", "useCurrentRouteId", "useRouteContext", "thisRoute", "_state$errors", "UseRouteError", "routeId", "useAsyncValue", "_data", "blockerId", "Route", "_props", "Router", "_ref4", "basenameProp", "locationProp", "staticProp", "navigationContext", "trailingPathname", "stripBasename", "Routes", "_ref5", "router", "createRoutesFromChildren", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "resolve", "promise", "pending", "renderError", "reject", "catch", "defineProperty", "get", "_tracked", "_error", "success", "then", "data", "Aborted<PERSON>eferredError", "ResolveAwait", "_ref7", "to<PERSON><PERSON>", "parentPath", "Children", "for<PERSON>ach", "isValidElement", "type", "apply", "treePath", "join", "caseSensitive", "loader", "action", "hasErrorBou<PERSON>ry", "shouldRevalidate", "handle", "enhanceManualRouteObjects", "routeClone", "_ref6", "_ref2", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "setState", "listen", "_ref3", "navigate", "navigation", "fallbackElement", "useSyncExternalStoreShim", "createHref", "n", "opts", "preventScrollReset", "historyAction", "initialized", "createRouter", "hydrationData", "initialize", "shouldBlock", "UseBlocker", "blockerKey", "String", "blockerFunction", "args", "blocker", "get<PERSON><PERSON>er", "deleteBlocker", "UseActionData", "values", "actionData", "_temp", "joinedPathname", "UseLoaderData", "loaderData", "pattern", "matchPath", "UseMatches", "UseNavigation", "UseRevalidator", "revalidate", "revalidation", "UseRouteLoaderData"], "mappings": ";;;;;;;;;;q2BAmBA,MAAMA,EACiB,mBAAdC,OAAOD,GAAoBC,OAAOD,GAP3C,SAAoBE,EAAQC,GAC1B,OACGD,IAAMC,IAAY,IAAND,GAAW,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,CAElE,GAOKC,SAAEA,EAAFC,UAAYA,EAAZC,gBAAuBA,EAAvBC,cAAwCA,GAAkBC,EAsHhE,SAASC,EAAuBC,GAC9B,MAAMC,EAAoBD,EAAKE,YACzBC,EAAYH,EAAKI,MACvB,IACE,MAAMC,EAAYJ,IAClB,OAAQX,EAAGa,EAAWE,EAGvB,CAFC,MAAOC,GACP,OAAO,CACR,CACF,CCnID,MAMMC,KALc,oBAAXC,aACoB,IAApBA,OAAOC,eAC2B,IAAlCD,OAAOC,SAASC,eCdlB,SACLC,EACAT,EACAU,GAMA,OAAOV,GACR,EFoBM,SACLS,EACAT,EAKAU,GAoBA,MAAMR,EAAQF,MA2BPF,KAAEA,GAAQa,GAAenB,EAAS,CAAEM,KAAM,CAAEI,QAAOF,iBA8C1D,OAzCAN,GAAgB,KACdI,EAAKI,MAAQA,EACbJ,EAAKE,YAAcA,EAMfH,EAAuBC,IAEzBa,EAAY,CAAEb,QAVI,GAanB,CAACW,EAAWP,EAAOF,IAEtBP,GAAU,KAGJI,EAAuBC,IAEzBa,EAAY,CAAEb,SAgBhB,OAAOW,GAdmB,KAQpBZ,EAAuBC,IAEzBa,EAAY,CAAEb,QACf,GAGH,GAEC,CAACW,IAEJd,EAAcO,GACPA,CACR,EChHYU,EACX,yBAA0BhB,EACtB,CAAEiB,GAAWA,EAAOD,qBAApB,CAA0ChB,GAC1CS,EEkCOS,EACXlB,EAAMmB,cAA8C,MAKzCC,EAAyBpB,EAAMmB,cAE1C,MAKWE,EAAerB,EAAMmB,cAAqC,MAsC1DG,EAAoBtB,EAAMmB,cACrC,MAYWI,EAAkBvB,EAAMmB,cACnC,MAYWK,EAAexB,EAAMmB,cAAkC,CAClEM,OAAQ,KACRC,QAAS,KAOEC,EAAoB3B,EAAMmB,cAAmB,MCpEnD,SAASS,IACd,OAA4C,MAArC5B,EAAM6B,WAAWN,EACzB,CAYM,SAASO,IAQd,OANEF,KADFG,EAAAA,WAAA,GAOO/B,EAAM6B,WAAWN,GAAiBS,QAC1C,CAmDM,SAASC,IAEZL,KADFG,EAAAA,WAAA,GAOA,IAAIG,SAAEA,EAAFC,UAAYA,GAAcnC,EAAM6B,WAAWP,IAC3CI,QAAEA,GAAY1B,EAAM6B,WAAWL,IAC7BY,SAAUC,GAAqBP,IAEjCQ,EAAqBC,KAAKC,UAC5BC,oCAA2Bf,GAASgB,KAAKC,GAAUA,EAAMC,gBAGvDC,EAAY7C,EAAM8C,QAAO,GA+C7B,OA9CA9C,EAAMH,WAAU,KACdgD,EAAUE,SAAU,CAApB,IAG+B/C,EAAMgD,aACrC,SAACC,EAAiBC,GAOhB,QAPkD,IAAlCA,IAAAA,EAA2B,CAAA,IAOtCL,EAAUE,QAAS,OAExB,GAAkB,iBAAPE,EAET,YADAd,EAAUgB,GAAGF,GAIf,IAAIG,EAAOC,EAAAA,UACTJ,EACAV,KAAKe,MAAMhB,GACXD,EACqB,SAArBa,EAAQK,UAOO,MAAbrB,IACFkB,EAAKhB,SACe,MAAlBgB,EAAKhB,SACDF,EACAsB,EAASA,UAAC,CAACtB,EAAUkB,EAAKhB,aAG/Bc,EAAQO,QAAUtB,EAAUsB,QAAUtB,EAAUuB,MACjDN,EACAF,EAAQS,MACRT,EApC2B,GAuC/B,CAAChB,EAAUC,EAAWG,EAAoBD,GAI7C,CAED,MAAMuB,EAAgB5D,EAAMmB,cAAuB,MAiB5C,SAAS0C,EAAUC,GACxB,IAAIrC,EAASzB,EAAM6B,WAAWL,GAAcC,OAC5C,OAAIA,EAEAzB,EAAAY,cAACgD,EAAcG,SAAf,CAAwBzD,MAAOwD,GAAUrC,GAGtCA,CACR,CAuBM,SAASuC,EACdf,EAEMgB,GAAA,IADNV,SAAEA,cAAiD,CAAA,EAC7CU,GACFvC,QAAEA,GAAY1B,EAAM6B,WAAWL,IAC7BY,SAAUC,GAAqBP,IAEjCQ,EAAqBC,KAAKC,UAC5BC,oCAA2Bf,GAASgB,KAAKC,GAAUA,EAAMC,gBAG3D,OAAO5C,EAAMkE,SACX,IACEb,EAAAA,UACEJ,EACAV,KAAKe,MAAMhB,GACXD,EACa,SAAbkB,IAEJ,CAACN,EAAIX,EAAoBD,EAAkBkB,GAE9C,CAUM,SAASY,EACdC,EACAC,GAGEzC,KADFG,EAAAA,WAAA,GAOA,IAAII,UAAEA,GAAcnC,EAAM6B,WAAWP,GACjCgD,EAAyBtE,EAAM6B,WAAWT,IACxCM,QAAS6C,GAAkBvE,EAAM6B,WAAWL,GAC9CgD,EAAaD,EAAcA,EAAcE,OAAS,GAClDC,EAAeF,EAAaA,EAAWG,OAAS,CAAA,GAC/BH,GAAaA,EAAWpC,SAC7C,IAAIwC,EAAqBJ,EAAaA,EAAW5B,aAAe,IAC9C4B,GAAcA,EAAWK,MAqC3C,IAEI7C,EAFA8C,EAAsBhD,IAG1B,GAAIuC,EAAa,CAAA,IAAAU,EACf,IAAIC,EACqB,iBAAhBX,EAA2BY,EAAAA,UAAUZ,GAAeA,EAGpC,MAAvBO,IACE,OAAAI,EAAAA,EAAkB5C,eAAlB,EAAA2C,EAA4BG,WAAWN,KAF3C7C,EAAAA,WAAA,GASAC,EAAWgD,CACZ,MACChD,EAAW8C,EAGb,IAAI1C,EAAWJ,EAASI,UAAY,IAChC+C,EACqB,MAAvBP,EACIxC,EACAA,EAASgD,MAAMR,EAAmBH,SAAW,IAE/C/C,EAAU2D,EAAWA,YAACjB,EAAQ,CAAEhC,SAAU+C,IAgB1CG,EAAkBC,EACpB7D,GACEA,EAAQgB,KAAKC,GACXlD,OAAO+F,OAAO,CAAd,EAAkB7C,EAAO,CACvBgC,OAAQlF,OAAO+F,OAAO,CAAd,EAAkBd,EAAc/B,EAAMgC,QAC9CvC,SAAUoB,EAASA,UAAC,CAClBoB,EAEAzC,EAAUsD,eACNtD,EAAUsD,eAAe9C,EAAMP,UAAUA,SACzCO,EAAMP,WAEZQ,aACyB,MAAvBD,EAAMC,aACFgC,EACApB,EAASA,UAAC,CACRoB,EAEAzC,EAAUsD,eACNtD,EAAUsD,eAAe9C,EAAMC,cAAcR,SAC7CO,EAAMC,mBAIxB2B,EACAD,QAA0BoB,GAM5B,OAAIrB,GAAeiB,EAEftF,EAAAY,cAACW,EAAgBwC,SAAjB,CACEzD,MAAO,CACL0B,SAAQ2D,EAAA,CACNvD,SAAU,IACVwD,OAAQ,GACRC,KAAM,GACNlC,MAAO,KACPmC,IAAK,WACF9D,GAEL+D,eAAgBC,EAAcC,OAACC,MAGhCZ,GAKAA,CACR,CAED,SAASa,IACP,IAAI3F,EAAQ4F,IACRC,EAAUC,EAAAA,qBAAqB9F,GAC5BA,EAAM+F,OAAU/F,IAAAA,EAAMgG,WACzBhG,aAAiBiG,MACjBjG,EAAM6F,QACN9D,KAAKC,UAAUhC,GACfkG,EAAQlG,aAAiBiG,MAAQjG,EAAMkG,MAAQ,KAE/CC,EAAY,CAAEC,QAAS,SAAUC,gBADrB,0BAmBhB,OACE7G,EAAAY,cAAAZ,EAAA8G,SAAA,KACE9G,2DACAA,EAAAY,cAAA,KAAA,CAAImG,MAAO,CAAEC,UAAW,WAAaX,GACpCK,EAAQ1G,EAAAY,cAAA,MAAA,CAAKmG,MAAOJ,GAAYD,GAAe,KAnBtC,KAuBf,CAcM,MAAMO,UAA4BjH,EAAMkH,UAI7CC,YAAYC,GACVC,MAAMD,GACNE,KAAK3D,MAAQ,CACX3B,SAAUoF,EAAMpF,SAChBxB,MAAO4G,EAAM5G,MAEhB,CAE8B+G,gCAAC/G,GAC9B,MAAO,CAAEA,MAAOA,EACjB,CAE8B+G,gCAC7BH,EACAzD,GAUA,OAAIA,EAAM3B,WAAaoF,EAAMpF,SACpB,CACLxB,MAAO4G,EAAM5G,MACbwB,SAAUoF,EAAMpF,UAQb,CACLxB,MAAO4G,EAAM5G,OAASmD,EAAMnD,MAC5BwB,SAAU2B,EAAM3B,SAEnB,CAEDwF,kBAAkBhH,EAAYiH,GAC5BC,QAAQlH,MACN,wDACAA,EACAiH,EAEH,CAEDE,SACE,OAAOL,KAAK3D,MAAMnD,MAChBR,EAACY,cAAAY,EAAauC,SAAd,CAAuBzD,MAAOgH,KAAKF,MAAMQ,cACvC5H,EAAAY,cAACe,EAAkBoC,SAAnB,CACEzD,MAAOgH,KAAK3D,MAAMnD,MAClBqH,SAAUP,KAAKF,MAAMU,aAIzBR,KAAKF,MAAMS,QAEd,EASH,SAASE,EAAqEC,GAAA,IAAvDJ,aAAEA,EAAFjF,MAAgBA,EAAhBkF,SAAuBA,GAAgCG,EACxEC,EAAoBjI,EAAM6B,WAAWX,GAazC,OARE+G,GACAA,EAAkBV,QAClBU,EAAkBC,eAClBvF,EAAMkC,MAAMsD,eAEZF,EAAkBC,cAAcE,2BAA6BzF,EAAMkC,MAAMwD,IAIzErI,EAAAY,cAACY,EAAauC,SAAd,CAAuBzD,MAAOsH,GAC3BC,EAGN,CAEM,SAAStC,EACd7D,EACA6C,EACA+D,GAEA,QAD2B,IAF3B/D,IAAAA,EAA8B,IAGf,MAAX7C,EAAiB,CACnB,GAAA,MAAI4G,IAAAA,EAAiBC,OAKnB,OAAO,KAFP7G,EAAU4G,EAAgB5G,OAI7B,CAED,IAAI4D,EAAkB5D,EAGlB6G,EAASD,MAAAA,OAAAA,EAAAA,EAAiBC,OAC9B,GAAc,MAAVA,EAAgB,CAClB,IAAIC,EAAalD,EAAgBmD,WAC9BC,GAAMA,EAAE7D,MAAMwD,KAAME,MAAAA,OAAAA,EAAAA,EAASG,EAAE7D,MAAMwD,OAGtCG,GAAc,GADhBzG,EAASA,WAAT,GAIAuD,EAAkBA,EAAgBF,MAChC,EACAuD,KAAKC,IAAItD,EAAgBb,OAAQ+D,EAAa,GAEjD,CAED,OAAOlD,EAAgBuD,aAAY,CAACpH,EAAQkB,EAAOmG,KACjD,IAAItI,EAAQmC,EAAMkC,MAAMwD,GAAZ,MAAiBE,OAAjB,EAAiBA,EAAS5F,EAAMkC,MAAMwD,IAAM,KAEpDF,EAAeG,EACf3F,EAAMkC,MAAMsD,cAAgBnI,EAAAY,cAACuF,EAAD,MAC5B,KACAzE,EAAU6C,EAAcwE,OAAOzD,EAAgBF,MAAM,EAAG0D,EAAQ,IAChEE,EAAc,IAChBhJ,EAAAY,cAACmH,EAAD,CAAepF,MAAOA,EAAOiF,aAAc,CAAEnG,SAAQC,YAClDlB,EACG2H,OACwBzC,IAAxB/C,EAAMkC,MAAMoE,QACZtG,EAAMkC,MAAMoE,QACZxH,GAMR,OAAO6G,IAAoB3F,EAAMkC,MAAMsD,cAA0B,IAAVW,GACrD9I,gBAACiH,EAAD,CACEjF,SAAUsG,EAAgBtG,SAC1B8F,UAAWK,EACX3H,MAAOA,EACPqH,SAAUmB,IACVpB,aAAc,CAAEnG,OAAQ,KAAMC,aAGhCsH,GATF,GAWC,KACJ,KAEIE,EAKAC,EAgBL,SAASC,EAAqBC,GAC5B,IAAIC,EAAMtJ,EAAM6B,WAAWX,GAE3B,OADUoI,GAAVvH,EAASA,WAAT,GACOuH,CACR,CAED,SAASC,EAAmBF,GAC1B,IAAI1F,EAAQ3D,EAAM6B,WAAWT,GAE7B,OADUuC,GAAV5B,EAASA,WAAT,GACO4B,CACR,CAQD,SAAS6F,EAAkBH,GACzB,IAAIxE,EAPN,SAAyBwE,GACvB,IAAIxE,EAAQ7E,EAAM6B,WAAWL,GAE7B,OADUqD,GAAV9C,EAASA,WAAT,GACO8C,CACR,CAGa4E,GACRC,EAAY7E,EAAMnD,QAAQmD,EAAMnD,QAAQ+C,OAAS,GAKrD,OAHEiF,EAAU7E,MAAMwD,IADlBtG,EAASA,WAAT,GAIO2H,EAAU7E,MAAMwD,EACxB,CA4FM,SAASjC,IAAyB,IAAAuD,EACvC,IAAInJ,EAAQR,EAAM6B,WAAWF,GACzBgC,EAAQ4F,EAAmBJ,EAAoBS,eAC/CC,EAAUL,EAAkBL,EAAoBS,eAIpD,OAAIpJ,IAKG,OAAPmJ,EAAOhG,EAAM4E,aAAN,EAAAoB,EAAeE,GACvB,CAKM,SAASC,IACd,IAAIxJ,EAAQN,EAAM6B,WAAWR,GAC7B,OAAA,MAAOf,OAAP,EAAOA,EAAOyJ,KACf,WAhKIb,GAAAA,0BAAAA,iCAAAA,EAAAA,IAAAA,gBAKAC,GAAAA,gCAAAA,gCAAAA,gCAAAA,gCAAAA,0CAAAA,0BAAAA,iCAAAA,EAAAA,IAAAA,OAqKL,IAAIa,EAAY,ECjjBT,SAASC,EAAMC,GACpBnI,EAASA,WAAT,EAKD,CAoBM,SAASoI,EAO2BC,GAAA,IANzClI,SAAUmI,EAAe,IADJxC,SAErBA,EAAW,KACX7F,SAAUsI,EAHWvE,eAIrBA,EAAiBC,EAAcC,OAACC,IAJX/D,UAKrBA,EACAoF,OAAQgD,GAAa,GACoBH,EAEtCxI,KADHG,EAAAA,WAAA,GAQA,IAAIG,EAAWmI,EAAa5G,QAAQ,OAAQ,KACxC+G,EAAoBxK,EAAMkE,SAC5B,KAAO,CAAEhC,WAAUC,YAAWoF,OAAQgD,KACtC,CAACrI,EAAUC,EAAWoI,IAGI,iBAAjBD,IACTA,EAAerF,EAAAA,UAAUqF,IAG3B,IAAIlI,SACFA,EAAW,IADTwD,OAEFA,EAAS,GAFPC,KAGFA,EAAO,GAHLlC,MAIFA,EAAQ,KAJNmC,IAKFA,EAAM,WACJwE,EAEAtI,EAAWhC,EAAMkE,SAAQ,KAC3B,IAAIuG,EAAmBC,EAAAA,cAActI,EAAUF,GAE/C,OAAwB,MAApBuI,EACK,KAGF,CACLrI,SAAUqI,EACV7E,SACAC,OACAlC,QACAmC,MALF,GAOC,CAAC5D,EAAUE,EAAUwD,EAAQC,EAAMlC,EAAOmC,IAS7C,OAAgB,MAAZ9D,EACK,KAIPhC,EAAAY,cAACU,EAAkByC,SAAnB,CAA4BzD,MAAOkK,GACjCxK,EAAAY,cAACW,EAAgBwC,SAAjB,CACE8D,SAAUA,EACVvH,MAAO,CAAE0B,WAAU+D,oBAI1B,CAaM,SAAS4E,EAG2BC,GAAA,IAHpB/C,SACrBA,EADqB7F,SAErBA,GACyC4I,EACrC3C,EAAoBjI,EAAM6B,WAAWX,GAQzC,OAAOiD,EAHL8D,IAAsBJ,EACjBI,EAAkB4C,OAAOzG,OAC1B0G,EAAyBjD,GACN7F,EAC1B,KAiCI+I,YAAAA,GAAAA,EAAAA,uBAAAA,EAAAA,uBAAAA,EAAAA,kBAAAA,EAAAA,IAAAA,OAML,MAAMC,EAAsB,IAAIC,SAAQ,SAExC,MAAMC,UAA2BlL,EAAMkH,UAIrCC,YAAYC,GACVC,MAAMD,GACNE,KAAK3D,MAAQ,CAAEnD,MAAO,KACvB,CAE8B+G,gCAAC/G,GAC9B,MAAO,CAAEA,QACV,CAEDgH,kBAAkBhH,EAAYiH,GAC5BC,QAAQlH,MACN,mDACAA,EACAiH,EAEH,CAEDE,SACE,IAAIE,SAAEA,EAAFM,aAAYA,EAAZgD,QAA0BA,GAAY7D,KAAKF,MAE3CgE,EAAiC,KACjC7E,EAA4BwE,EAAkBM,QAElD,GAAMF,aAAmBF,QAMlB,GAAI3D,KAAK3D,MAAMnD,MAAO,CAE3B+F,EAASwE,EAAkBvK,MAC3B,IAAI8K,EAAchE,KAAK3D,MAAMnD,MAC7B4K,EAAUH,QAAQM,SAASC,OAAM,SACjC/L,OAAOgM,eAAeL,EAAS,WAAY,CAAEM,IAAK,KAAM,IACxDjM,OAAOgM,eAAeL,EAAS,SAAU,CAAEM,IAAK,IAAMJ,GACvD,MAAWH,EAA2BQ,UAErCP,EAAUD,EACV5E,OACqBb,IAAnB0F,EAAQQ,OACJb,EAAkBvK,WACAkF,IAAlB0F,EAAQrB,MACRgB,EAAkBc,QAClBd,EAAkBM,UAGxB9E,EAASwE,EAAkBM,QAC3B5L,OAAOgM,eAAeN,EAAS,WAAY,CAAEO,IAAK,KAAM,IACxDN,EAAUD,EAAQW,MACfC,GACCtM,OAAOgM,eAAeN,EAAS,QAAS,CAAEO,IAAK,IAAMK,MACtDvL,GACCf,OAAOgM,eAAeN,EAAS,SAAU,CAAEO,IAAK,IAAMlL,YA5B1D+F,EAASwE,EAAkBc,QAC3BT,EAAUH,QAAQE,UAClB1L,OAAOgM,eAAeL,EAAS,WAAY,CAAEM,IAAK,KAAM,IACxDjM,OAAOgM,eAAeL,EAAS,QAAS,CAAEM,IAAK,IAAMP,IA6BvD,GACE5E,IAAWwE,EAAkBvK,OAC7B4K,EAAQQ,kBAAkBI,EAAAA,qBAG1B,MAAMhB,EAGR,GAAIzE,IAAWwE,EAAkBvK,QAAU2H,EAEzC,MAAMiD,EAAQQ,OAGhB,GAAIrF,IAAWwE,EAAkBvK,MAE/B,OAAOR,EAAAY,cAACS,EAAa0C,SAAd,CAAuBzD,MAAO8K,EAASvD,SAAUM,IAG1D,GAAI5B,IAAWwE,EAAkBc,QAE/B,OAAO7L,EAAAY,cAACS,EAAa0C,SAAd,CAAuBzD,MAAO8K,EAASvD,SAAUA,IAI1D,MAAMuD,CACP,EAOH,SAASa,EAINC,GAAA,IAJmBrE,SACpBA,GAGCqE,EACGH,EAAOjC,IACPqC,EAA+B,mBAAbtE,EAA0BA,EAASkE,GAAQlE,EACjE,OAAO7H,EAAAY,cAAAZ,EAAA8G,SAAA,KAAGqF,EACX,CAaM,SAASrB,EACdjD,EACAuE,QACe,IADfA,IAAAA,EAAuB,IAEvB,IAAIhI,EAAwB,GAuD5B,OArDApE,EAAMqM,SAASC,QAAQzE,GAAU,CAACoB,EAASH,KACzC,IAAK9I,EAAMuM,eAAetD,GAGxB,OAGF,GAAIA,EAAQuD,OAASxM,EAAM8G,SAMzB,YAJA1C,EAAOV,KAAK+I,MACVrI,EACA0G,EAAyB7B,EAAQ7B,MAAMS,SAAUuE,IAMnDnD,EAAQuD,OAASvC,GADnBlI,EAAAA,WAAA,GAQGkH,EAAQ7B,MAAM0B,OAAUG,EAAQ7B,MAAMS,UADzC9F,EAASA,WAAT,GAKA,IAAI2K,EAAW,IAAIN,EAAYtD,GAC3BjE,EAAqB,CACvBwD,GAAIY,EAAQ7B,MAAMiB,IAAMqE,EAASC,KAAK,KACtCC,cAAe3D,EAAQ7B,MAAMwF,cAC7B3D,QAASA,EAAQ7B,MAAM6B,QACvBH,MAAOG,EAAQ7B,MAAM0B,MACrB1F,KAAM6F,EAAQ7B,MAAMhE,KACpByJ,OAAQ5D,EAAQ7B,MAAMyF,OACtBC,OAAQ7D,EAAQ7B,MAAM0F,OACtB3E,aAAcc,EAAQ7B,MAAMe,aAC5B4E,iBAAgD,MAA9B9D,EAAQ7B,MAAMe,aAChC6E,iBAAkB/D,EAAQ7B,MAAM4F,iBAChCC,OAAQhE,EAAQ7B,MAAM6F,QAGpBhE,EAAQ7B,MAAMS,WAChBhD,EAAMgD,SAAWiD,EACf7B,EAAQ7B,MAAMS,SACd6E,IAIJtI,EAAOV,KAAKmB,EAAZ,IAGKT,CACR,CAgBM,SAAS8I,EACd9I,GAEA,OAAOA,EAAO1B,KAAKmC,IACjB,IAAIsI,EAAkBtI,EAAAA,CAAAA,EAAAA,GAOtB,OANmC,MAA/BsI,EAAWJ,mBACbI,EAAWJ,iBAA8C,MAA3BI,EAAWhF,cAEvCgF,EAAWtF,WACbsF,EAAWtF,SAAWqF,EAA0BC,EAAWtF,WAEtDsF,CAAP,GAEH,mlCApOM,SAAgEC,GAAA,IAAjDvF,SAAEA,EAAFM,aAAYA,EAAZgD,QAA0BA,GAAuBiC,EACrE,OACEpN,gBAACkL,EAAD,CAAoBC,QAASA,EAAShD,aAAcA,GAClDnI,EAACY,cAAAqL,EAAcpE,KAAAA,GAGpB,iBAzRM,SAKmCwF,GAAA,IALbnL,SAC3BA,EAD2B2F,SAE3BA,EAF2ByF,eAG3BA,EAH2BC,aAI3BA,GACwCF,EACpCG,EAAaxN,EAAM8C,SACG,MAAtB0K,EAAWzK,UACbyK,EAAWzK,QAAU0K,sBAAoB,CACvCH,iBACAC,eACAG,UAAU,KAId,IAAIC,EAAUH,EAAWzK,SACpBY,EAAOiK,GAAY5N,EAAMJ,SAAS,CACrCkN,OAAQa,EAAQb,OAChB9K,SAAU2L,EAAQ3L,WAKpB,OAFAhC,EAAMF,iBAAgB,IAAM6N,EAAQE,OAAOD,IAAW,CAACD,IAGrD3N,gBAACmK,EAAD,CACEjI,SAAUA,EACV2F,SAAUA,EACV7F,SAAU2B,EAAM3B,SAChB+D,eAAgBpC,EAAMmJ,OACtB3K,UAAWwL,GAGhB,aAkBM,SAKiBG,GAAA,IALC7K,GACvBA,EADuBQ,QAEvBA,EAFuBE,MAGvBA,EAHuBJ,SAIvBA,GACsBuK,EAEpBlM,KADFG,EAAAA,WAAA,GAcA,IAAIuG,EAAkBtI,EAAM6B,WAAWT,GACnC2M,EAAW9L,IAYf,OAVAjC,EAAMH,WAAU,KAIVyI,GAAwD,SAArCA,EAAgB0F,WAAWrK,OAGlDoK,EAAS9K,EAAI,CAAEQ,UAASE,QAAOJ,YAA/B,IAGK,IACR,WAWM,SAAgB6D,GACrB,OAAOvD,EAAUuD,EAAMtD,QACxB,wCAjLM,SAGqCkE,GAAA,IAHbiG,gBAC7BA,EAD6BpD,OAE7BA,GAC0C7C,EAEtCrE,EAAqBuK,EACvBrD,EAAOhK,WACP,IAAMgK,EAAOlH,QAIb,IAAMkH,EAAOlH,QAGXxB,EAAYnC,EAAMkE,SAAQ,KACrB,CACLiK,WAAYtD,EAAOsD,WACnB1I,eAAgBoF,EAAOpF,eACvBtC,GAAKiL,GAAMvD,EAAOkD,SAASK,GAC3B1K,KAAM,CAACT,EAAIU,EAAO0K,IAChBxD,EAAOkD,SAAS9K,EAAI,CAClBU,QACA2K,mBAAoBD,MAAAA,OAAAA,EAAAA,EAAMC,qBAE9B7K,QAAS,CAACR,EAAIU,EAAO0K,IACnBxD,EAAOkD,SAAS9K,EAAI,CAClBQ,SAAS,EACTE,QACA2K,mBAAoBD,MAAAA,OAAAA,EAAAA,EAAMC,wBAG/B,CAACzD,IAEA3I,EAAW2I,EAAO3I,UAAY,IAQlC,OACElC,EACEY,cAAAZ,EAAA8G,SAAA,KAAA9G,EAAAY,cAACM,EAAkB6C,SAAnB,CACEzD,MAAO,CACLuK,SACA1I,YACAoF,QAAQ,EAERrF,aAGFlC,EAAAY,cAACQ,EAAuB2C,SAAxB,CAAiCzD,MAAOqD,GACtC3D,gBAACmK,EAAD,CACEjI,SAAU2I,EAAO3I,SACjBF,SAAU6I,EAAOlH,MAAM3B,SACvB+D,eAAgB8E,EAAOlH,MAAM4K,cAC7BpM,UAAWA,GAEV0I,EAAOlH,MAAM6K,YAAcxO,EAACY,cAAA+J,EAA5B,MAAwCsD,KAI9C,KAGN,sNCsFM,SACL7J,EACAiK,GAOA,OAAOI,eAAa,CAClBvM,SAAUmM,MAAAA,OAAAA,EAAAA,EAAMnM,SAChByL,QAASF,EAAAA,oBAAoB,CAC3BH,eAAgBe,MAAAA,OAAAA,EAAAA,EAAMf,eACtBC,aAAcc,MAAAA,OAAAA,EAAAA,EAAMd,eAEtBmB,cAAeL,MAAAA,OAAAA,EAAAA,EAAMK,cACrBtK,OAAQ8I,EAA0B9I,KACjCuK,YACJ,4EDqYM,SACLjN,GAEA,OAAO6D,EAAe7D,EACvB,wBDgOM,SAAoBkN,GACzB,IAAI/D,OAAEA,GAAWzB,EAAqBF,EAAe2F,aAChDC,GAAc9O,EAAMJ,UAAS,IAAMmP,SAAS/E,KAE7CgF,EAAkBhP,EAAMgD,aACzBiM,GAC+B,mBAAhBL,IACRA,EAAYK,KACZL,GAER,CAACA,IAGCM,EAAUrE,EAAOsE,WAAWL,EAAYE,GAQ5C,OALAhP,EAAMH,WACJ,IAAM,IAAMgL,EAAOuE,cAAcN,IACjC,CAACjE,EAAQiE,IAGJI,CACR,kBA3EM,WACL,IAAIvL,EAAQ4F,EAAmBJ,EAAoBkG,eAKnD,OAHYrP,EAAM6B,WAAWL,IAC7BO,EAASA,WAAT,GAEOtC,OAAO6P,QAAY,MAAL3L,OAAAA,EAAAA,EAAO4L,aAAc,IAAI,EAC/C,kBAiCM,WACL,IAAIjP,EAAQN,EAAM6B,WAAWR,GAC7B,OAAA,MAAOf,OAAP,EAAOA,EAAOsL,MACf,8BA7wBM,SACL3I,EAEQuM,GAAA,IADRjM,SAAEA,cAAiD,CAAA,EAC3CiM,EAEN5N,KADFG,EAAAA,WAAA,GAOA,IAAIG,SAAEA,EAAFC,UAAYA,GAAcnC,EAAM6B,WAAWP,IAC3CuE,KAAEA,EAAFzD,SAAQA,EAARwD,OAAkBA,GAAW5B,EAAgBf,EAAI,CAAEM,aAEnDkM,EAAiBrN,EAWrB,MALiB,MAAbF,IACFuN,EACe,MAAbrN,EAAmBF,EAAWsB,EAASA,UAAC,CAACtB,EAAUE,KAGhDD,EAAUgM,WAAW,CAAE/L,SAAUqN,EAAgB7J,SAAQC,QACjE,yCAgrBM,WACL,IAAIlC,EAAQ4F,EAAmBJ,EAAoBuG,eAC/C7F,EAAUL,EAAkBL,EAAoBuG,eAEpD,IAAI/L,EAAM4E,QAAmC,MAAzB5E,EAAM4E,OAAOsB,GAMjC,OAAOlG,EAAMgM,WAAW9F,GALtBnC,QAAQlH,MAAR,2DAC+DqJ,EAD/D,IAMH,6BA1oBM,SAGL+F,GAEEhO,KADFG,EAAAA,WAAA,GAOA,IAAIK,SAAEA,GAAaN,IACnB,OAAO9B,EAAMkE,SACX,IAAM2L,YAA0BD,EAASxN,IACzC,CAACA,EAAUwN,GAEd,eAqlBM,WACL,IAAIlO,QAAEA,EAAFiO,WAAWA,GAAepG,EAC5BJ,EAAoB2G,YAEtB,OAAO9P,EAAMkE,SACX,IACExC,EAAQgB,KAAKC,IACX,IAAIP,SAAEA,EAAFuC,OAAYA,GAAWhC,EAI3B,MAAO,CACL0F,GAAI1F,EAAMkC,MAAMwD,GAChBjG,WACAuC,SACAoH,KAAM4D,EAAWhN,EAAMkC,MAAMwD,IAC7B4E,OAAQtK,EAAMkC,MAAMoI,OALtB,KAQJ,CAACvL,EAASiO,GAEb,kCA3CM,WAEL,OADYpG,EAAmBJ,EAAoB4G,eACtC/B,UACd,sBA7lBM,WACL,OAAOhO,EAAM6B,WAAWN,GAAiBwE,cAC1C,mCAkHM,WACL,OAAO/F,EAAM6B,WAAW+B,EACzB,cAwBM,WAKL,IAAIlC,QAAEA,GAAY1B,EAAM6B,WAAWL,GAC/BgD,EAAa9C,EAAQA,EAAQ+C,OAAS,GAC1C,OAAOD,EAAcA,EAAWG,OAAiB,EAClD,uCA6cM,WACL,IAAIsD,EAAoBmB,EAAqBF,EAAe8G,gBACxDrM,EAAQ4F,EAAmBJ,EAAoB6G,gBACnD,MAAO,CACLC,WAAYhI,EAAkB4C,OAAOoF,WACrCtM,MAAOA,EAAMuM,aAEhB,yCAgDM,SAA4BrG,GAEjC,OADYN,EAAmBJ,EAAoBgH,oBACtCR,WAAW9F,EACzB"}