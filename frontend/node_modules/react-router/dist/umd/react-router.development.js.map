{"version": 3, "file": "react-router.development.js", "sources": ["../../lib/use-sync-external-store-shim/useSyncExternalStoreShimClient.ts", "../../lib/use-sync-external-store-shim/useSyncExternalStoreShimServer.ts", "../../lib/use-sync-external-store-shim/index.ts", "../../lib/context.ts", "../../lib/hooks.tsx", "../../lib/components.tsx", "../../index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as React from \"react\";\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction isPolyfill(x: any, y: any) {\n  return (\n    (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y) // eslint-disable-line no-self-compare\n  );\n}\n\nconst is: (x: any, y: any) => boolean =\n  typeof Object.is === \"function\" ? Object.is : isPolyfill;\n\n// Intentionally not using named imports because Roll<PERSON> uses dynamic\n// dispatch for CommonJS interop named imports.\nconst { useState, useEffect, useLayoutEffect, useDebugValue } = React;\n\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false;\n\n// Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  getServerSnapshot?: () => T\n): T {\n  if (__DEV__) {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that \" +\n            \"does not support useSyncExternalStore. The \" +\n            \"use-sync-external-store shim will not work correctly. Upgrade \" +\n            \"to a newer pre-release.\"\n        );\n      }\n    }\n  }\n\n  // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n  const value = getSnapshot();\n  if (__DEV__) {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n      if (!is(value, cachedValue)) {\n        console.error(\n          \"The result of getSnapshot should be cached to avoid an infinite loop\"\n        );\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  }\n\n  // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n  const [{ inst }, forceUpdate] = useState({ inst: { value, getSnapshot } });\n\n  // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot;\n\n    // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe, value, getSnapshot]);\n\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({ inst });\n      }\n    };\n    // Subscribe to the store and return a clean-up function.\n    return subscribe(handleStoreChange);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst: any) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  getServerSnapshot?: () => T\n): T {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n", "/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport * as React from \"react\";\n\nimport { useSyncExternalStore as client } from \"./useSyncExternalStoreShimClient\";\nimport { useSyncExternalStore as server } from \"./useSyncExternalStoreShimServer\";\n\nconst canUseDOM: boolean = !!(\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\"\n);\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? server : client;\n\nexport const useSyncExternalStore =\n  \"useSyncExternalStore\" in React\n    ? ((module) => module.useSyncExternalStore)(React)\n    : shim;\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  History,\n  Location,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport type { Action as NavigationType } from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject extends NavigationContextObject {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  Router as RemixRouter,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  warning,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n} from \"@remix-run/router\";\n\nimport type {\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n  DataRouteMatch,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n  AwaitContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history.  If this is a root navigation, then we\n      // navigate to the raw basename which allows the basename to have full\n      // control over the presence of a trailing slash on root links\n      if (basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterStateContext || undefined\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own&nbsp;\n          <code style={codeStyles}>errorElement</code> props on&nbsp;\n          <code style={codeStyles}>&lt;Route&gt;</code>\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    match.route.errorElement\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState?: RemixRouter[\"state\"]\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for the current errors: ${errors}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors?.[match.route.id] : null;\n    // Only data routers handle errors\n    let errorElement = dataRouterState\n      ? match.route.errorElement || <DefaultErrorElement />\n      : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => (\n      <RenderedRoute match={match} routeContext={{ outlet, matches }}>\n        {error\n          ? errorElement\n          : match.route.element !== undefined\n          ? match.route.element\n          : outlet}\n      </RenderedRoute>\n    );\n    // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n    return dataRouterState && (match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nenum DataRouterStateHook {\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation,\n  };\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () =>\n      matches.map((match) => {\n        let { pathname, params } = match;\n        // Note: This structure matches that created by createUseMatchesMatch\n        // in the @remix-run/router , so if you change this please also change\n        // that :)  Eventually we'll DRY this up\n        return {\n          id: match.route.id,\n          pathname,\n          params,\n          data: loaderData[match.route.id] as unknown,\n          handle: match.route.handle as unknown,\n        };\n      }),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useActionData must be used inside a RouteContext`);\n\n  return Object.values(state?.actionData || {})[0];\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (args) => {\n      return typeof shouldBlock === \"function\"\n        ? !!shouldBlock(args)\n        : !!shouldBlock;\n    },\n    [shouldBlock]\n  );\n\n  let blocker = router.getBlocker(blockerKey, blockerFunction);\n\n  // Cleanup on unmount\n  React.useEffect(\n    () => () => router.deleteBlocker(blockerKey),\n    [router, blockerKey]\n  );\n\n  return blocker;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import * as React from \"react\";\nimport type {\n  TrackedPromise,\n  InitialEntry,\n  Location,\n  MemoryHistory,\n  Router as RemixRouter,\n  RouterState,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  AbortedDeferredError,\n  createMemoryHistory,\n  invariant,\n  parsePath,\n  stripBasename,\n  warning,\n} from \"@remix-run/router\";\nimport { useSyncExternalStore as useSyncExternalStoreShim } from \"./use-sync-external-store-shim\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  LocationContext,\n  NavigationContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  AwaitContext,\n} from \"./context\";\nimport {\n  useAsyncValue,\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n}: RouterProviderProps): React.ReactElement {\n  // Sync router state to our component state to force re-renders\n  let state: RouterState = useSyncExternalStoreShim(\n    router.subscribe,\n    () => router.state,\n    // We have to provide this so React@18 doesn't complain during hydration,\n    // but we pass our serialized hydration data into the router so state here\n    // is already synced with what the server saw\n    () => router.state\n  );\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider\n        value={{\n          router,\n          navigator,\n          static: false,\n          // Do we need this?\n          basename,\n        }}\n      >\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={router.basename}\n            location={router.state.location}\n            navigationType={router.state.historyAction}\n            navigator={navigator}\n          >\n            {router.state.initialized ? <Routes /> : fallbackElement}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n    navigate(to, { replace, state, relative });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  let dataRouterContext = React.useContext(DataRouterContext);\n  // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n  let routes =\n    dataRouterContext && !children\n      ? (dataRouterContext.router.routes as DataRouteObject[])\n      : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, parentPath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let treePath = [...parentPath, index];\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\nexport function enhanceManualRouteObjects(\n  routes: RouteObject[]\n): RouteObject[] {\n  return routes.map((route) => {\n    let routeClone = { ...route };\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n    return routeClone;\n  });\n}\n", "import type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  Fetcher,\n  HydrationState,\n  JsonFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  PathPattern,\n  RedirectFunction,\n  Router as RemixRouter,\n  ShouldRevalidateFunction,\n  To,\n  InitialEntry,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  resolvePath,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  RoutesProps,\n  RouterProviderProps,\n} from \"./lib/components\";\nimport {\n  enhanceManualRouteObjects,\n  createRoutesFromChildren,\n  renderMatches,\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NavigateOptions,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  RelativeRoutingType,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigationType,\n  useNavigate,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useLoaderData,\n  useMatches,\n  useNavigation,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker as unstable_Blocker,\n  BlockerFunction as unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker as unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  NavigationContext as UNSAFE_NavigationContext,\n  LocationContext as UNSAFE_LocationContext,\n  RouteContext as UNSAFE_RouteContext,\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects,\n};\n"], "names": ["isPolyfill", "x", "y", "is", "Object", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "React", "didWarnOld18Alpha", "didWarnUncachedGetSnapshot", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "console", "error", "value", "cachedValue", "inst", "forceUpdate", "checkIfSnapshotChanged", "handleStoreChange", "latestGetSnapshot", "prevValue", "nextValue", "canUseDOM", "window", "document", "createElement", "isServerEnvironment", "shim", "server", "client", "module", "DataRouterContext", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "RouteErrorContext", "useHref", "to", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "useNavigate", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getPathContributingMatches", "map", "match", "pathnameBase", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "useParams", "routeMatch", "length", "params", "useRoutes", "routes", "locationArg", "dataRouterStateContext", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "slice", "matchRoutes", "element", "undefined", "renderedMatches", "_renderMatches", "assign", "encodeLocation", "key", "NavigationType", "Pop", "DefaultErrorElement", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "fontStyle", "RenderErrorBoundary", "Component", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "component", "children", "RenderedRoute", "dataRouterContext", "static", "staticContext", "errorElement", "_deepestRenderedBoundaryId", "id", "dataRouterState", "errors", "errorIndex", "findIndex", "m", "Math", "min", "reduceRight", "index", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "revalidation", "useMatches", "loaderData", "UseMatches", "data", "handle", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "values", "actionData", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "String", "blockerFunction", "args", "blocker", "get<PERSON><PERSON>er", "deleteBlocker", "alreadyWarned", "cond", "RouterProvider", "fallbackElement", "useSyncExternalStoreShim", "n", "opts", "preventScrollReset", "historyAction", "initialized", "MemoryRouter", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "setState", "action", "listen", "Navigate", "Outlet", "Route", "_props", "Router", "basenameProp", "locationProp", "staticProp", "navigationContext", "trailingPathname", "stripBasename", "Routes", "createRoutesFromChildren", "Await", "resolve", "AwaitRenderStatus", "neverSettledPromise", "Promise", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "promise", "pending", "success", "defineProperty", "get", "renderError", "reject", "catch", "_tracked", "then", "Aborted<PERSON>eferredError", "ResolveAwait", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "type", "Fragment", "apply", "name", "treePath", "join", "caseSensitive", "loader", "hasErrorBou<PERSON>ry", "shouldRevalidate", "renderMatches", "enhanceManualRouteObjects", "routeClone", "createMemoryRouter", "createRouter", "hydrationData", "initialize"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;;EACA,SAASA,UAAT,CAAoBC,CAApB,EAA4BC,CAA5B,EAAoC;IAClC,OACGD,CAAC,KAAKC,CAAN,KAAYD,CAAC,KAAK,CAAN,IAAW,CAAA,GAAIA,CAAJ,KAAU,IAAIC,CAArC,CAAD,IAA8CD,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CADjE;EAAA,GAAA;EAGD,CAAA;;EAED,MAAMC,EAA+B,GACnC,OAAOC,MAAM,CAACD,EAAd,KAAqB,UAArB,GAAkCC,MAAM,CAACD,EAAzC,GAA8CH,UADhD;EAIA;;EACA,MAAM;IAAEK,QAAF;IAAYC,SAAZ;IAAuBC,eAAvB;EAAwCC,EAAAA,aAAAA;EAAxC,CAAA,GAA0DC,gBAAhE,CAAA;EAEA,IAAIC,iBAAiB,GAAG,KAAxB,CAAA;EACA,IAAIC,0BAA0B,GAAG,KAAjC;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,sBAAT,CACLC,SADK,EAELC,WAFK;EAIL;EACA;EACA;EACAC,iBAPK,EAQF;IACU;MACX,IAAI,CAACL,iBAAL,EAAwB;QACtB,IAAI,iBAAA,IAAqBD,gBAAzB,EAAgC;EAC9BC,QAAAA,iBAAiB,GAAG,IAApB,CAAA;UACAM,OAAO,CAACC,KAAR,CACE,gEAAA,GACE,6CADF,GAEE,gEAFF,GAGE,yBAJJ,CAAA,CAAA;EAMD,OAAA;EACF,KAAA;EACF,GAbE;EAgBH;EACA;EACA;;;IACA,MAAMC,KAAK,GAAGJ,WAAW,EAAzB,CAAA;;IACa;MACX,IAAI,CAACH,0BAAL,EAAiC;QAC/B,MAAMQ,WAAW,GAAGL,WAAW,EAA/B,CAAA;;EACA,MAAA,IAAI,CAACX,EAAE,CAACe,KAAD,EAAQC,WAAR,CAAP,EAA6B;UAC3BH,OAAO,CAACC,KAAR,CACE,sEADF,CAAA,CAAA;EAGAN,QAAAA,0BAA0B,GAAG,IAA7B,CAAA;EACD,OAAA;EACF,KAAA;EACF,GA9BE;EAiCH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,EAAA,MAAM,CAAC;EAAES,IAAAA,IAAAA;EAAF,GAAD,EAAWC,WAAX,CAA0BhB,GAAAA,QAAQ,CAAC;EAAEe,IAAAA,IAAI,EAAE;QAAEF,KAAF;EAASJ,MAAAA,WAAAA;EAAT,KAAA;KAAT,CAAxC,CA9CG;EAiDH;EACA;;EACAP,EAAAA,eAAe,CAAC,MAAM;MACpBa,IAAI,CAACF,KAAL,GAAaA,KAAb,CAAA;EACAE,IAAAA,IAAI,CAACN,WAAL,GAAmBA,WAAnB,CAFoB;EAKpB;EACA;EACA;;EACA,IAAA,IAAIQ,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;EAChC;EACAC,MAAAA,WAAW,CAAC;EAAED,QAAAA,IAAAA;EAAF,OAAD,CAAX,CAAA;EACD,KAXmB;;KAAP,EAaZ,CAACP,SAAD,EAAYK,KAAZ,EAAmBJ,WAAnB,CAbY,CAAf,CAAA;EAeAR,EAAAA,SAAS,CAAC,MAAM;EACd;EACA;EACA,IAAA,IAAIgB,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;EAChC;EACAC,MAAAA,WAAW,CAAC;EAAED,QAAAA,IAAAA;EAAF,OAAD,CAAX,CAAA;EACD,KAAA;;MACD,MAAMG,iBAAiB,GAAG,MAAM;EAC9B;EACA;EACA;EACA;EAEA;EACA;EACA,MAAA,IAAID,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;EAChC;EACAC,QAAAA,WAAW,CAAC;EAAED,UAAAA,IAAAA;EAAF,SAAD,CAAX,CAAA;EACD,OAAA;EACF,KAZD,CAPc;;;EAqBd,IAAA,OAAOP,SAAS,CAACU,iBAAD,CAAhB,CArBc;EAuBf,GAvBQ,EAuBN,CAACV,SAAD,CAvBM,CAAT,CAAA;IAyBAL,aAAa,CAACU,KAAD,CAAb,CAAA;EACA,EAAA,OAAOA,KAAP,CAAA;EACD,CAAA;;EAED,SAASI,sBAAT,CAAgCF,IAAhC,EAA2C;EACzC,EAAA,MAAMI,iBAAiB,GAAGJ,IAAI,CAACN,WAA/B,CAAA;EACA,EAAA,MAAMW,SAAS,GAAGL,IAAI,CAACF,KAAvB,CAAA;;IACA,IAAI;MACF,MAAMQ,SAAS,GAAGF,iBAAiB,EAAnC,CAAA;EACA,IAAA,OAAO,CAACrB,EAAE,CAACsB,SAAD,EAAYC,SAAZ,CAAV,CAAA;KAFF,CAGE,OAAOT,KAAP,EAAc;EACd,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;EACF;;ECvJD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEO,SAASL,sBAAT,CACLC,SADK,EAELC,WAFK,EAGLC,iBAHK,EAIF;EACH;EACA;EACA;EACA;EACA,EAAA,OAAOD,WAAW,EAAlB,CAAA;EACD;;ECnBD;EACA;EACA;EACA;EACA;EAgBA,MAAMa,SAAkB,GAAG,CAAC,EAC1B,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHf,CAA5B,CAAA;EAKA,MAAMC,mBAAmB,GAAG,CAACJ,SAA7B,CAAA;EACA,MAAMK,IAAI,GAAGD,mBAAmB,GAAGE,sBAAH,GAAYC,sBAA5C,CAAA;EAEO,MAAMtB,oBAAoB,GAC/B,sBAA0BH,IAAAA,gBAA1B,GACI,CAAE0B,MAAD,IAAYA,MAAM,CAACvB,oBAApB,EAA0CH,gBAA1C,CADJ,GAEIuB,IAHC;;ACqCA,QAAMI,iBAAiB,gBAC5B3B,gBAAK,CAAC4B,aAAN,CAAoD,IAApD,EADK;;EAEM;IACXD,iBAAiB,CAACE,WAAlB,GAAgC,YAAhC,CAAA;EACD,CAAA;;AAEM,QAAMC,sBAAsB,gBAAG9B,gBAAK,CAAC4B,aAAN,CAEpC,IAFoC,EAA/B;;EAGM;IACXE,sBAAsB,CAACD,WAAvB,GAAqC,iBAArC,CAAA;EACD,CAAA;;EAEM,MAAME,YAAY,gBAAG/B,gBAAK,CAAC4B,aAAN,CAA2C,IAA3C,CAArB,CAAA;;EACM;IACXG,YAAY,CAACF,WAAb,GAA2B,OAA3B,CAAA;EACD,CAAA;;AAmCM,QAAMG,iBAAiB,gBAAGhC,gBAAK,CAAC4B,aAAN,CAC/B,IAD+B,EAA1B;;EAIM;IACXI,iBAAiB,CAACH,WAAlB,GAAgC,YAAhC,CAAA;EACD,CAAA;;AAOM,QAAMI,eAAe,gBAAGjC,gBAAK,CAAC4B,aAAN,CAC7B,IAD6B,EAAxB;;EAIM;IACXK,eAAe,CAACJ,WAAhB,GAA8B,UAA9B,CAAA;EACD,CAAA;;QAOYK,YAAY,gBAAGlC,gBAAK,CAAC4B,aAAN,CAAwC;EAClEO,EAAAA,MAAM,EAAE,IAD0D;EAElEC,EAAAA,OAAO,EAAE,EAAA;EAFyD,CAAxC,EAArB;;EAKM;IACXF,YAAY,CAACL,WAAb,GAA2B,OAA3B,CAAA;EACD,CAAA;;EAEM,MAAMQ,iBAAiB,gBAAGrC,gBAAK,CAAC4B,aAAN,CAAyB,IAAzB,CAA1B,CAAA;;EAEM;IACXS,iBAAiB,CAACR,WAAlB,GAAgC,YAAhC,CAAA;EACD;;EC/GD;EACA;EACA;EACA;EACA;EACA;;EACO,SAASS,OAAT,CACLC,EADK,EAGG,KAAA,EAAA;IAAA,IADR;EAAEC,IAAAA,QAAAA;EAAF,GACQ,sBAD2C,EAC3C,GAAA,KAAA,CAAA;EACR,EAAA,CACEC,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,oEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IAOA,IAAI;MAAEC,QAAF;EAAYC,IAAAA,SAAAA;EAAZ,GAAA,GAA0B5C,gBAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAA9B,CAAA;IACA,IAAI;MAAEc,IAAF;MAAQC,QAAR;EAAkBC,IAAAA,MAAAA;KAAWC,GAAAA,eAAe,CAACV,EAAD,EAAK;EAAEC,IAAAA,QAAAA;EAAF,GAAL,CAAhD,CAAA;EAEA,EAAA,IAAIU,cAAc,GAAGH,QAArB,CAXQ;EAcR;EACA;EACA;;IACA,IAAIJ,QAAQ,KAAK,GAAjB,EAAsB;EACpBO,IAAAA,cAAc,GACZH,QAAQ,KAAK,GAAb,GAAmBJ,QAAnB,GAA8BQ,gBAAS,CAAC,CAACR,QAAD,EAAWI,QAAX,CAAD,CADzC,CAAA;EAED,GAAA;;IAED,OAAOH,SAAS,CAACQ,UAAV,CAAqB;EAAEL,IAAAA,QAAQ,EAAEG,cAAZ;MAA4BF,MAA5B;EAAoCF,IAAAA,IAAAA;EAApC,GAArB,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;;EACO,SAASL,kBAAT,GAAuC;EAC5C,EAAA,OAAOzC,gBAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,KAAqC,IAA5C,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASoB,WAAT,GAAiC;EACtC,EAAA,CACEZ,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,wEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,EAAA,OAAO1C,gBAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,EAAkCqB,QAAzC,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,iBAAT,GAA6C;EAClD,EAAA,OAAOvD,gBAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,EAAkCuB,cAAzC,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,QAAT,CAGLC,OAHK,EAG0D;EAC/D,EAAA,CACEjB,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,qEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IAOA,IAAI;EAAEK,IAAAA,QAAAA;EAAF,GAAA,GAAeM,WAAW,EAA9B,CAAA;EACA,EAAA,OAAOrD,gBAAK,CAAC2D,OAAN,CACL,MAAMC,gBAAS,CAAiBF,OAAjB,EAA0BX,QAA1B,CADV,EAEL,CAACA,QAAD,EAAWW,OAAX,CAFK,CAAP,CAAA;EAID,CAAA;EAED;EACA;EACA;;EAMA;EACA;EACA;EACA;EACA;EACA;EACO,SAASG,WAAT,GAAyC;EAC9C,EAAA,CACEpB,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,wEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IAOA,IAAI;MAAEC,QAAF;EAAYC,IAAAA,SAAAA;EAAZ,GAAA,GAA0B5C,gBAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAA9B,CAAA;IACA,IAAI;EAAEI,IAAAA,OAAAA;EAAF,GAAA,GAAcpC,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;IACA,IAAI;EAAEa,IAAAA,QAAQ,EAAEe,gBAAAA;EAAZ,GAAA,GAAiCT,WAAW,EAAhD,CAAA;EAEA,EAAA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,wCAA0B,CAAC9B,OAAD,CAA1B,CAAoC+B,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB,CAAA;EAIA,EAAA,IAAIC,SAAS,GAAGtE,gBAAK,CAACuE,MAAN,CAAa,KAAb,CAAhB,CAAA;IACAvE,gBAAK,CAACH,SAAN,CAAgB,MAAM;MACpByE,SAAS,CAACE,OAAV,GAAoB,IAApB,CAAA;KADF,CAAA,CAAA;IAIA,IAAIC,QAA0B,GAAGzE,gBAAK,CAAC0E,WAAN,CAC/B,UAACnC,EAAD,EAAkBoC,OAAlB,EAAoD;EAAA,IAAA,IAAlCA,OAAkC,KAAA,KAAA,CAAA,EAAA;EAAlCA,MAAAA,OAAkC,GAAP,EAAO,CAAA;EAAA,KAAA;;EAClD,IAAAC,cAAO,CACLN,SAAS,CAACE,OADL,EAEL,oGAFK,CAAP,CAAA,CAAA;EAMA,IAAA,IAAI,CAACF,SAAS,CAACE,OAAf,EAAwB,OAAA;;EAExB,IAAA,IAAI,OAAOjC,EAAP,KAAc,QAAlB,EAA4B;QAC1BK,SAAS,CAACiC,EAAV,CAAatC,EAAb,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAIuC,IAAI,GAAGC,gBAAS,CAClBxC,EADkB,EAElByB,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFkB,EAGlBD,gBAHkB,EAIlBa,OAAO,CAACnC,QAAR,KAAqB,MAJH,CAApB,CAdkD;EAsBlD;EACA;EACA;;MACA,IAAIG,QAAQ,KAAK,GAAjB,EAAsB;QACpBmC,IAAI,CAAC/B,QAAL,GACE+B,IAAI,CAAC/B,QAAL,KAAkB,GAAlB,GACIJ,QADJ,GAEIQ,gBAAS,CAAC,CAACR,QAAD,EAAWmC,IAAI,CAAC/B,QAAhB,CAAD,CAHf,CAAA;EAID,KAAA;;MAED,CAAC,CAAC,CAAC4B,OAAO,CAACM,OAAV,GAAoBrC,SAAS,CAACqC,OAA9B,GAAwCrC,SAAS,CAACsC,IAAnD,EACEJ,IADF,EAEEH,OAAO,CAACQ,KAFV,EAGER,OAHF,CAAA,CAAA;KAjC6B,EAuC/B,CAAChC,QAAD,EAAWC,SAAX,EAAsBmB,kBAAtB,EAA0CD,gBAA1C,CAvC+B,CAAjC,CAAA;EA0CA,EAAA,OAAOW,QAAP,CAAA;EACD,CAAA;EAED,MAAMW,aAAa,gBAAGpF,gBAAK,CAAC4B,aAAN,CAA6B,IAA7B,CAAtB,CAAA;EAEA;EACA;EACA;EACA;EACA;;EACO,SAASyD,gBAAT,GAAwD;EAC7D,EAAA,OAAOrF,gBAAK,CAAC6C,UAAN,CAAiBuC,aAAjB,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;EACO,SAASE,SAAT,CAAmBC,OAAnB,EAAiE;IACtE,IAAIpD,MAAM,GAAGnC,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,EAA+BC,MAA5C,CAAA;;EACA,EAAA,IAAIA,MAAJ,EAAY;MACV,oBACEnC,gBAAA,CAAA,aAAA,CAAC,aAAD,CAAe,QAAf,EAAA;EAAwB,MAAA,KAAK,EAAEuF,OAAAA;EAA/B,KAAA,EAAyCpD,MAAzC,CADF,CAAA;EAGD,GAAA;;EACD,EAAA,OAAOA,MAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;;EACO,SAASqD,SAAT,GAIL;IACA,IAAI;EAAEpD,IAAAA,OAAAA;EAAF,GAAA,GAAcpC,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;IACA,IAAIuD,UAAU,GAAGrD,OAAO,CAACA,OAAO,CAACsD,MAAR,GAAiB,CAAlB,CAAxB,CAAA;EACA,EAAA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAf,GAAgC,EAAjD,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;;EACO,SAAS1C,eAAT,CACLV,EADK,EAGC,MAAA,EAAA;IAAA,IADN;EAAEC,IAAAA,QAAAA;EAAF,GACM,uBAD6C,EAC7C,GAAA,MAAA,CAAA;IACN,IAAI;EAAEJ,IAAAA,OAAAA;EAAF,GAAA,GAAcpC,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;IACA,IAAI;EAAEa,IAAAA,QAAQ,EAAEe,gBAAAA;EAAZ,GAAA,GAAiCT,WAAW,EAAhD,CAAA;EAEA,EAAA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,wCAA0B,CAAC9B,OAAD,CAA1B,CAAoC+B,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB,CAAA;EAIA,EAAA,OAAOrE,gBAAK,CAAC2D,OAAN,CACL,MACEoB,gBAAS,CACPxC,EADO,EAEPyB,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFO,EAGPD,gBAHO,EAIPtB,QAAQ,KAAK,MAJN,CAFN,EAQL,CAACD,EAAD,EAAKwB,kBAAL,EAAyBD,gBAAzB,EAA2CtB,QAA3C,CARK,CAAP,CAAA;EAUD,CAAA;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASoD,SAAT,CACLC,MADK,EAELC,WAFK,EAGsB;EAC3B,EAAA,CACErD,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,sEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;IAOA,IAAI;EAAEE,IAAAA,SAAAA;EAAF,GAAA,GAAgB5C,gBAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAApB,CAAA;EACA,EAAA,IAAI+D,sBAAsB,GAAG/F,gBAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAA7B,CAAA;IACA,IAAI;EAAEM,IAAAA,OAAO,EAAE4D,aAAAA;EAAX,GAAA,GAA6BhG,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAjC,CAAA;IACA,IAAIuD,UAAU,GAAGO,aAAa,CAACA,aAAa,CAACN,MAAd,GAAuB,CAAxB,CAA9B,CAAA;IACA,IAAIO,YAAY,GAAGR,UAAU,GAAGA,UAAU,CAACE,MAAd,GAAuB,EAApD,CAAA;IACA,IAAIO,cAAc,GAAGT,UAAU,GAAGA,UAAU,CAAC1C,QAAd,GAAyB,GAAxD,CAAA;IACA,IAAIoD,kBAAkB,GAAGV,UAAU,GAAGA,UAAU,CAACpB,YAAd,GAA6B,GAAhE,CAAA;EACA,EAAA,IAAI+B,WAAW,GAAGX,UAAU,IAAIA,UAAU,CAACY,KAA3C,CAAA;;IAEa;EACX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACA,IAAIC,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAACtB,IAA5B,IAAqC,EAAtD,CAAA;EACAyB,IAAAA,WAAW,CACTL,cADS,EAET,CAACE,WAAD,IAAgBE,UAAU,CAACE,QAAX,CAAoB,GAApB,CAFP,EAGT,gEAAA,IAAA,IAAA,GACMN,cADN,GAAA,0BAAA,GAC6CI,UAD7C,GAAA,eAAA,CAAA,GAAA,sEAAA,GAAA,iEAAA,GAAA,+BAAA,IAAA,yCAAA,GAK2CA,UAL3C,GAAA,gBAAA,CAAA,IAAA,SAAA,IAMWA,UAAU,KAAK,GAAf,GAAqB,GAArB,GAA8BA,UAA9B,GAAA,IANX,WAHS,CAAX,CAAA;EAWD,GAAA;;IAED,IAAIG,mBAAmB,GAAGpD,WAAW,EAArC,CAAA;EAEA,EAAA,IAAIC,QAAJ,CAAA;;EACA,EAAA,IAAIwC,WAAJ,EAAiB;EAAA,IAAA,IAAA,qBAAA,CAAA;;EACf,IAAA,IAAIY,iBAAiB,GACnB,OAAOZ,WAAP,KAAuB,QAAvB,GAAkCa,gBAAS,CAACb,WAAD,CAA3C,GAA2DA,WAD7D,CAAA;MAGA,EACEK,kBAAkB,KAAK,GAAvB,KACEO,CAAAA,qBAAAA,GAAAA,iBAAiB,CAAC3D,QADpB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAA4B6D,UAA5B,CAAuCT,kBAAvC,CADF,CADF,CAAA,GAAAzD,gBAAS,CAAA,KAAA,EAGP,2FAEiEyD,GAAAA,iFAAAA,IAAAA,+DAAAA,GAAAA,kBAFjE,GAGmBO,KAAAA,CAAAA,IAAAA,iBAAAA,GAAAA,iBAAiB,CAAC3D,QAHrC,GAHO,sCAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EASAO,IAAAA,QAAQ,GAAGoD,iBAAX,CAAA;EACD,GAdD,MAcO;EACLpD,IAAAA,QAAQ,GAAGmD,mBAAX,CAAA;EACD,GAAA;;EAED,EAAA,IAAI1D,QAAQ,GAAGO,QAAQ,CAACP,QAAT,IAAqB,GAApC,CAAA;EACA,EAAA,IAAI8D,iBAAiB,GACnBV,kBAAkB,KAAK,GAAvB,GACIpD,QADJ,GAEIA,QAAQ,CAAC+D,KAAT,CAAeX,kBAAkB,CAACT,MAAlC,KAA6C,GAHnD,CAAA;EAKA,EAAA,IAAItD,OAAO,GAAG2E,kBAAW,CAAClB,MAAD,EAAS;EAAE9C,IAAAA,QAAQ,EAAE8D,iBAAAA;EAAZ,GAAT,CAAzB,CAAA;;IAEa;EACX,IAAAjC,cAAO,CACLwB,WAAW,IAAIhE,OAAO,IAAI,IADrB,EAE0BkB,+BAAAA,GAAAA,QAAQ,CAACP,QAFnC,GAE8CO,QAAQ,CAACN,MAFvD,GAEgEM,QAAQ,CAACR,IAFzE,GAAP,KAAA,CAAA,CAAA,CAAA;EAKA,IAAA8B,cAAO,CACLxC,OAAO,IAAI,IAAX,IACEA,OAAO,CAACA,OAAO,CAACsD,MAAR,GAAiB,CAAlB,CAAP,CAA4BW,KAA5B,CAAkCW,OAAlC,KAA8CC,SAF3C,EAGL,mCAAmC3D,GAAAA,QAAQ,CAACP,QAA5C,GAAuDO,QAAQ,CAACN,MAAhE,GAAyEM,QAAQ,CAACR,IAAlF,2IAHK,CAAP,CAAA,CAAA;EAMD,GAAA;;EAED,EAAA,IAAIoE,eAAe,GAAGC,cAAc,CAClC/E,OAAO,IACLA,OAAO,CAAC+B,GAAR,CAAaC,KAAD,IACVzE,MAAM,CAACyH,MAAP,CAAc,EAAd,EAAkBhD,KAAlB,EAAyB;EACvBuB,IAAAA,MAAM,EAAEhG,MAAM,CAACyH,MAAP,CAAc,EAAd,EAAkBnB,YAAlB,EAAgC7B,KAAK,CAACuB,MAAtC,CADe;EAEvB5C,IAAAA,QAAQ,EAAEI,gBAAS,CAAC,CAClBgD,kBADkB;EAGlBvD,IAAAA,SAAS,CAACyE,cAAV,GACIzE,SAAS,CAACyE,cAAV,CAAyBjD,KAAK,CAACrB,QAA/B,EAAyCA,QAD7C,GAEIqB,KAAK,CAACrB,QALQ,CAAD,CAFI;EASvBsB,IAAAA,YAAY,EACVD,KAAK,CAACC,YAAN,KAAuB,GAAvB,GACI8B,kBADJ,GAEIhD,gBAAS,CAAC,CACRgD,kBADQ;EAGRvD,IAAAA,SAAS,CAACyE,cAAV,GACIzE,SAAS,CAACyE,cAAV,CAAyBjD,KAAK,CAACC,YAA/B,EAA6CtB,QADjD,GAEIqB,KAAK,CAACC,YALF,CAAD,CAAA;KAZjB,CADF,CAFgC,EAwBlC2B,aAxBkC,EAyBlCD,sBAAsB,IAAIkB,SAzBQ,CAApC,CA/F2B;EA4H3B;EACA;;;IACA,IAAInB,WAAW,IAAIoB,eAAnB,EAAoC;MAClC,oBACElH,gBAAA,CAAA,aAAA,CAAC,eAAD,CAAiB,QAAjB,EAAA;EACE,MAAA,KAAK,EAAE;UACLsD,QAAQ,EAAA,QAAA,CAAA;EACNP,UAAAA,QAAQ,EAAE,GADJ;EAENC,UAAAA,MAAM,EAAE,EAFF;EAGNF,UAAAA,IAAI,EAAE,EAHA;EAINqC,UAAAA,KAAK,EAAE,IAJD;EAKNmC,UAAAA,GAAG,EAAE,SAAA;EALC,SAAA,EAMHhE,QANG,CADH;UASLE,cAAc,EAAE+D,aAAc,CAACC,GAAAA;EAT1B,OAAA;EADT,KAAA,EAaGN,eAbH,CADF,CAAA;EAiBD,GAAA;;EAED,EAAA,OAAOA,eAAP,CAAA;EACD,CAAA;;EAED,SAASO,mBAAT,GAA+B;IAC7B,IAAIjH,KAAK,GAAGkH,aAAa,EAAzB,CAAA;EACA,EAAA,IAAIC,OAAO,GAAGC,2BAAoB,CAACpH,KAAD,CAApB,GACPA,KAAK,CAACqH,MADC,GACSrH,GAAAA,GAAAA,KAAK,CAACsH,UADf,GAEVtH,KAAK,YAAYuH,KAAjB,GACAvH,KAAK,CAACmH,OADN,GAEA3D,IAAI,CAACC,SAAL,CAAezD,KAAf,CAJJ,CAAA;IAKA,IAAIwH,KAAK,GAAGxH,KAAK,YAAYuH,KAAjB,GAAyBvH,KAAK,CAACwH,KAA/B,GAAuC,IAAnD,CAAA;IACA,IAAIC,SAAS,GAAG,wBAAhB,CAAA;EACA,EAAA,IAAIC,SAAS,GAAG;EAAEC,IAAAA,OAAO,EAAE,QAAX;EAAqBC,IAAAA,eAAe,EAAEH,SAAAA;KAAtD,CAAA;EACA,EAAA,IAAII,UAAU,GAAG;EAAEF,IAAAA,OAAO,EAAE,SAAX;EAAsBC,IAAAA,eAAe,EAAEH,SAAAA;KAAxD,CAAA;IAEA,IAAIK,OAAO,GAAG,IAAd,CAAA;;IACa;EACXA,IAAAA,OAAO,gBACLtI,gBAAA,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA,eACEA,gBADF,CAAA,aAAA,CAAA,GAAA,EAAA,IAAA,EAAA,yCAAA,CAAA,eAEEA,gBAGE,CAAA,aAAA,CAAA,GAAA,EAAA,IAAA,EAAA,iGAAA,eAAAA,gBAAA,CAAA,aAAA,CAAA,MAAA,EAAA;EAAM,MAAA,KAAK,EAAEqI,UAAAA;EAAb,KAAA,EAAA,cAAA,CAHF,EAIE,eAAA,eAAArI,gBAAA,CAAA,aAAA,CAAA,MAAA,EAAA;EAAM,MAAA,KAAK,EAAEqI,UAAAA;EAAb,KAAA,EAAA,SAAA,CAJF,CAFF,CADF,CAAA;EAWD,GAAA;;IAED,oBACErI,gBAAA,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA,eACEA,2EADF,eAEEA,gBAAA,CAAA,aAAA,CAAA,IAAA,EAAA;EAAI,IAAA,KAAK,EAAE;EAAEuI,MAAAA,SAAS,EAAE,QAAA;EAAb,KAAA;EAAX,GAAA,EAAqCZ,OAArC,CAFF,EAGGK,KAAK,gBAAGhI,gBAAA,CAAA,aAAA,CAAA,KAAA,EAAA;EAAK,IAAA,KAAK,EAAEkI,SAAAA;EAAZ,GAAA,EAAwBF,KAAxB,CAAH,GAA0C,IAHlD,EAIGM,OAJH,CADF,CAAA;EAQD,CAAA;;EAcM,MAAME,mBAAN,SAAkCxI,gBAAK,CAACyI,SAAxC,CAGL;IACAC,WAAW,CAACC,KAAD,EAAkC;EAC3C,IAAA,KAAA,CAAMA,KAAN,CAAA,CAAA;EACA,IAAA,IAAA,CAAKxD,KAAL,GAAa;QACX7B,QAAQ,EAAEqF,KAAK,CAACrF,QADL;QAEX9C,KAAK,EAAEmI,KAAK,CAACnI,KAAAA;OAFf,CAAA;EAID,GAAA;;IAE8B,OAAxBoI,wBAAwB,CAACpI,KAAD,EAAa;MAC1C,OAAO;EAAEA,MAAAA,KAAK,EAAEA,KAAAA;OAAhB,CAAA;EACD,GAAA;;EAE8B,EAAA,OAAxBqI,wBAAwB,CAC7BF,KAD6B,EAE7BxD,KAF6B,EAG7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAIA,KAAK,CAAC7B,QAAN,KAAmBqF,KAAK,CAACrF,QAA7B,EAAuC;QACrC,OAAO;UACL9C,KAAK,EAAEmI,KAAK,CAACnI,KADR;UAEL8C,QAAQ,EAAEqF,KAAK,CAACrF,QAAAA;SAFlB,CAAA;EAID,KAdD;EAiBA;EACA;EACA;;;MACA,OAAO;EACL9C,MAAAA,KAAK,EAAEmI,KAAK,CAACnI,KAAN,IAAe2E,KAAK,CAAC3E,KADvB;QAEL8C,QAAQ,EAAE6B,KAAK,CAAC7B,QAAAA;OAFlB,CAAA;EAID,GAAA;;EAEDwF,EAAAA,iBAAiB,CAACtI,KAAD,EAAauI,SAAb,EAA6B;EAC5CxI,IAAAA,OAAO,CAACC,KAAR,CACE,uDADF,EAEEA,KAFF,EAGEuI,SAHF,CAAA,CAAA;EAKD,GAAA;;EAEDC,EAAAA,MAAM,GAAG;MACP,OAAO,IAAA,CAAK7D,KAAL,CAAW3E,KAAX,gBACLR,gBAAC,CAAA,aAAA,CAAA,YAAD,CAAc,QAAd,EAAA;QAAuB,KAAK,EAAE,IAAK2I,CAAAA,KAAL,CAAWM,YAAAA;OACvC,eAAAjJ,gBAAA,CAAA,aAAA,CAAC,iBAAD,CAAmB,QAAnB,EAAA;EACE,MAAA,KAAK,EAAE,IAAA,CAAKmF,KAAL,CAAW3E,KADpB;QAEE,QAAQ,EAAE,IAAKmI,CAAAA,KAAL,CAAWO,SAAAA;EAFvB,KAAA,CADF,CADK,GAQL,IAAKP,CAAAA,KAAL,CAAWQ,QARb,CAAA;EAUD,GAAA;;EA7DD,CAAA;;EAsEF,SAASC,aAAT,CAA8E,IAAA,EAAA;IAAA,IAAvD;MAAEH,YAAF;MAAgB7E,KAAhB;EAAuB+E,IAAAA,QAAAA;KAAgC,GAAA,IAAA,CAAA;IAC5E,IAAIE,iBAAiB,GAAGrJ,gBAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAxB,CAD4E;EAI5E;;EACA,EAAA,IACE0H,iBAAiB,IACjBA,iBAAiB,CAACC,MADlB,IAEAD,iBAAiB,CAACE,aAFlB,IAGAnF,KAAK,CAACiC,KAAN,CAAYmD,YAJd,EAKE;MACAH,iBAAiB,CAACE,aAAlB,CAAgCE,0BAAhC,GAA6DrF,KAAK,CAACiC,KAAN,CAAYqD,EAAzE,CAAA;EACD,GAAA;;IAED,oBACE1J,gBAAA,CAAA,aAAA,CAAC,YAAD,CAAc,QAAd,EAAA;EAAuB,IAAA,KAAK,EAAEiJ,YAAAA;EAA9B,GAAA,EACGE,QADH,CADF,CAAA;EAKD,CAAA;;EAEM,SAAShC,cAAT,CACL/E,OADK,EAEL4D,aAFK,EAGL2D,eAHK,EAIsB;EAAA,EAAA,IAF3B3D,aAE2B,KAAA,KAAA,CAAA,EAAA;EAF3BA,IAAAA,aAE2B,GAFG,EAEH,CAAA;EAAA,GAAA;;IAC3B,IAAI5D,OAAO,IAAI,IAAf,EAAqB;EACnB,IAAA,IAAIuH,eAAJ,IAAA,IAAA,IAAIA,eAAe,CAAEC,MAArB,EAA6B;EAC3B;EACA;QACAxH,OAAO,GAAGuH,eAAe,CAACvH,OAA1B,CAAA;EACD,KAJD,MAIO;EACL,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,IAAI8E,eAAe,GAAG9E,OAAtB,CAX2B;;EAc3B,EAAA,IAAIwH,MAAM,GAAGD,eAAH,IAAGA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAe,CAAEC,MAA9B,CAAA;;IACA,IAAIA,MAAM,IAAI,IAAd,EAAoB;MAClB,IAAIC,UAAU,GAAG3C,eAAe,CAAC4C,SAAhB,CACdC,CAAD,IAAOA,CAAC,CAAC1D,KAAF,CAAQqD,EAAR,KAAcE,MAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAAGG,CAAC,CAAC1D,KAAF,CAAQqD,EAAX,CAApB,CADQ,CAAjB,CAAA;MAGA,EACEG,UAAU,IAAI,CADhB,CAAAnH,GAAAA,gBAAS,qEAEoDkH,MAFpD,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAIA1C,IAAAA,eAAe,GAAGA,eAAe,CAACJ,KAAhB,CAChB,CADgB,EAEhBkD,IAAI,CAACC,GAAL,CAAS/C,eAAe,CAACxB,MAAzB,EAAiCmE,UAAU,GAAG,CAA9C,CAFgB,CAAlB,CAAA;EAID,GAAA;;IAED,OAAO3C,eAAe,CAACgD,WAAhB,CAA4B,CAAC/H,MAAD,EAASiC,KAAT,EAAgB+F,KAAhB,KAA0B;MAC3D,IAAI3J,KAAK,GAAG4D,KAAK,CAACiC,KAAN,CAAYqD,EAAZ,GAAiBE,MAAjB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAiBA,MAAM,CAAGxF,KAAK,CAACiC,KAAN,CAAYqD,EAAf,CAAvB,GAA4C,IAAxD,CAD2D;;EAG3D,IAAA,IAAIF,YAAY,GAAGG,eAAe,GAC9BvF,KAAK,CAACiC,KAAN,CAAYmD,YAAZ,iBAA4BxJ,gBAAA,CAAA,aAAA,CAAC,mBAAD,EAAA,IAAA,CADE,GAE9B,IAFJ,CAAA;EAGA,IAAA,IAAIoC,OAAO,GAAG4D,aAAa,CAACoE,MAAd,CAAqBlD,eAAe,CAACJ,KAAhB,CAAsB,CAAtB,EAAyBqD,KAAK,GAAG,CAAjC,CAArB,CAAd,CAAA;;EACA,IAAA,IAAIE,WAAW,GAAG,mBAChBrK,gBAAA,CAAA,aAAA,CAAC,aAAD,EAAA;EAAe,MAAA,KAAK,EAAEoE,KAAtB;EAA6B,MAAA,YAAY,EAAE;UAAEjC,MAAF;EAAUC,QAAAA,OAAAA;EAAV,OAAA;OACxC5B,EAAAA,KAAK,GACFgJ,YADE,GAEFpF,KAAK,CAACiC,KAAN,CAAYW,OAAZ,KAAwBC,SAAxB,GACA7C,KAAK,CAACiC,KAAN,CAAYW,OADZ,GAEA7E,MALN,CADF,CAP2D;EAiB3D;EACA;;;EACA,IAAA,OAAOwH,eAAe,KAAKvF,KAAK,CAACiC,KAAN,CAAYmD,YAAZ,IAA4BW,KAAK,KAAK,CAA3C,CAAf,gBACLnK,+BAAC,mBAAD,EAAA;QACE,QAAQ,EAAE2J,eAAe,CAACrG,QAD5B;EAEE,MAAA,SAAS,EAAEkG,YAFb;EAGE,MAAA,KAAK,EAAEhJ,KAHT;QAIE,QAAQ,EAAE6J,WAAW,EAJvB;EAKE,MAAA,YAAY,EAAE;EAAElI,QAAAA,MAAM,EAAE,IAAV;EAAgBC,QAAAA,OAAAA;EAAhB,OAAA;OANX,CAAA,GASLiI,WAAW,EATb,CAAA;KAnBK,EA8BJ,IA9BI,CAAP,CAAA;EA+BD,CAAA;MAEIC;;aAAAA;IAAAA;IAAAA;EAAAA,CAAAA,EAAAA,mBAAAA;;MAKAC;;aAAAA;IAAAA;IAAAA;IAAAA;IAAAA;IAAAA;IAAAA;IAAAA;EAAAA,CAAAA,EAAAA,wBAAAA;;EAUL,SAASC,yBAAT,CACEC,QADF,EAEE;EACA,EAAA,OAAUA,QAAV,GAAA,4FAAA,CAAA;EACD,CAAA;;EAED,SAASC,oBAAT,CAA8BD,QAA9B,EAAwD;EACtD,EAAA,IAAIE,GAAG,GAAG3K,gBAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAV,CAAA;IACA,CAAUgJ,GAAV,GAAAjI,gBAAS,CAAM8H,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAA/B,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EACA,EAAA,OAAOE,GAAP,CAAA;EACD,CAAA;;EAED,SAASC,kBAAT,CAA4BH,QAA5B,EAA2D;EACzD,EAAA,IAAItF,KAAK,GAAGnF,gBAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAAZ,CAAA;IACA,CAAUqD,KAAV,GAAAzC,gBAAS,CAAQ8H,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAAjC,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EACA,EAAA,OAAOtF,KAAP,CAAA;EACD,CAAA;;EAED,SAAS0F,eAAT,CAAyBJ,QAAzB,EAAwD;EACtD,EAAA,IAAIpE,KAAK,GAAGrG,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAZ,CAAA;IACA,CAAUmE,KAAV,GAAA3D,gBAAS,CAAQ8H,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAAjC,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EACA,EAAA,OAAOpE,KAAP,CAAA;EACD,CAAA;;EAED,SAASyE,iBAAT,CAA2BL,QAA3B,EAA0D;EACxD,EAAA,IAAIpE,KAAK,GAAGwE,eAAe,CAACJ,QAAD,CAA3B,CAAA;EACA,EAAA,IAAIM,SAAS,GAAG1E,KAAK,CAACjE,OAAN,CAAciE,KAAK,CAACjE,OAAN,CAAcsD,MAAd,GAAuB,CAArC,CAAhB,CAAA;EACA,EAAA,CACEqF,SAAS,CAAC1E,KAAV,CAAgBqD,EADlB,GAAAhH,gBAAS,CAAA,KAAA,EAEJ+H,QAFI,GAAA,0DAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAIA,EAAA,OAAOM,SAAS,CAAC1E,KAAV,CAAgBqD,EAAvB,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;;;EACO,SAASsB,aAAT,GAAyB;EAC9B,EAAA,IAAI7F,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAACU,aAArB,CAA9B,CAAA;IACA,OAAO9F,KAAK,CAAC+F,UAAb,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASC,cAAT,GAA0B;EAC/B,EAAA,IAAI9B,iBAAiB,GAAGqB,oBAAoB,CAACJ,cAAc,CAACc,cAAhB,CAA5C,CAAA;EACA,EAAA,IAAIjG,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAACa,cAArB,CAA9B,CAAA;IACA,OAAO;EACLC,IAAAA,UAAU,EAAEhC,iBAAiB,CAACiC,MAAlB,CAAyBD,UADhC;MAELlG,KAAK,EAAEA,KAAK,CAACoG,YAAAA;KAFf,CAAA;EAID,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASC,UAAT,GAAsB;IAC3B,IAAI;MAAEpJ,OAAF;EAAWqJ,IAAAA,UAAAA;EAAX,GAAA,GAA0Bb,kBAAkB,CAC9CL,mBAAmB,CAACmB,UAD0B,CAAhD,CAAA;IAGA,OAAO1L,gBAAK,CAAC2D,OAAN,CACL,MACEvB,OAAO,CAAC+B,GAAR,CAAaC,KAAD,IAAW;MACrB,IAAI;QAAErB,QAAF;EAAY4C,MAAAA,MAAAA;OAAWvB,GAAAA,KAA3B,CADqB;EAGrB;EACA;;MACA,OAAO;EACLsF,MAAAA,EAAE,EAAEtF,KAAK,CAACiC,KAAN,CAAYqD,EADX;QAEL3G,QAFK;QAGL4C,MAHK;QAILgG,IAAI,EAAEF,UAAU,CAACrH,KAAK,CAACiC,KAAN,CAAYqD,EAAb,CAJX;EAKLkC,MAAAA,MAAM,EAAExH,KAAK,CAACiC,KAAN,CAAYuF,MAAAA;OALtB,CAAA;EAOD,GAZD,CAFG,EAeL,CAACxJ,OAAD,EAAUqJ,UAAV,CAfK,CAAP,CAAA;EAiBD,CAAA;EAED;EACA;EACA;;EACO,SAASI,aAAT,GAAkC;EACvC,EAAA,IAAI1G,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAACuB,aAArB,CAA9B,CAAA;EACA,EAAA,IAAIC,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAACuB,aAArB,CAA/B,CAAA;;IAEA,IAAI3G,KAAK,CAACyE,MAAN,IAAgBzE,KAAK,CAACyE,MAAN,CAAamC,OAAb,CAAyB,IAAA,IAA7C,EAAmD;MACjDxL,OAAO,CAACC,KAAR,CAAA,0DAAA,GAC+DuL,OAD/D,GAAA,GAAA,CAAA,CAAA;EAGA,IAAA,OAAO9E,SAAP,CAAA;EACD,GAAA;;EACD,EAAA,OAAO9B,KAAK,CAACsG,UAAN,CAAiBM,OAAjB,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASC,kBAAT,CAA4BD,OAA5B,EAAsD;EAC3D,EAAA,IAAI5G,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAAC0B,kBAArB,CAA9B,CAAA;EACA,EAAA,OAAO9G,KAAK,CAACsG,UAAN,CAAiBM,OAAjB,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASG,aAAT,GAAkC;EACvC,EAAA,IAAI/G,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAAC4B,aAArB,CAA9B,CAAA;EAEA,EAAA,IAAI9F,KAAK,GAAGrG,gBAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAZ,CAAA;EACA,EAAA,CAAUmE,KAAV,GAAA3D,gBAAS,CAAT,KAAA,EAAA,kDAAA,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAEA,EAAA,OAAO/C,MAAM,CAACyM,MAAP,CAAc,CAAAjH,KAAK,IAAA,IAAL,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAAEkH,UAAP,KAAqB,EAAnC,CAAA,CAAuC,CAAvC,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;;EACO,SAAS3E,aAAT,GAAkC;EAAA,EAAA,IAAA,aAAA,CAAA;;EACvC,EAAA,IAAIlH,KAAK,GAAGR,gBAAK,CAAC6C,UAAN,CAAiBR,iBAAjB,CAAZ,CAAA;EACA,EAAA,IAAI8C,KAAK,GAAGyF,kBAAkB,CAACL,mBAAmB,CAAC+B,aAArB,CAA9B,CAAA;IACA,IAAIP,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAAC+B,aAArB,CAA/B,CAHuC;EAMvC;;EACA,EAAA,IAAI9L,KAAJ,EAAW;EACT,IAAA,OAAOA,KAAP,CAAA;EACD,GATsC;;;EAYvC,EAAA,OAAA,CAAA,aAAA,GAAO2E,KAAK,CAACyE,MAAb,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAemC,OAAf,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASQ,aAAT,GAAkC;EACvC,EAAA,IAAI9L,KAAK,GAAGT,gBAAK,CAAC6C,UAAN,CAAiBd,YAAjB,CAAZ,CAAA;EACA,EAAA,OAAOtB,KAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,KAAK,CAAE+L,KAAd,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASC,aAAT,GAAkC;EACvC,EAAA,IAAIhM,KAAK,GAAGT,gBAAK,CAAC6C,UAAN,CAAiBd,YAAjB,CAAZ,CAAA;EACA,EAAA,OAAOtB,KAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,KAAK,CAAEiM,MAAd,CAAA;EACD,CAAA;EAED,IAAIC,SAAS,GAAG,CAAhB,CAAA;EAEA;EACA;EACA;EACA;EACA;EACA;;EACO,SAASC,UAAT,CAAoBC,WAApB,EAAqE;IAC1E,IAAI;EAAEvB,IAAAA,MAAAA;EAAF,GAAA,GAAaZ,oBAAoB,CAACJ,cAAc,CAACwC,UAAhB,CAArC,CAAA;EACA,EAAA,IAAI,CAACC,UAAD,CAAe/M,GAAAA,gBAAK,CAACJ,QAAN,CAAe,MAAMoN,MAAM,CAAC,EAAEL,SAAH,CAA3B,CAAnB,CAAA;EAEA,EAAA,IAAIM,eAAe,GAAGjN,gBAAK,CAAC0E,WAAN,CACnBwI,IAAD,IAAU;EACR,IAAA,OAAO,OAAOL,WAAP,KAAuB,UAAvB,GACH,CAAC,CAACA,WAAW,CAACK,IAAD,CADV,GAEH,CAAC,CAACL,WAFN,CAAA;EAGD,GALmB,EAMpB,CAACA,WAAD,CANoB,CAAtB,CAAA;IASA,IAAIM,OAAO,GAAG7B,MAAM,CAAC8B,UAAP,CAAkBL,UAAlB,EAA8BE,eAA9B,CAAd,CAb0E;;EAgB1EjN,EAAAA,gBAAK,CAACH,SAAN,CACE,MAAM,MAAMyL,MAAM,CAAC+B,aAAP,CAAqBN,UAArB,CADd,EAEE,CAACzB,MAAD,EAASyB,UAAT,CAFF,CAAA,CAAA;EAKA,EAAA,OAAOI,OAAP,CAAA;EACD,CAAA;EAED,MAAMG,aAAsC,GAAG,EAA/C,CAAA;;EAEA,SAAS/G,WAAT,CAAqBe,GAArB,EAAkCiG,IAAlC,EAAiD5F,OAAjD,EAAkE;IAChE,IAAI,CAAC4F,IAAD,IAAS,CAACD,aAAa,CAAChG,GAAD,CAA3B,EAAkC;EAChCgG,IAAAA,aAAa,CAAChG,GAAD,CAAb,GAAqB,IAArB,CAAA;EACA,IAAA1C,cAAO,CAAC,KAAD,EAAQ+C,OAAR,CAAP,CAAA,CAAA;EACD,GAAA;EACF;;ECrzBD;EACA;EACA;EACO,SAAS6F,cAAT,CAGqC,IAAA,EAAA;IAAA,IAHb;MAC7BC,eAD6B;EAE7BnC,IAAAA,MAAAA;KAC0C,GAAA,IAAA,CAAA;EAC1C;EACA,EAAA,IAAInG,KAAkB,GAAGuI,oBAAwB,CAC/CpC,MAAM,CAAClL,SADwC,EAE/C,MAAMkL,MAAM,CAACnG,KAFkC;EAI/C;EACA;IACA,MAAMmG,MAAM,CAACnG,KANkC,CAAjD,CAAA;EASA,EAAA,IAAIvC,SAAS,GAAG5C,gBAAK,CAAC2D,OAAN,CAAc,MAAiB;MAC7C,OAAO;QACLP,UAAU,EAAEkI,MAAM,CAAClI,UADd;QAELiE,cAAc,EAAEiE,MAAM,CAACjE,cAFlB;QAGLxC,EAAE,EAAG8I,CAAD,IAAOrC,MAAM,CAAC7G,QAAP,CAAgBkJ,CAAhB,CAHN;EAILzI,MAAAA,IAAI,EAAE,CAAC3C,EAAD,EAAK4C,KAAL,EAAYyI,IAAZ,KACJtC,MAAM,CAAC7G,QAAP,CAAgBlC,EAAhB,EAAoB;UAClB4C,KADkB;EAElB0I,QAAAA,kBAAkB,EAAED,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;EAFR,OAApB,CALG;EASL5I,MAAAA,OAAO,EAAE,CAAC1C,EAAD,EAAK4C,KAAL,EAAYyI,IAAZ,KACPtC,MAAM,CAAC7G,QAAP,CAAgBlC,EAAhB,EAAoB;EAClB0C,QAAAA,OAAO,EAAE,IADS;UAElBE,KAFkB;EAGlB0I,QAAAA,kBAAkB,EAAED,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEC,kBAAAA;SAH5B,CAAA;OAVJ,CAAA;EAgBD,GAjBe,EAiBb,CAACvC,MAAD,CAjBa,CAAhB,CAAA;IAmBA,IAAI3I,QAAQ,GAAG2I,MAAM,CAAC3I,QAAP,IAAmB,GAAlC,CA9B0C;EAiC1C;EACA;EACA;EACA;EACA;;EACA,EAAA,oBACE3C,gBACE,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA,eAAAA,gBAAA,CAAA,aAAA,CAAC,iBAAD,CAAmB,QAAnB,EAAA;EACE,IAAA,KAAK,EAAE;QACLsL,MADK;QAEL1I,SAFK;EAGL0G,MAAAA,MAAM,EAAE,KAHH;EAIL;EACA3G,MAAAA,QAAAA;EALK,KAAA;KAQP,eAAA3C,gBAAA,CAAA,aAAA,CAAC,sBAAD,CAAwB,QAAxB,EAAA;EAAiC,IAAA,KAAK,EAAEmF,KAAAA;EAAxC,GAAA,eACEnF,+BAAC,MAAD,EAAA;MACE,QAAQ,EAAEsL,MAAM,CAAC3I,QADnB;EAEE,IAAA,QAAQ,EAAE2I,MAAM,CAACnG,KAAP,CAAa7B,QAFzB;EAGE,IAAA,cAAc,EAAEgI,MAAM,CAACnG,KAAP,CAAa2I,aAH/B;EAIE,IAAA,SAAS,EAAElL,SAAAA;EAJb,GAAA,EAMG0I,MAAM,CAACnG,KAAP,CAAa4I,WAAb,gBAA2B/N,gBAAC,CAAA,aAAA,CAAA,MAAD,EAA3B,IAAA,CAAA,GAAwCyN,eAN3C,CADF,CATF,CADF,EAqBG,IArBH,CADF,CAAA;EAyBD,CAAA;;EASD;EACA;EACA;EACA;EACA;EACO,SAASO,YAAT,CAKmC,KAAA,EAAA;IAAA,IALb;MAC3BrL,QAD2B;MAE3BwG,QAF2B;MAG3B8E,cAH2B;EAI3BC,IAAAA,YAAAA;KACwC,GAAA,KAAA,CAAA;EACxC,EAAA,IAAIC,UAAU,GAAGnO,gBAAK,CAACuE,MAAN,EAAjB,CAAA;;EACA,EAAA,IAAI4J,UAAU,CAAC3J,OAAX,IAAsB,IAA1B,EAAgC;EAC9B2J,IAAAA,UAAU,CAAC3J,OAAX,GAAqB4J,0BAAmB,CAAC;QACvCH,cADuC;QAEvCC,YAFuC;EAGvCG,MAAAA,QAAQ,EAAE,IAAA;EAH6B,KAAD,CAAxC,CAAA;EAKD,GAAA;;EAED,EAAA,IAAIC,OAAO,GAAGH,UAAU,CAAC3J,OAAzB,CAAA;IACA,IAAI,CAACW,KAAD,EAAQoJ,QAAR,IAAoBvO,gBAAK,CAACJ,QAAN,CAAe;MACrC4O,MAAM,EAAEF,OAAO,CAACE,MADqB;MAErClL,QAAQ,EAAEgL,OAAO,CAAChL,QAAAA;EAFmB,GAAf,CAAxB,CAAA;EAKAtD,EAAAA,gBAAK,CAACF,eAAN,CAAsB,MAAMwO,OAAO,CAACG,MAAR,CAAeF,QAAf,CAA5B,EAAsD,CAACD,OAAD,CAAtD,CAAA,CAAA;EAEA,EAAA,oBACEtO,+BAAC,MAAD,EAAA;EACE,IAAA,QAAQ,EAAE2C,QADZ;EAEE,IAAA,QAAQ,EAAEwG,QAFZ;MAGE,QAAQ,EAAEhE,KAAK,CAAC7B,QAHlB;MAIE,cAAc,EAAE6B,KAAK,CAACqJ,MAJxB;EAKE,IAAA,SAAS,EAAEF,OAAAA;KANf,CAAA,CAAA;EASD,CAAA;;EASD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASI,QAAT,CAKiB,KAAA,EAAA;IAAA,IALC;MACvBnM,EADuB;MAEvB0C,OAFuB;MAGvBE,KAHuB;EAIvB3C,IAAAA,QAAAA;KACsB,GAAA,KAAA,CAAA;EACtB,EAAA,CACEC,kBAAkB,EADpB,GAAAC,gBAAS,CAEP,KAAA;EACA;IAHO,qEAAT,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,EAAAkC,cAAO,CACL,CAAC5E,gBAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAAoCsH,CAAAA,MADhC,EAEL,yEAAA,GAAA,wEAAA,GAAA,0EAFK,CAAP,CAAA,CAAA;EAOA,EAAA,IAAIK,eAAe,GAAG3J,gBAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAAtB,CAAA;IACA,IAAI2C,QAAQ,GAAGZ,WAAW,EAA1B,CAAA;IAEA7D,gBAAK,CAACH,SAAN,CAAgB,MAAM;EACpB;EACA;EACA;MACA,IAAI8J,eAAe,IAAIA,eAAe,CAACuB,UAAhB,CAA2B/F,KAA3B,KAAqC,MAA5D,EAAoE;EAClE,MAAA,OAAA;EACD,KAAA;;MACDV,QAAQ,CAAClC,EAAD,EAAK;QAAE0C,OAAF;QAAWE,KAAX;EAAkB3C,MAAAA,QAAAA;EAAlB,KAAL,CAAR,CAAA;KAPF,CAAA,CAAA;EAUA,EAAA,OAAO,IAAP,CAAA;EACD,CAAA;;EAMD;EACA;EACA;EACA;EACA;EACO,SAASmM,MAAT,CAAgBhG,KAAhB,EAA+D;EACpE,EAAA,OAAOrD,SAAS,CAACqD,KAAK,CAACpD,OAAP,CAAhB,CAAA;EACD,CAAA;;EAoCD;EACA;EACA;EACA;EACA;EACO,SAASqJ,KAAT,CAAeC,MAAf,EAA8D;IACnEnM,gBAAS,CAAA,KAAA,EAEP,2IAFO,CAAT,CAAA,CAAA,CAAA;EAKD,CAAA;;EAWD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASoM,MAAT,CAOoC,KAAA,EAAA;IAAA,IAPpB;MACrBnM,QAAQ,EAAEoM,YAAY,GAAG,GADJ;EAErB5F,IAAAA,QAAQ,GAAG,IAFU;EAGrB7F,IAAAA,QAAQ,EAAE0L,YAHW;MAIrBxL,cAAc,GAAG+D,aAAc,CAACC,GAJX;MAKrB5E,SALqB;MAMrB0G,MAAM,EAAE2F,UAAU,GAAG,KAAA;KACoB,GAAA,KAAA,CAAA;EACzC,EAAA,CACE,CAACxM,kBAAkB,EADrB,GAAAC,gBAAS,CAAA,KAAA,EAEP,uDAFO,GAAA,mDAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CADyC;EAQzC;;IACA,IAAIC,QAAQ,GAAGoM,YAAY,CAAC9J,OAAb,CAAqB,MAArB,EAA6B,GAA7B,CAAf,CAAA;EACA,EAAA,IAAIiK,iBAAiB,GAAGlP,gBAAK,CAAC2D,OAAN,CACtB,OAAO;MAAEhB,QAAF;MAAYC,SAAZ;EAAuB0G,IAAAA,MAAM,EAAE2F,UAAAA;KAAtC,CADsB,EAEtB,CAACtM,QAAD,EAAWC,SAAX,EAAsBqM,UAAtB,CAFsB,CAAxB,CAAA;;EAKA,EAAA,IAAI,OAAOD,YAAP,KAAwB,QAA5B,EAAsC;EACpCA,IAAAA,YAAY,GAAGrI,gBAAS,CAACqI,YAAD,CAAxB,CAAA;EACD,GAAA;;IAED,IAAI;EACFjM,IAAAA,QAAQ,GAAG,GADT;EAEFC,IAAAA,MAAM,GAAG,EAFP;EAGFF,IAAAA,IAAI,GAAG,EAHL;EAIFqC,IAAAA,KAAK,GAAG,IAJN;EAKFmC,IAAAA,GAAG,GAAG,SAAA;EALJ,GAAA,GAMA0H,YANJ,CAAA;EAQA,EAAA,IAAI1L,QAAQ,GAAGtD,gBAAK,CAAC2D,OAAN,CAAc,MAAM;EACjC,IAAA,IAAIwL,gBAAgB,GAAGC,oBAAa,CAACrM,QAAD,EAAWJ,QAAX,CAApC,CAAA;;MAEA,IAAIwM,gBAAgB,IAAI,IAAxB,EAA8B;EAC5B,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;;MAED,OAAO;EACLpM,MAAAA,QAAQ,EAAEoM,gBADL;QAELnM,MAFK;QAGLF,IAHK;QAILqC,KAJK;EAKLmC,MAAAA,GAAAA;OALF,CAAA;EAOD,GAdc,EAcZ,CAAC3E,QAAD,EAAWI,QAAX,EAAqBC,MAArB,EAA6BF,IAA7B,EAAmCqC,KAAnC,EAA0CmC,GAA1C,CAdY,CAAf,CAAA;EAgBA,EAAA1C,cAAO,CACLtB,QAAQ,IAAI,IADP,EAEL,qBAAA,GAAqBX,QAArB,GAAA,mCAAA,IAAA,IAAA,GACMI,QADN,GACiBC,MADjB,GAC0BF,IAD1B,iGAFK,CAAP,CAAA,CAAA;;IAOA,IAAIQ,QAAQ,IAAI,IAAhB,EAAsB;EACpB,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;IAED,oBACEtD,gBAAA,CAAA,aAAA,CAAC,iBAAD,CAAmB,QAAnB,EAAA;EAA4B,IAAA,KAAK,EAAEkP,iBAAAA;KACjC,eAAAlP,gBAAA,CAAA,aAAA,CAAC,eAAD,CAAiB,QAAjB,EAAA;EACE,IAAA,QAAQ,EAAEmJ,QADZ;EAEE,IAAA,KAAK,EAAE;QAAE7F,QAAF;EAAYE,MAAAA,cAAAA;EAAZ,KAAA;EAFT,GAAA,CADF,CADF,CAAA;EAQD,CAAA;;EAOD;EACA;EACA;EACA;EACA;EACA;EACO,SAAS6L,MAAT,CAGoC,KAAA,EAAA;IAAA,IAHpB;MACrBlG,QADqB;EAErB7F,IAAAA,QAAAA;KACyC,GAAA,KAAA,CAAA;IACzC,IAAI+F,iBAAiB,GAAGrJ,gBAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAxB,CADyC;EAGzC;EACA;;EACA,EAAA,IAAIkE,MAAM,GACRwD,iBAAiB,IAAI,CAACF,QAAtB,GACKE,iBAAiB,CAACiC,MAAlB,CAAyBzF,MAD9B,GAEIyJ,wBAAwB,CAACnG,QAAD,CAH9B,CAAA;EAIA,EAAA,OAAOvD,SAAS,CAACC,MAAD,EAASvC,QAAT,CAAhB,CAAA;EACD,CAAA;;EAYD;EACA;EACA;EACA;EACO,SAASiM,KAAT,CAAgE,KAAA,EAAA;IAAA,IAAjD;MAAEpG,QAAF;MAAYK,YAAZ;EAA0BgG,IAAAA,OAAAA;KAAuB,GAAA,KAAA,CAAA;EACrE,EAAA,oBACExP,+BAAC,kBAAD,EAAA;EAAoB,IAAA,OAAO,EAAEwP,OAA7B;EAAsC,IAAA,YAAY,EAAEhG,YAAAA;EAApD,GAAA,eACExJ,gBAAC,CAAA,aAAA,CAAA,YAAD,EAAemJ,IAAAA,EAAAA,QAAf,CADF,CADF,CAAA;EAKD,CAAA;MAWIsG;;aAAAA;EAAAA,EAAAA,kBAAAA;EAAAA,EAAAA,kBAAAA;EAAAA,EAAAA,kBAAAA;EAAAA,CAAAA,EAAAA,sBAAAA;;EAML,MAAMC,mBAAmB,GAAG,IAAIC,OAAJ,CAAY,MAAM,EAAlB,CAA5B,CAAA;;EAEA,MAAMC,kBAAN,SAAiC5P,gBAAK,CAACyI,SAAvC,CAGE;IACAC,WAAW,CAACC,KAAD,EAAiC;EAC1C,IAAA,KAAA,CAAMA,KAAN,CAAA,CAAA;EACA,IAAA,IAAA,CAAKxD,KAAL,GAAa;EAAE3E,MAAAA,KAAK,EAAE,IAAA;OAAtB,CAAA;EACD,GAAA;;IAE8B,OAAxBoI,wBAAwB,CAACpI,KAAD,EAAa;MAC1C,OAAO;EAAEA,MAAAA,KAAAA;OAAT,CAAA;EACD,GAAA;;EAEDsI,EAAAA,iBAAiB,CAACtI,KAAD,EAAauI,SAAb,EAA6B;EAC5CxI,IAAAA,OAAO,CAACC,KAAR,CACE,kDADF,EAEEA,KAFF,EAGEuI,SAHF,CAAA,CAAA;EAKD,GAAA;;EAEDC,EAAAA,MAAM,GAAG;MACP,IAAI;QAAEG,QAAF;QAAYK,YAAZ;EAA0BgG,MAAAA,OAAAA;EAA1B,KAAA,GAAsC,KAAK7G,KAA/C,CAAA;MAEA,IAAIkH,OAA8B,GAAG,IAArC,CAAA;EACA,IAAA,IAAIhI,MAAyB,GAAG4H,iBAAiB,CAACK,OAAlD,CAAA;;EAEA,IAAA,IAAI,EAAEN,OAAO,YAAYG,OAArB,CAAJ,EAAmC;EACjC;QACA9H,MAAM,GAAG4H,iBAAiB,CAACM,OAA3B,CAAA;EACAF,MAAAA,OAAO,GAAGF,OAAO,CAACH,OAAR,EAAV,CAAA;EACA7P,MAAAA,MAAM,CAACqQ,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;EAAEI,QAAAA,GAAG,EAAE,MAAM,IAAA;SAAxD,CAAA,CAAA;EACAtQ,MAAAA,MAAM,CAACqQ,cAAP,CAAsBH,OAAtB,EAA+B,OAA/B,EAAwC;EAAEI,QAAAA,GAAG,EAAE,MAAMT,OAAAA;SAArD,CAAA,CAAA;EACD,KAND,MAMO,IAAI,IAAA,CAAKrK,KAAL,CAAW3E,KAAf,EAAsB;EAC3B;QACAqH,MAAM,GAAG4H,iBAAiB,CAACjP,KAA3B,CAAA;EACA,MAAA,IAAI0P,WAAW,GAAG,IAAK/K,CAAAA,KAAL,CAAW3E,KAA7B,CAAA;EACAqP,MAAAA,OAAO,GAAGF,OAAO,CAACQ,MAAR,EAAiBC,CAAAA,KAAjB,CAAuB,MAAM,EAA7B,CAAV,CAJ2B;;EAK3BzQ,MAAAA,MAAM,CAACqQ,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;EAAEI,QAAAA,GAAG,EAAE,MAAM,IAAA;SAAxD,CAAA,CAAA;EACAtQ,MAAAA,MAAM,CAACqQ,cAAP,CAAsBH,OAAtB,EAA+B,QAA/B,EAAyC;EAAEI,QAAAA,GAAG,EAAE,MAAMC,WAAAA;SAAtD,CAAA,CAAA;EACD,KAPM,MAOA,IAAKV,OAAD,CAA4Ba,QAAhC,EAA0C;EAC/C;EACAR,MAAAA,OAAO,GAAGL,OAAV,CAAA;QACA3H,MAAM,GACJgI,OAAO,CAACnD,MAAR,KAAmBzF,SAAnB,GACIwI,iBAAiB,CAACjP,KADtB,GAEIqP,OAAO,CAACrD,KAAR,KAAkBvF,SAAlB,GACAwI,iBAAiB,CAACM,OADlB,GAEAN,iBAAiB,CAACK,OALxB,CAAA;EAMD,KATM,MASA;EACL;QACAjI,MAAM,GAAG4H,iBAAiB,CAACK,OAA3B,CAAA;EACAnQ,MAAAA,MAAM,CAACqQ,cAAP,CAAsBR,OAAtB,EAA+B,UAA/B,EAA2C;EAAES,QAAAA,GAAG,EAAE,MAAM,IAAA;SAAxD,CAAA,CAAA;EACAJ,MAAAA,OAAO,GAAGL,OAAO,CAACc,IAAR,CACP3E,IAAD,IACEhM,MAAM,CAACqQ,cAAP,CAAsBR,OAAtB,EAA+B,OAA/B,EAAwC;EAAES,QAAAA,GAAG,EAAE,MAAMtE,IAAAA;SAArD,CAFM,EAGPnL,KAAD,IACEb,MAAM,CAACqQ,cAAP,CAAsBR,OAAtB,EAA+B,QAA/B,EAAyC;EAAES,QAAAA,GAAG,EAAE,MAAMzP,KAAAA;EAAb,OAAzC,CAJM,CAAV,CAAA;EAMD,KAAA;;MAED,IACEqH,MAAM,KAAK4H,iBAAiB,CAACjP,KAA7B,IACAqP,OAAO,CAACnD,MAAR,YAA0B6D,2BAF5B,EAGE;EACA;EACA,MAAA,MAAMb,mBAAN,CAAA;EACD,KAAA;;MAED,IAAI7H,MAAM,KAAK4H,iBAAiB,CAACjP,KAA7B,IAAsC,CAACgJ,YAA3C,EAAyD;EACvD;QACA,MAAMqG,OAAO,CAACnD,MAAd,CAAA;EACD,KAAA;;EAED,IAAA,IAAI7E,MAAM,KAAK4H,iBAAiB,CAACjP,KAAjC,EAAwC;EACtC;QACA,oBAAOR,gBAAA,CAAA,aAAA,CAAC,YAAD,CAAc,QAAd,EAAA;EAAuB,QAAA,KAAK,EAAE6P,OAA9B;EAAuC,QAAA,QAAQ,EAAErG,YAAAA;SAAxD,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI3B,MAAM,KAAK4H,iBAAiB,CAACM,OAAjC,EAA0C;EACxC;QACA,oBAAO/P,gBAAA,CAAA,aAAA,CAAC,YAAD,CAAc,QAAd,EAAA;EAAuB,QAAA,KAAK,EAAE6P,OAA9B;EAAuC,QAAA,QAAQ,EAAE1G,QAAAA;SAAxD,CAAA,CAAA;EACD,KA7DM;;;EAgEP,IAAA,MAAM0G,OAAN,CAAA;EACD,GAAA;;EAnFD,CAAA;EAsFF;EACA;EACA;EACA;;;EACA,SAASW,YAAT,CAIG,KAAA,EAAA;IAAA,IAJmB;EACpBrH,IAAAA,QAAAA;KAGC,GAAA,KAAA,CAAA;IACD,IAAIwC,IAAI,GAAGY,aAAa,EAAxB,CAAA;EACA,EAAA,IAAIkE,QAAQ,GAAG,OAAOtH,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAACwC,IAAD,CAAzC,GAAkDxC,QAAjE,CAAA;IACA,oBAAOnJ,gBAAA,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA,EAAGyQ,QAAH,CAAP,CAAA;EACD;EAGD;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACO,SAASnB,wBAAT,CACLnG,QADK,EAEL7C,UAFK,EAGU;EAAA,EAAA,IADfA,UACe,KAAA,KAAA,CAAA,EAAA;EADfA,IAAAA,UACe,GADQ,EACR,CAAA;EAAA,GAAA;;IACf,IAAIT,MAAqB,GAAG,EAA5B,CAAA;IAEA7F,gBAAK,CAAC0Q,QAAN,CAAeC,OAAf,CAAuBxH,QAAvB,EAAiC,CAACnC,OAAD,EAAUmD,KAAV,KAAoB;EACnD,IAAA,IAAI,eAACnK,gBAAK,CAAC4Q,cAAN,CAAqB5J,OAArB,CAAL,EAAoC;EAClC;EACA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIA,OAAO,CAAC6J,IAAR,KAAiB7Q,gBAAK,CAAC8Q,QAA3B,EAAqC;EACnC;EACAjL,MAAAA,MAAM,CAACX,IAAP,CAAY6L,KAAZ,CACElL,MADF,EAEEyJ,wBAAwB,CAACtI,OAAO,CAAC2B,KAAR,CAAcQ,QAAf,EAAyB7C,UAAzB,CAF1B,CAAA,CAAA;EAIA,MAAA,OAAA;EACD,KAAA;;MAED,EACEU,OAAO,CAAC6J,IAAR,KAAiBjC,KADnB,CAAAlM,GAAAA,gBAAS,CAGL,KAAA,EAAA,GAAA,IAAA,OAAOsE,OAAO,CAAC6J,IAAf,KAAwB,QAAxB,GAAmC7J,OAAO,CAAC6J,IAA3C,GAAkD7J,OAAO,CAAC6J,IAAR,CAAaG,IAH1D,CAAA,GAAA,wGAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;MAOA,EACE,CAAChK,OAAO,CAAC2B,KAAR,CAAcwB,KAAf,IAAwB,CAACnD,OAAO,CAAC2B,KAAR,CAAcQ,QADzC,IAAAzG,gBAAS,CAAA,KAAA,EAEP,0CAFO,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAKA,IAAA,IAAIuO,QAAQ,GAAG,CAAC,GAAG3K,UAAJ,EAAgB6D,KAAhB,CAAf,CAAA;EACA,IAAA,IAAI9D,KAAkB,GAAG;EACvBqD,MAAAA,EAAE,EAAE1C,OAAO,CAAC2B,KAAR,CAAce,EAAd,IAAoBuH,QAAQ,CAACC,IAAT,CAAc,GAAd,CADD;EAEvBC,MAAAA,aAAa,EAAEnK,OAAO,CAAC2B,KAAR,CAAcwI,aAFN;EAGvBnK,MAAAA,OAAO,EAAEA,OAAO,CAAC2B,KAAR,CAAc3B,OAHA;EAIvBmD,MAAAA,KAAK,EAAEnD,OAAO,CAAC2B,KAAR,CAAcwB,KAJE;EAKvBrF,MAAAA,IAAI,EAAEkC,OAAO,CAAC2B,KAAR,CAAc7D,IALG;EAMvBsM,MAAAA,MAAM,EAAEpK,OAAO,CAAC2B,KAAR,CAAcyI,MANC;EAOvB5C,MAAAA,MAAM,EAAExH,OAAO,CAAC2B,KAAR,CAAc6F,MAPC;EAQvBhF,MAAAA,YAAY,EAAExC,OAAO,CAAC2B,KAAR,CAAca,YARL;EASvB6H,MAAAA,gBAAgB,EAAErK,OAAO,CAAC2B,KAAR,CAAca,YAAd,IAA8B,IATzB;EAUvB8H,MAAAA,gBAAgB,EAAEtK,OAAO,CAAC2B,KAAR,CAAc2I,gBAVT;EAWvB1F,MAAAA,MAAM,EAAE5E,OAAO,CAAC2B,KAAR,CAAciD,MAAAA;OAXxB,CAAA;;EAcA,IAAA,IAAI5E,OAAO,CAAC2B,KAAR,CAAcQ,QAAlB,EAA4B;EAC1B9C,MAAAA,KAAK,CAAC8C,QAAN,GAAiBmG,wBAAwB,CACvCtI,OAAO,CAAC2B,KAAR,CAAcQ,QADyB,EAEvC8H,QAFuC,CAAzC,CAAA;EAID,KAAA;;MAEDpL,MAAM,CAACX,IAAP,CAAYmB,KAAZ,CAAA,CAAA;KAlDF,CAAA,CAAA;EAqDA,EAAA,OAAOR,MAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAAS0L,aAAT,CACLnP,OADK,EAEsB;IAC3B,OAAO+E,cAAc,CAAC/E,OAAD,CAArB,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;;EACO,SAASoP,yBAAT,CACL3L,MADK,EAEU;EACf,EAAA,OAAOA,MAAM,CAAC1B,GAAP,CAAYkC,KAAD,IAAW;MAC3B,IAAIoL,UAAU,GAAQpL,QAAAA,CAAAA,EAAAA,EAAAA,KAAR,CAAd,CAAA;;EACA,IAAA,IAAIoL,UAAU,CAACJ,gBAAX,IAA+B,IAAnC,EAAyC;EACvCI,MAAAA,UAAU,CAACJ,gBAAX,GAA8BI,UAAU,CAACjI,YAAX,IAA2B,IAAzD,CAAA;EACD,KAAA;;MACD,IAAIiI,UAAU,CAACtI,QAAf,EAAyB;QACvBsI,UAAU,CAACtI,QAAX,GAAsBqI,yBAAyB,CAACC,UAAU,CAACtI,QAAZ,CAA/C,CAAA;EACD,KAAA;;EACD,IAAA,OAAOsI,UAAP,CAAA;EACD,GATM,CAAP,CAAA;EAUD;;EC/aM,SAASC,kBAAT,CACL7L,MADK,EAEL+H,IAFK,EAQQ;EACb,EAAA,OAAO+D,mBAAY,CAAC;EAClBhP,IAAAA,QAAQ,EAAEiL,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEjL,QADE;MAElB2L,OAAO,EAAEF,0BAAmB,CAAC;EAC3BH,MAAAA,cAAc,EAAEL,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEK,cADK;EAE3BC,MAAAA,YAAY,EAAEN,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEM,YAAAA;EAFO,KAAD,CAFV;EAMlB0D,IAAAA,aAAa,EAAEhE,IAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEgE,aANH;MAOlB/L,MAAM,EAAE2L,yBAAyB,CAAC3L,MAAD,CAAA;KAPhB,CAAZ,CAQJgM,UARI,EAAP,CAAA;EASD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}