{"version": 3, "file": "react-router.development.js", "sources": ["../lib/use-sync-external-store-shim/useSyncExternalStoreShimClient.ts", "../lib/use-sync-external-store-shim/useSyncExternalStoreShimServer.ts", "../lib/use-sync-external-store-shim/index.ts", "../lib/context.ts", "../lib/hooks.tsx", "../lib/components.tsx", "../index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as React from \"react\";\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction isPolyfill(x: any, y: any) {\n  return (\n    (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y) // eslint-disable-line no-self-compare\n  );\n}\n\nconst is: (x: any, y: any) => boolean =\n  typeof Object.is === \"function\" ? Object.is : isPolyfill;\n\n// Intentionally not using named imports because Roll<PERSON> uses dynamic\n// dispatch for CommonJS interop named imports.\nconst { useState, useEffect, useLayoutEffect, useDebugValue } = React;\n\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false;\n\n// Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  getServerSnapshot?: () => T\n): T {\n  if (__DEV__) {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that \" +\n            \"does not support useSyncExternalStore. The \" +\n            \"use-sync-external-store shim will not work correctly. Upgrade \" +\n            \"to a newer pre-release.\"\n        );\n      }\n    }\n  }\n\n  // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n  const value = getSnapshot();\n  if (__DEV__) {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n      if (!is(value, cachedValue)) {\n        console.error(\n          \"The result of getSnapshot should be cached to avoid an infinite loop\"\n        );\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  }\n\n  // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n  const [{ inst }, forceUpdate] = useState({ inst: { value, getSnapshot } });\n\n  // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot;\n\n    // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe, value, getSnapshot]);\n\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({ inst });\n      }\n    };\n    // Subscribe to the store and return a clean-up function.\n    return subscribe(handleStoreChange);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst: any) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  getServerSnapshot?: () => T\n): T {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n", "/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport * as React from \"react\";\n\nimport { useSyncExternalStore as client } from \"./useSyncExternalStoreShimClient\";\nimport { useSyncExternalStore as server } from \"./useSyncExternalStoreShimServer\";\n\nconst canUseDOM: boolean = !!(\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\"\n);\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? server : client;\n\nexport const useSyncExternalStore =\n  \"useSyncExternalStore\" in React\n    ? ((module) => module.useSyncExternalStore)(React)\n    : shim;\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  History,\n  Location,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport type { Action as NavigationType } from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject extends NavigationContextObject {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  Router as RemixRouter,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  warning,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n} from \"@remix-run/router\";\n\nimport type {\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n  DataRouteMatch,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n  AwaitContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history.  If this is a root navigation, then we\n      // navigate to the raw basename which allows the basename to have full\n      // control over the presence of a trailing slash on root links\n      if (basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterStateContext || undefined\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own&nbsp;\n          <code style={codeStyles}>errorElement</code> props on&nbsp;\n          <code style={codeStyles}>&lt;Route&gt;</code>\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    match.route.errorElement\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState?: RemixRouter[\"state\"]\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for the current errors: ${errors}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors?.[match.route.id] : null;\n    // Only data routers handle errors\n    let errorElement = dataRouterState\n      ? match.route.errorElement || <DefaultErrorElement />\n      : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => (\n      <RenderedRoute match={match} routeContext={{ outlet, matches }}>\n        {error\n          ? errorElement\n          : match.route.element !== undefined\n          ? match.route.element\n          : outlet}\n      </RenderedRoute>\n    );\n    // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n    return dataRouterState && (match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nenum DataRouterStateHook {\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation,\n  };\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () =>\n      matches.map((match) => {\n        let { pathname, params } = match;\n        // Note: This structure matches that created by createUseMatchesMatch\n        // in the @remix-run/router , so if you change this please also change\n        // that :)  Eventually we'll DRY this up\n        return {\n          id: match.route.id,\n          pathname,\n          params,\n          data: loaderData[match.route.id] as unknown,\n          handle: match.route.handle as unknown,\n        };\n      }),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useActionData must be used inside a RouteContext`);\n\n  return Object.values(state?.actionData || {})[0];\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (args) => {\n      return typeof shouldBlock === \"function\"\n        ? !!shouldBlock(args)\n        : !!shouldBlock;\n    },\n    [shouldBlock]\n  );\n\n  let blocker = router.getBlocker(blockerKey, blockerFunction);\n\n  // Cleanup on unmount\n  React.useEffect(\n    () => () => router.deleteBlocker(blockerKey),\n    [router, blockerKey]\n  );\n\n  return blocker;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import * as React from \"react\";\nimport type {\n  TrackedPromise,\n  InitialEntry,\n  Location,\n  MemoryHistory,\n  Router as RemixRouter,\n  RouterState,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  AbortedDeferredError,\n  createMemoryHistory,\n  invariant,\n  parsePath,\n  stripBasename,\n  warning,\n} from \"@remix-run/router\";\nimport { useSyncExternalStore as useSyncExternalStoreShim } from \"./use-sync-external-store-shim\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  LocationContext,\n  NavigationContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  AwaitContext,\n} from \"./context\";\nimport {\n  useAsyncValue,\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n}: RouterProviderProps): React.ReactElement {\n  // Sync router state to our component state to force re-renders\n  let state: RouterState = useSyncExternalStoreShim(\n    router.subscribe,\n    () => router.state,\n    // We have to provide this so React@18 doesn't complain during hydration,\n    // but we pass our serialized hydration data into the router so state here\n    // is already synced with what the server saw\n    () => router.state\n  );\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider\n        value={{\n          router,\n          navigator,\n          static: false,\n          // Do we need this?\n          basename,\n        }}\n      >\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={router.basename}\n            location={router.state.location}\n            navigationType={router.state.historyAction}\n            navigator={navigator}\n          >\n            {router.state.initialized ? <Routes /> : fallbackElement}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n    navigate(to, { replace, state, relative });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  let dataRouterContext = React.useContext(DataRouterContext);\n  // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n  let routes =\n    dataRouterContext && !children\n      ? (dataRouterContext.router.routes as DataRouteObject[])\n      : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, parentPath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let treePath = [...parentPath, index];\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\nexport function enhanceManualRouteObjects(\n  routes: RouteObject[]\n): RouteObject[] {\n  return routes.map((route) => {\n    let routeClone = { ...route };\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n    return routeClone;\n  });\n}\n", "import type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  Fetcher,\n  HydrationState,\n  JsonFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  PathPattern,\n  RedirectFunction,\n  Router as RemixRouter,\n  ShouldRevalidateFunction,\n  To,\n  InitialEntry,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  resolvePath,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  RoutesProps,\n  RouterProviderProps,\n} from \"./lib/components\";\nimport {\n  enhanceManualRouteObjects,\n  createRoutesFromChildren,\n  renderMatches,\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NavigateOptions,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  RelativeRoutingType,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigationType,\n  useNavigate,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useLoaderData,\n  useMatches,\n  useNavigation,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker as unstable_Blocker,\n  BlockerFunction as unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker as unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  NavigationContext as UNSAFE_NavigationContext,\n  LocationContext as UNSAFE_LocationContext,\n  RouteContext as UNSAFE_RouteContext,\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects,\n};\n"], "names": ["isPolyfill", "x", "y", "is", "Object", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "React", "didWarnOld18Alpha", "didWarnUncachedGetSnapshot", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "console", "error", "value", "cachedValue", "inst", "forceUpdate", "checkIfSnapshotChanged", "handleStoreChange", "latestGetSnapshot", "prevValue", "nextValue", "canUseDOM", "window", "document", "createElement", "isServerEnvironment", "shim", "server", "client", "module", "DataRouterContext", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "RouteErrorContext", "useHref", "to", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "useNavigate", "locationPathname", "routePathnamesJson", "JSON", "stringify", "getPathContributingMatches", "map", "match", "pathnameBase", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "Provider", "useParams", "routeMatch", "length", "params", "useRoutes", "routes", "locationArg", "dataRouterStateContext", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "slice", "matchRoutes", "element", "undefined", "renderedMatches", "_renderMatches", "assign", "encodeLocation", "key", "NavigationType", "Pop", "DefaultErrorElement", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "Fragment", "style", "fontStyle", "RenderErrorBoundary", "Component", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "dataRouterContext", "static", "staticContext", "errorElement", "_deepestRenderedBoundaryId", "id", "dataRouterState", "errors", "errorIndex", "findIndex", "m", "Math", "min", "reduceRight", "index", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "revalidation", "useMatches", "loaderData", "UseMatches", "data", "handle", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "values", "actionData", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "String", "blockerFunction", "args", "blocker", "get<PERSON><PERSON>er", "deleteBlocker", "alreadyWarned", "cond", "RouterProvider", "fallbackElement", "useSyncExternalStoreShim", "n", "opts", "preventScrollReset", "Router", "historyAction", "initialized", "Routes", "MemoryRouter", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "setState", "action", "listen", "Navigate", "Outlet", "Route", "_props", "basenameProp", "locationProp", "staticProp", "navigationContext", "trailingPathname", "stripBasename", "createRoutesFromChildren", "Await", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "get", "renderError", "reject", "catch", "_tracked", "then", "Aborted<PERSON>eferredError", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "type", "apply", "name", "treePath", "join", "caseSensitive", "loader", "hasErrorBou<PERSON>ry", "shouldRevalidate", "renderMatches", "enhanceManualRouteObjects", "routeClone", "createMemoryRouter", "createRouter", "hydrationData", "initialize"], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;AAKG;AAIH;;;AAGG;;AACH,SAASA,UAAT,CAAoBC,CAApB,EAA4BC,CAA5B,EAAkC;EAChC,OACGD,CAAC,KAAKC,CAAN,KAAYD,CAAC,KAAK,CAAN,IAAW,CAAA,GAAIA,CAAJ,KAAU,IAAIC,CAArC,CAAD,IAA8CD,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CADjE;AAAA,GAAA;AAGD,CAAA;;AAED,MAAMC,EAAE,GACN,OAAOC,MAAM,CAACD,EAAd,KAAqB,UAArB,GAAkCC,MAAM,CAACD,EAAzC,GAA8CH,UADhD;AAIA;;AACA,MAAM;EAAEK,QAAF;EAAYC,SAAZ;EAAuBC,eAAvB;AAAwCC,EAAAA,aAAAA;AAAxC,CAAA,GAA0DC,KAAhE,CAAA;AAEA,IAAIC,iBAAiB,GAAG,KAAxB,CAAA;AACA,IAAIC,0BAA0B,GAAG,KAAjC;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACgB,SAAAC,sBAAA,CACdC,SADc,EAEdC,WAFc;AAId;AACA;AACA;AACAC,iBAPc,EAOa;EAEd;IACX,IAAI,CAACL,iBAAL,EAAwB;MACtB,IAAI,iBAAA,IAAqBD,KAAzB,EAAgC;AAC9BC,QAAAA,iBAAiB,GAAG,IAApB,CAAA;QACAM,OAAO,CAACC,KAAR,CACE,gEAAA,GACE,6CADF,GAEE,gEAFF,GAGE,yBAJJ,CAAA,CAAA;AAMD,OAAA;AACF,KAAA;AACF,GAd0B;AAiB3B;AACA;AACA;;;EACA,MAAMC,KAAK,GAAGJ,WAAW,EAAzB,CAAA;;EACa;IACX,IAAI,CAACH,0BAAL,EAAiC;MAC/B,MAAMQ,WAAW,GAAGL,WAAW,EAA/B,CAAA;;AACA,MAAA,IAAI,CAACX,EAAE,CAACe,KAAD,EAAQC,WAAR,CAAP,EAA6B;QAC3BH,OAAO,CAACC,KAAR,CACE,sEADF,CAAA,CAAA;AAGAN,QAAAA,0BAA0B,GAAG,IAA7B,CAAA;AACD,OAAA;AACF,KAAA;AACF,GA/B0B;AAkC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,EAAA,MAAM,CAAC;AAAES,IAAAA,IAAAA;AAAF,GAAD,EAAWC,WAAX,CAA0BhB,GAAAA,QAAQ,CAAC;AAAEe,IAAAA,IAAI,EAAE;MAAEF,KAAF;AAASJ,MAAAA,WAAAA;AAAT,KAAA;GAAT,CAAxC,CA/C2B;AAkD3B;AACA;;AACAP,EAAAA,eAAe,CAAC,MAAK;IACnBa,IAAI,CAACF,KAAL,GAAaA,KAAb,CAAA;AACAE,IAAAA,IAAI,CAACN,WAAL,GAAmBA,WAAnB,CAFmB;AAKnB;AACA;AACA;;AACA,IAAA,IAAIQ,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;AAChC;AACAC,MAAAA,WAAW,CAAC;AAAED,QAAAA,IAAAA;AAAF,OAAD,CAAX,CAAA;AACD,KAXkB;;GAAN,EAaZ,CAACP,SAAD,EAAYK,KAAZ,EAAmBJ,WAAnB,CAbY,CAAf,CAAA;AAeAR,EAAAA,SAAS,CAAC,MAAK;AACb;AACA;AACA,IAAA,IAAIgB,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;AAChC;AACAC,MAAAA,WAAW,CAAC;AAAED,QAAAA,IAAAA;AAAF,OAAD,CAAX,CAAA;AACD,KAAA;;IACD,MAAMG,iBAAiB,GAAG,MAAK;AAC7B;AACA;AACA;AACA;AAEA;AACA;AACA,MAAA,IAAID,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;AAChC;AACAC,QAAAA,WAAW,CAAC;AAAED,UAAAA,IAAAA;AAAF,SAAD,CAAX,CAAA;AACD,OAAA;AACF,KAZD,CAPa;;;AAqBb,IAAA,OAAOP,SAAS,CAACU,iBAAD,CAAhB,CArBa;AAuBd,GAvBQ,EAuBN,CAACV,SAAD,CAvBM,CAAT,CAAA;EAyBAL,aAAa,CAACU,KAAD,CAAb,CAAA;AACA,EAAA,OAAOA,KAAP,CAAA;AACD,CAAA;;AAED,SAASI,sBAAT,CAAgCF,IAAhC,EAAyC;AACvC,EAAA,MAAMI,iBAAiB,GAAGJ,IAAI,CAACN,WAA/B,CAAA;AACA,EAAA,MAAMW,SAAS,GAAGL,IAAI,CAACF,KAAvB,CAAA;;EACA,IAAI;IACF,MAAMQ,SAAS,GAAGF,iBAAiB,EAAnC,CAAA;AACA,IAAA,OAAO,CAACrB,EAAE,CAACsB,SAAD,EAAYC,SAAZ,CAAV,CAAA;GAFF,CAGE,OAAOT,KAAP,EAAc;AACd,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;AACF;;ACvJD;;;;;;;AAOG;SAEaL,uBACdC,WACAC,aACAC,mBAA2B;AAE3B;AACA;AACA;AACA;AACA,EAAA,OAAOD,WAAW,EAAlB,CAAA;AACD;;ACnBD;;;;AAIG;AAgBH,MAAMa,SAAS,GAAY,CAAC,EAC1B,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHf,CAA5B,CAAA;AAKA,MAAMC,mBAAmB,GAAG,CAACJ,SAA7B,CAAA;AACA,MAAMK,IAAI,GAAGD,mBAAmB,GAAGE,sBAAH,GAAYC,sBAA5C,CAAA;AAEO,MAAMtB,oBAAoB,GAC/B,sBAA0BH,IAAAA,KAA1B,GACI,CAAE0B,MAAD,IAAYA,MAAM,CAACvB,oBAApB,EAA0CH,KAA1C,CADJ,GAEIuB,IAHC;;ACqCA,MAAMI,iBAAiB,gBAC5B3B,KAAK,CAAC4B,aAAN,CAAoD,IAApD,EADK;;AAEM;EACXD,iBAAiB,CAACE,WAAlB,GAAgC,YAAhC,CAAA;AACD,CAAA;;AAEM,MAAMC,sBAAsB,gBAAG9B,KAAK,CAAC4B,aAAN,CAEpC,IAFoC,EAA/B;;AAGM;EACXE,sBAAsB,CAACD,WAAvB,GAAqC,iBAArC,CAAA;AACD,CAAA;;AAEM,MAAME,YAAY,gBAAG/B,KAAK,CAAC4B,aAAN,CAA2C,IAA3C,CAArB,CAAA;;AACM;EACXG,YAAY,CAACF,WAAb,GAA2B,OAA3B,CAAA;AACD,CAAA;;AAmCM,MAAMG,iBAAiB,gBAAGhC,KAAK,CAAC4B,aAAN,CAC/B,IAD+B,EAA1B;;AAIM;EACXI,iBAAiB,CAACH,WAAlB,GAAgC,YAAhC,CAAA;AACD,CAAA;;AAOM,MAAMI,eAAe,gBAAGjC,KAAK,CAAC4B,aAAN,CAC7B,IAD6B,EAAxB;;AAIM;EACXK,eAAe,CAACJ,WAAhB,GAA8B,UAA9B,CAAA;AACD,CAAA;;MAOYK,YAAY,gBAAGlC,KAAK,CAAC4B,aAAN,CAAwC;AAClEO,EAAAA,MAAM,EAAE,IAD0D;AAElEC,EAAAA,OAAO,EAAE,EAAA;AAFyD,CAAxC,EAArB;;AAKM;EACXF,YAAY,CAACL,WAAb,GAA2B,OAA3B,CAAA;AACD,CAAA;;AAEM,MAAMQ,iBAAiB,gBAAGrC,KAAK,CAAC4B,aAAN,CAAyB,IAAzB,CAA1B,CAAA;;AAEM;EACXS,iBAAiB,CAACR,WAAlB,GAAgC,YAAhC,CAAA;AACD;;AC/GD;;;;;AAKG;;AACG,SAAUS,OAAV,CACJC,EADI,EAEJ;AAAEC,EAAAA,QAAAA;AAAF,CAAA,GAAmD,EAF/C,EAEiD;AAErD,EAAA,CACEC,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,kEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,IAAI;IAAEC,QAAF;AAAYC,IAAAA,SAAAA;AAAZ,GAAA,GAA0B5C,KAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAA9B,CAAA;EACA,IAAI;IAAEc,IAAF;IAAQC,QAAR;AAAkBC,IAAAA,MAAAA;GAAWC,GAAAA,eAAe,CAACV,EAAD,EAAK;AAAEC,IAAAA,QAAAA;AAAF,GAAL,CAAhD,CAAA;AAEA,EAAA,IAAIU,cAAc,GAAGH,QAArB,CAZqD;AAerD;AACA;AACA;;EACA,IAAIJ,QAAQ,KAAK,GAAjB,EAAsB;AACpBO,IAAAA,cAAc,GACZH,QAAQ,KAAK,GAAb,GAAmBJ,QAAnB,GAA8BQ,SAAS,CAAC,CAACR,QAAD,EAAWI,QAAX,CAAD,CADzC,CAAA;AAED,GAAA;;EAED,OAAOH,SAAS,CAACQ,UAAV,CAAqB;AAAEL,IAAAA,QAAQ,EAAEG,cAAZ;IAA4BF,MAA5B;AAAoCF,IAAAA,IAAAA;AAApC,GAArB,CAAP,CAAA;AACD,CAAA;AAED;;;;AAIG;;SACaL,qBAAkB;AAChC,EAAA,OAAOzC,KAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,KAAqC,IAA5C,CAAA;AACD,CAAA;AAED;;;;;;;;;AASG;;SACaoB,cAAW;AACzB,EAAA,CACEZ,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,sEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AAOA,EAAA,OAAO1C,KAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,EAAkCqB,QAAzC,CAAA;AACD,CAAA;AAED;;;;;AAKG;;SACaC,oBAAiB;AAC/B,EAAA,OAAOvD,KAAK,CAAC6C,UAAN,CAAiBZ,eAAjB,EAAkCuB,cAAzC,CAAA;AACD,CAAA;AAED;;;;;;AAMG;;AACG,SAAUC,QAAV,CAGJC,OAHI,EAG6B;AACjC,EAAA,CACEjB,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,mEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,IAAI;AAAEK,IAAAA,QAAAA;AAAF,GAAA,GAAeM,WAAW,EAA9B,CAAA;AACA,EAAA,OAAOrD,KAAK,CAAC2D,OAAN,CACL,MAAMC,SAAS,CAAiBF,OAAjB,EAA0BX,QAA1B,CADV,EAEL,CAACA,QAAD,EAAWW,OAAX,CAFK,CAAP,CAAA;AAID,CAAA;AAUD;;;;;AAKG;;SACaG,cAAW;AACzB,EAAA,CACEpB,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,sEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,IAAI;IAAEC,QAAF;AAAYC,IAAAA,SAAAA;AAAZ,GAAA,GAA0B5C,KAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAA9B,CAAA;EACA,IAAI;AAAEI,IAAAA,OAAAA;AAAF,GAAA,GAAcpC,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;EACA,IAAI;AAAEa,IAAAA,QAAQ,EAAEe,gBAAAA;AAAZ,GAAA,GAAiCT,WAAW,EAAhD,CAAA;AAEA,EAAA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,iCAA0B,CAAC9B,OAAD,CAA1B,CAAoC+B,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB,CAAA;AAIA,EAAA,IAAIC,SAAS,GAAGtE,KAAK,CAACuE,MAAN,CAAa,KAAb,CAAhB,CAAA;EACAvE,KAAK,CAACH,SAAN,CAAgB,MAAK;IACnByE,SAAS,CAACE,OAAV,GAAoB,IAApB,CAAA;GADF,CAAA,CAAA;AAIA,EAAA,IAAIC,QAAQ,GAAqBzE,KAAK,CAAC0E,WAAN,CAC/B,CAACnC,EAAD,EAAkBoC,OAAA,GAA2B,EAA7C,KAAmD;IACjDC,OAAO,CACLN,SAAS,CAACE,OADL,EAEyD,CAAA,4DAAA,CAA9D,GACE,CAAA,iCAAA,CAHG,CAAP,CAAA,CAAA;AAMA,IAAA,IAAI,CAACF,SAAS,CAACE,OAAf,EAAwB,OAAA;;AAExB,IAAA,IAAI,OAAOjC,EAAP,KAAc,QAAlB,EAA4B;MAC1BK,SAAS,CAACiC,EAAV,CAAatC,EAAb,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED,IAAIuC,IAAI,GAAGC,SAAS,CAClBxC,EADkB,EAElByB,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFkB,EAGlBD,gBAHkB,EAIlBa,OAAO,CAACnC,QAAR,KAAqB,MAJH,CAApB,CAdiD;AAsBjD;AACA;AACA;;IACA,IAAIG,QAAQ,KAAK,GAAjB,EAAsB;MACpBmC,IAAI,CAAC/B,QAAL,GACE+B,IAAI,CAAC/B,QAAL,KAAkB,GAAlB,GACIJ,QADJ,GAEIQ,SAAS,CAAC,CAACR,QAAD,EAAWmC,IAAI,CAAC/B,QAAhB,CAAD,CAHf,CAAA;AAID,KAAA;;IAED,CAAC,CAAC,CAAC4B,OAAO,CAACM,OAAV,GAAoBrC,SAAS,CAACqC,OAA9B,GAAwCrC,SAAS,CAACsC,IAAnD,EACEJ,IADF,EAEEH,OAAO,CAACQ,KAFV,EAGER,OAHF,CAAA,CAAA;GAjC6B,EAuC/B,CAAChC,QAAD,EAAWC,SAAX,EAAsBmB,kBAAtB,EAA0CD,gBAA1C,CAvC+B,CAAjC,CAAA;AA0CA,EAAA,OAAOW,QAAP,CAAA;AACD,CAAA;AAED,MAAMW,aAAa,gBAAGpF,KAAK,CAAC4B,aAAN,CAA6B,IAA7B,CAAtB,CAAA;AAEA;;;;AAIG;;SACayD,mBAAgB;AAC9B,EAAA,OAAOrF,KAAK,CAAC6C,UAAN,CAAiBuC,aAAjB,CAAP,CAAA;AACD,CAAA;AAED;;;;;AAKG;;AACG,SAAUE,SAAV,CAAoBC,OAApB,EAAqC;EACzC,IAAIpD,MAAM,GAAGnC,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,EAA+BC,MAA5C,CAAA;;AACA,EAAA,IAAIA,MAAJ,EAAY;AACV,IAAA,oBACEnC,KAAA,CAAAqB,aAAA,CAAC+D,aAAa,CAACI,QAAf,EAAuB;AAAC/E,MAAAA,KAAK,EAAE8E,OAAAA;KAA/B,EAAyCpD,MAAzC,CADF,CAAA;AAGD,GAAA;;AACD,EAAA,OAAOA,MAAP,CAAA;AACD,CAAA;AAED;;;;;AAKG;;SACasD,YAAS;EAKvB,IAAI;AAAErD,IAAAA,OAAAA;AAAF,GAAA,GAAcpC,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;EACA,IAAIwD,UAAU,GAAGtD,OAAO,CAACA,OAAO,CAACuD,MAAR,GAAiB,CAAlB,CAAxB,CAAA;AACA,EAAA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAf,GAAgC,EAAjD,CAAA;AACD,CAAA;AAED;;;;AAIG;;AACG,SAAU3C,eAAV,CACJV,EADI,EAEJ;AAAEC,EAAAA,QAAAA;AAAF,CAAA,GAAmD,EAF/C,EAEiD;EAErD,IAAI;AAAEJ,IAAAA,OAAAA;AAAF,GAAA,GAAcpC,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAlB,CAAA;EACA,IAAI;AAAEa,IAAAA,QAAQ,EAAEe,gBAAAA;AAAZ,GAAA,GAAiCT,WAAW,EAAhD,CAAA;AAEA,EAAA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,iCAA0B,CAAC9B,OAAD,CAA1B,CAAoC+B,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB,CAAA;AAIA,EAAA,OAAOrE,KAAK,CAAC2D,OAAN,CACL,MACEoB,SAAS,CACPxC,EADO,EAEPyB,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFO,EAGPD,gBAHO,EAIPtB,QAAQ,KAAK,MAJN,CAFN,EAQL,CAACD,EAAD,EAAKwB,kBAAL,EAAyBD,gBAAzB,EAA2CtB,QAA3C,CARK,CAAP,CAAA;AAUD,CAAA;AAED;;;;;;;AAOG;;AACa,SAAAqD,SAAA,CACdC,MADc,EAEdC,WAFc,EAE0B;AAExC,EAAA,CACEtD,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,oEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAOA,IAAI;AAAEE,IAAAA,SAAAA;AAAF,GAAA,GAAgB5C,KAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAApB,CAAA;AACA,EAAA,IAAIgE,sBAAsB,GAAGhG,KAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAA7B,CAAA;EACA,IAAI;AAAEM,IAAAA,OAAO,EAAE6D,aAAAA;AAAX,GAAA,GAA6BjG,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAjC,CAAA;EACA,IAAIwD,UAAU,GAAGO,aAAa,CAACA,aAAa,CAACN,MAAd,GAAuB,CAAxB,CAA9B,CAAA;EACA,IAAIO,YAAY,GAAGR,UAAU,GAAGA,UAAU,CAACE,MAAd,GAAuB,EAApD,CAAA;EACA,IAAIO,cAAc,GAAGT,UAAU,GAAGA,UAAU,CAAC3C,QAAd,GAAyB,GAAxD,CAAA;EACA,IAAIqD,kBAAkB,GAAGV,UAAU,GAAGA,UAAU,CAACrB,YAAd,GAA6B,GAAhE,CAAA;AACA,EAAA,IAAIgC,WAAW,GAAGX,UAAU,IAAIA,UAAU,CAACY,KAA3C,CAAA;;EAEa;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,IAAIC,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAACvB,IAA5B,IAAqC,EAAtD,CAAA;AACA0B,IAAAA,WAAW,CACTL,cADS,EAET,CAACE,WAAD,IAAgBE,UAAU,CAACE,QAAX,CAAoB,GAApB,CAFP,EAGyD,CAAlE,gEAAA,CAAA,GACM,IAAAN,cAAuC,CAAA,sBAAA,EAAAI,UAAwB,CADrE,YAAA,CAAA,GAEsE,CAFtE,kEAAA,CAAA,GAGmE,iEAHnE,GAIiC,CAAA,6BAAA,CAJjC,GAKE,CAAyCA,sCAAAA,EAAAA,UAAyB,eALpE,GAME,CAAA,MAAA,EAASA,UAAU,KAAK,GAAf,GAAqB,GAArB,GAA8B,GAAAA,UAAc,CAAA,EAAA,CAAA,KAT9C,CAAX,CAAA;AAWD,GAAA;;EAED,IAAIG,mBAAmB,GAAGrD,WAAW,EAArC,CAAA;AAEA,EAAA,IAAIC,QAAJ,CAAA;;AACA,EAAA,IAAIyC,WAAJ,EAAiB;AACf,IAAA,IAAIY,iBAAiB,GACnB,OAAOZ,WAAP,KAAuB,QAAvB,GAAkCa,SAAS,CAACb,WAAD,CAA3C,GAA2DA,WAD7D,CAAA;AAGA,IAAA,EACEK,kBAAkB,KAAK,GAAvB,IACEO,iBAAiB,CAAC5D,QAAlB,EAA4B8D,UAA5B,CAAuCT,kBAAvC,CAFJ,IAAA1D,SAAS,CAAA,KAAA,EAGwF,CAA/F,6FAAA,CAAA,GACmF,CADnF,+EAAA,CAAA,GAEE,CAA+D0D,4DAAAA,EAAAA,kBAAsB,IAFvF,GAGE,CAAA,cAAA,EAAiBO,iBAAiB,CAAC5D,QAAQ,CAAA,qCAAA,CANtC,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AASAO,IAAAA,QAAQ,GAAGqD,iBAAX,CAAA;AACD,GAdD,MAcO;AACLrD,IAAAA,QAAQ,GAAGoD,mBAAX,CAAA;AACD,GAAA;;AAED,EAAA,IAAI3D,QAAQ,GAAGO,QAAQ,CAACP,QAAT,IAAqB,GAApC,CAAA;AACA,EAAA,IAAI+D,iBAAiB,GACnBV,kBAAkB,KAAK,GAAvB,GACIrD,QADJ,GAEIA,QAAQ,CAACgE,KAAT,CAAeX,kBAAkB,CAACT,MAAlC,KAA6C,GAHnD,CAAA;AAKA,EAAA,IAAIvD,OAAO,GAAG4E,WAAW,CAAClB,MAAD,EAAS;AAAE/C,IAAAA,QAAQ,EAAE+D,iBAAAA;AAAZ,GAAT,CAAzB,CAAA;;EAEa;IACXlC,OAAO,CACLyB,WAAW,IAAIjE,OAAO,IAAI,IADrB,GAE0BkB,4BAAAA,EAAAA,QAAQ,CAACP,QAAW,CAAA,EAAAO,QAAQ,CAACN,MAAM,GAAGM,QAAQ,CAACR,IAAQ,CAAA,EAAA,CAFjF,CAAP,CAAA,CAAA;AAKA,IAAA8B,OAAO,CACLxC,OAAO,IAAI,IAAX,IACEA,OAAO,CAACA,OAAO,CAACuD,MAAR,GAAiB,CAAlB,CAAP,CAA4BW,KAA5B,CAAkCW,OAAlC,KAA8CC,SAF3C,EAG8B,CAAA5D,gCAAAA,EAAAA,QAAQ,CAACP,QAAW,GAAAO,QAAQ,CAACN,MAAS,CAAA,EAAAM,QAAQ,CAACR,IAAkC,CAApH,4BAAA,CAAA,GACE,oGAJG,CAAP,CAAA,CAAA;AAMD,GAAA;;AAED,EAAA,IAAIqE,eAAe,GAAGC,cAAc,CAClChF,OAAO,IACLA,OAAO,CAAC+B,GAAR,CAAaC,KAAD,IACVzE,MAAM,CAAC0H,MAAP,CAAc,EAAd,EAAkBjD,KAAlB,EAAyB;AACvBwB,IAAAA,MAAM,EAAEjG,MAAM,CAAC0H,MAAP,CAAc,EAAd,EAAkBnB,YAAlB,EAAgC9B,KAAK,CAACwB,MAAtC,CADe;AAEvB7C,IAAAA,QAAQ,EAAEI,SAAS,CAAC,CAClBiD,kBADkB;AAGlBxD,IAAAA,SAAS,CAAC0E,cAAV,GACI1E,SAAS,CAAC0E,cAAV,CAAyBlD,KAAK,CAACrB,QAA/B,EAAyCA,QAD7C,GAEIqB,KAAK,CAACrB,QALQ,CAAD,CAFI;AASvBsB,IAAAA,YAAY,EACVD,KAAK,CAACC,YAAN,KAAuB,GAAvB,GACI+B,kBADJ,GAEIjD,SAAS,CAAC,CACRiD,kBADQ;AAGRxD,IAAAA,SAAS,CAAC0E,cAAV,GACI1E,SAAS,CAAC0E,cAAV,CAAyBlD,KAAK,CAACC,YAA/B,EAA6CtB,QADjD,GAEIqB,KAAK,CAACC,YALF,CAAD,CAAA;GAZjB,CADF,CAFgC,EAwBlC4B,aAxBkC,EAyBlCD,sBAAsB,IAAIkB,SAzBQ,CAApC,CAhGwC;AA6HxC;AACA;;;EACA,IAAInB,WAAW,IAAIoB,eAAnB,EAAoC;AAClC,IAAA,oBACEnH,KAAC,CAAAqB,aAAD,CAACY,eAAe,CAACuD,QAAjB,EAAyB;AACvB/E,MAAAA,KAAK,EAAE;AACL6C,QAAAA,QAAQ,EAAE;AACRP,UAAAA,QAAQ,EAAE,GADF;AAERC,UAAAA,MAAM,EAAE,EAFA;AAGRF,UAAAA,IAAI,EAAE,EAHE;AAIRqC,UAAAA,KAAK,EAAE,IAJC;AAKRoC,UAAAA,GAAG,EAAE,SALG;UAMR,GAAGjE,QAAAA;SAPA;QASLE,cAAc,EAAEgE,MAAc,CAACC,GAAAA;AAT1B,OAAA;KADT,EAaGN,eAbH,CADF,CAAA;AAiBD,GAAA;;AAED,EAAA,OAAOA,eAAP,CAAA;AACD,CAAA;;AAED,SAASO,mBAAT,GAA4B;EAC1B,IAAIlH,KAAK,GAAGmH,aAAa,EAAzB,CAAA;AACA,EAAA,IAAIC,OAAO,GAAGC,oBAAoB,CAACrH,KAAD,CAApB,MACPA,KAAK,CAACsH,MAAU,CAAA,CAAA,EAAAtH,KAAK,CAACuH,UAAY,CAD3B,CAAA,GAEVvH,KAAK,YAAYwH,KAAjB,GACAxH,KAAK,CAACoH,OADN,GAEA5D,IAAI,CAACC,SAAL,CAAezD,KAAf,CAJJ,CAAA;EAKA,IAAIyH,KAAK,GAAGzH,KAAK,YAAYwH,KAAjB,GAAyBxH,KAAK,CAACyH,KAA/B,GAAuC,IAAnD,CAAA;EACA,IAAIC,SAAS,GAAG,wBAAhB,CAAA;AACA,EAAA,IAAIC,SAAS,GAAG;AAAEC,IAAAA,OAAO,EAAE,QAAX;AAAqBC,IAAAA,eAAe,EAAEH,SAAAA;GAAtD,CAAA;AACA,EAAA,IAAII,UAAU,GAAG;AAAEF,IAAAA,OAAO,EAAE,SAAX;AAAsBC,IAAAA,eAAe,EAAEH,SAAAA;GAAxD,CAAA;EAEA,IAAIK,OAAO,GAAG,IAAd,CAAA;;EACa;AACXA,IAAAA,OAAO,gBACLvI,KAAA,CAAAqB,aAAA,CAAArB,KAAA,CAAAwI,QAAA,EAAA,IAAA,eACExI,KAA0B,CAAAqB,aAA1B,CAA0B,GAA1B,EAA0B,IAA1B,EAA0B,yCAA1B,CADF,eAEErB,KAAA,CAAAqB,aAAA,CAAA,GAAA,EAAA,IAAA,qGAAA,eAGErB,KAAM,CAAAqB,aAAN,CAAM,MAAN,EAAM;AAAAoH,MAAAA,KAAK,EAAEH,UAAAA;KAAb,EAA4C,cAA5C,CAHF,mBAAA,eAIEtI,KAAA,CAAAqB,aAAA,CAAA,MAAA,EAAA;AAAMoH,MAAAA,KAAK,EAAEH,UAAAA;AAAb,KAAA,WAAA,CAJF,CAFF,CADF,CAAA;AAWD,GAAA;;EAED,oBACEtI,KAAA,CAAAqB,aAAA,CAAArB,KAAA,CAAAwI,QAAA,EAAA,IAAA,eACExI,KAAsC,CAAAqB,aAAtC,CAAsC,IAAtC,EAAsC,IAAtC,EAAsC,+BAAtC,CADF,eAEErB,KAAI,CAAAqB,aAAJ,CAAI,IAAJ,EAAI;AAAAoH,IAAAA,KAAK,EAAE;AAAEC,MAAAA,SAAS,EAAE,QAAA;AAAb,KAAA;GAAX,EAAqCd,OAArC,CAFF,EAGGK,KAAK,gBAAGjI,KAAA,CAAAqB,aAAA,CAAA,KAAA,EAAA;AAAKoH,IAAAA,KAAK,EAAEN,SAAAA;AAAZ,GAAA,EAAwBF,KAAxB,CAAH,GAA0C,IAHlD,EAIGM,OAJH,CADF,CAAA;AAQD,CAAA;;AAcY,MAAAI,mBAAA,SAA4B3I,KAAK,CAAC4I,SAAlC,CAGZ;EACCC,WAAA,CAAYC,KAAZ,EAA2C;AACzC,IAAA,KAAA,CAAMA,KAAN,CAAA,CAAA;AACA,IAAA,IAAA,CAAK3D,KAAL,GAAa;MACX7B,QAAQ,EAAEwF,KAAK,CAACxF,QADL;MAEX9C,KAAK,EAAEsI,KAAK,CAACtI,KAAAA;KAFf,CAAA;AAID,GAAA;;EAE8B,OAAxBuI,wBAAwB,CAACvI,KAAD,EAAW;IACxC,OAAO;AAAEA,MAAAA,KAAK,EAAEA,KAAAA;KAAhB,CAAA;AACD,GAAA;;AAE8B,EAAA,OAAxBwI,wBAAwB,CAC7BF,KAD6B,EAE7B3D,KAF6B,EAEE;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAIA,KAAK,CAAC7B,QAAN,KAAmBwF,KAAK,CAACxF,QAA7B,EAAuC;MACrC,OAAO;QACL9C,KAAK,EAAEsI,KAAK,CAACtI,KADR;QAEL8C,QAAQ,EAAEwF,KAAK,CAACxF,QAAAA;OAFlB,CAAA;AAID,KAf8B;AAkB/B;AACA;AACA;;;IACA,OAAO;AACL9C,MAAAA,KAAK,EAAEsI,KAAK,CAACtI,KAAN,IAAe2E,KAAK,CAAC3E,KADvB;MAEL8C,QAAQ,EAAE6B,KAAK,CAAC7B,QAAAA;KAFlB,CAAA;AAID,GAAA;;AAED2F,EAAAA,iBAAiB,CAACzI,KAAD,EAAa0I,SAAb,EAA2B;AAC1C3I,IAAAA,OAAO,CAACC,KAAR,CACE,uDADF,EAEEA,KAFF,EAGE0I,SAHF,CAAA,CAAA;AAKD,GAAA;;AAEDC,EAAAA,MAAM,GAAA;AACJ,IAAA,OAAO,IAAKhE,CAAAA,KAAL,CAAW3E,KAAX,gBACLR,KAAA,CAAAqB,aAAA,CAACa,YAAY,CAACsD,QAAd;MAAuB/E,KAAK,EAAE,IAAKqI,CAAAA,KAAL,CAAWM,YAAAA;KAAzC,eACEpJ,KAAA,CAAAqB,aAAA,CAACgB,iBAAiB,CAACmD,QAAnB,EAA2B;AACzB/E,MAAAA,KAAK,EAAE,IAAA,CAAK0E,KAAL,CAAW3E,KADO;MAEzB6I,QAAQ,EAAE,IAAKP,CAAAA,KAAL,CAAWQ,SAAAA;AAFI,KAA3B,CADF,CADK,GAQL,IAAKR,CAAAA,KAAL,CAAWO,QARb,CAAA;AAUD,GAAA;;AA7DF,CAAA;;AAsED,SAASE,aAAT,CAAuB;EAAEH,YAAF;EAAgBhF,KAAhB;AAAuBiF,EAAAA,QAAAA;AAAvB,CAAvB,EAA4E;EAC1E,IAAIG,iBAAiB,GAAGxJ,KAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAxB,CAD0E;AAI1E;;AACA,EAAA,IACE6H,iBAAiB,IACjBA,iBAAiB,CAACC,MADlB,IAEAD,iBAAiB,CAACE,aAFlB,IAGAtF,KAAK,CAACkC,KAAN,CAAYqD,YAJd,EAKE;IACAH,iBAAiB,CAACE,aAAlB,CAAgCE,0BAAhC,GAA6DxF,KAAK,CAACkC,KAAN,CAAYuD,EAAzE,CAAA;AACD,GAAA;;AAED,EAAA,oBACE7J,KAAA,CAAAqB,aAAA,CAACa,YAAY,CAACsD,QAAd,EAAsB;AAAC/E,IAAAA,KAAK,EAAE2I,YAAAA;GAA9B,EACGC,QADH,CADF,CAAA;AAKD,CAAA;;AAEK,SAAUjC,cAAV,CACJhF,OADI,EAEJ6D,aAA8B,GAAA,EAF1B,EAGJ6D,eAHI,EAGkC;EAEtC,IAAI1H,OAAO,IAAI,IAAf,EAAqB;IACnB,IAAI0H,eAAe,EAAEC,MAArB,EAA6B;AAC3B;AACA;MACA3H,OAAO,GAAG0H,eAAe,CAAC1H,OAA1B,CAAA;AACD,KAJD,MAIO;AACL,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,IAAI+E,eAAe,GAAG/E,OAAtB,CAZsC;;AAetC,EAAA,IAAI2H,MAAM,GAAGD,eAAe,EAAEC,MAA9B,CAAA;;EACA,IAAIA,MAAM,IAAI,IAAd,EAAoB;IAClB,IAAIC,UAAU,GAAG7C,eAAe,CAAC8C,SAAhB,CACdC,CAAD,IAAOA,CAAC,CAAC5D,KAAF,CAAQuD,EAAR,IAAcE,MAAM,GAAGG,CAAC,CAAC5D,KAAF,CAAQuD,EAAX,CADZ,CAAjB,CAAA;AAGA,IAAA,EACEG,UAAU,IAAI,CADhB,CAAA,GAAAtH,SAAS,CAAA,KAAA,EAEoD,CAAAqH,wDAAAA,EAAAA,MAAQ,CAF5D,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AAIA5C,IAAAA,eAAe,GAAGA,eAAe,CAACJ,KAAhB,CAChB,CADgB,EAEhBoD,IAAI,CAACC,GAAL,CAASjD,eAAe,CAACxB,MAAzB,EAAiCqE,UAAU,GAAG,CAA9C,CAFgB,CAAlB,CAAA;AAID,GAAA;;EAED,OAAO7C,eAAe,CAACkD,WAAhB,CAA4B,CAAClI,MAAD,EAASiC,KAAT,EAAgBkG,KAAhB,KAAyB;AAC1D,IAAA,IAAI9J,KAAK,GAAG4D,KAAK,CAACkC,KAAN,CAAYuD,EAAZ,GAAiBE,MAAM,GAAG3F,KAAK,CAACkC,KAAN,CAAYuD,EAAf,CAAvB,GAA4C,IAAxD,CAD0D;;AAG1D,IAAA,IAAIF,YAAY,GAAGG,eAAe,GAC9B1F,KAAK,CAACkC,KAAN,CAAYqD,YAAZ,iBAA4B3J,KAAC,CAAAqB,aAAD,CAACqG,mBAAD,EAAuB,IAAvB,CADE,GAE9B,IAFJ,CAAA;AAGA,IAAA,IAAItF,OAAO,GAAG6D,aAAa,CAACsE,MAAd,CAAqBpD,eAAe,CAACJ,KAAhB,CAAsB,CAAtB,EAAyBuD,KAAK,GAAG,CAAjC,CAArB,CAAd,CAAA;;IACA,IAAIE,WAAW,GAAG,mBAChBxK,KAAA,CAAAqB,aAAA,CAACkI,aAAD,EAAe;AAAAnF,MAAAA,KAAK,EAAEA,KAAP;AAAcgF,MAAAA,YAAY,EAAE;QAAEjH,MAAF;AAAUC,QAAAA,OAAAA;AAAV,OAAA;KAA3C,EACG5B,KAAK,GACFmJ,YADE,GAEFvF,KAAK,CAACkC,KAAN,CAAYW,OAAZ,KAAwBC,SAAxB,GACA9C,KAAK,CAACkC,KAAN,CAAYW,OADZ,GAEA9E,MALN,CADF,CAP0D;AAiB1D;AACA;;;AACA,IAAA,OAAO2H,eAAe,KAAK1F,KAAK,CAACkC,KAAN,CAAYqD,YAAZ,IAA4BW,KAAK,KAAK,CAA3C,CAAf,gBACLtK,KAAA,CAAAqB,aAAA,CAACsH,mBAAD,EAAoB;MAClBrF,QAAQ,EAAEwG,eAAe,CAACxG,QADR;AAElBgG,MAAAA,SAAS,EAAEK,YAFO;AAGlBnJ,MAAAA,KAAK,EAAEA,KAHW;MAIlB6I,QAAQ,EAAEmB,WAAW,EAJH;AAKlBpB,MAAAA,YAAY,EAAE;AAAEjH,QAAAA,MAAM,EAAE,IAAV;AAAgBC,QAAAA,OAAAA;AAAhB,OAAA;KALhB,CADK,GASLoI,WAAW,EATb,CAAA;GAnBK,EA8BJ,IA9BI,CAAP,CAAA;AA+BD,CAAA;AAED,IAAKC,cAAL,CAAA;;AAAA,CAAA,UAAKA,cAAL,EAAmB;AACjBA,EAAAA,cAAA,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AACAA,EAAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;AACD,CAHD,EAAKA,cAAc,KAAdA,cAAc,GAGlB,EAHkB,CAAnB,CAAA,CAAA;;AAKA,IAAKC,mBAAL,CAAA;;AAAA,CAAA,UAAKA,mBAAL,EAAwB;AACtBA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,eAAA,CAAA,GAAA,eAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AACAA,EAAAA,mBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAA,CAAA;AACD,CARD,EAAKA,mBAAmB,KAAnBA,mBAAmB,GAQvB,EARuB,CAAxB,CAAA,CAAA;;AAUA,SAASC,yBAAT,CACEC,QADF,EACgD;EAE9C,OAAO,CAAA,EAAGA,QAAQ,CAAlB,0FAAA,CAAA,CAAA;AACD,CAAA;;AAED,SAASC,oBAAT,CAA8BD,QAA9B,EAAsD;AACpD,EAAA,IAAIE,GAAG,GAAG9K,KAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAV,CAAA;EACA,CAAUmJ,GAAV,GAAApI,SAAS,CAAMiI,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAA/B,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AACA,EAAA,OAAOE,GAAP,CAAA;AACD,CAAA;;AAED,SAASC,kBAAT,CAA4BH,QAA5B,EAAyD;AACvD,EAAA,IAAIzF,KAAK,GAAGnF,KAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAAZ,CAAA;EACA,CAAUqD,KAAV,GAAAzC,SAAS,CAAQiI,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAAjC,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AACA,EAAA,OAAOzF,KAAP,CAAA;AACD,CAAA;;AAED,SAAS6F,eAAT,CAAyBJ,QAAzB,EAAsD;AACpD,EAAA,IAAItE,KAAK,GAAGtG,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAZ,CAAA;EACA,CAAUoE,KAAV,GAAA5D,SAAS,CAAQiI,KAAAA,EAAAA,yBAAyB,CAACC,QAAD,CAAjC,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AACA,EAAA,OAAOtE,KAAP,CAAA;AACD,CAAA;;AAED,SAAS2E,iBAAT,CAA2BL,QAA3B,EAAwD;AACtD,EAAA,IAAItE,KAAK,GAAG0E,eAAe,CAACJ,QAAD,CAA3B,CAAA;AACA,EAAA,IAAIM,SAAS,GAAG5E,KAAK,CAAClE,OAAN,CAAckE,KAAK,CAAClE,OAAN,CAAcuD,MAAd,GAAuB,CAArC,CAAhB,CAAA;AACA,EAAA,CACEuF,SAAS,CAAC5E,KAAV,CAAgBuD,EADlB,GAAAnH,SAAS,CAEJ,KAAA,EAAA,CAAA,EAAAkI,QAAgE,CAAA,sDAAA,CAF5D,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AAIA,EAAA,OAAOM,SAAS,CAAC5E,KAAV,CAAgBuD,EAAvB,CAAA;AACD,CAAA;AAED;;;AAGG;;;SACasB,gBAAa;AAC3B,EAAA,IAAIhG,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAACU,aAArB,CAA9B,CAAA;EACA,OAAOjG,KAAK,CAACkG,UAAb,CAAA;AACD,CAAA;AAED;;;AAGG;;SACaC,iBAAc;AAC5B,EAAA,IAAI9B,iBAAiB,GAAGqB,oBAAoB,CAACJ,cAAc,CAACc,cAAhB,CAA5C,CAAA;AACA,EAAA,IAAIpG,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAACa,cAArB,CAA9B,CAAA;EACA,OAAO;AACLC,IAAAA,UAAU,EAAEhC,iBAAiB,CAACiC,MAAlB,CAAyBD,UADhC;IAELrG,KAAK,EAAEA,KAAK,CAACuG,YAAAA;GAFf,CAAA;AAID,CAAA;AAED;;;AAGG;;SACaC,aAAU;EACxB,IAAI;IAAEvJ,OAAF;AAAWwJ,IAAAA,UAAAA;AAAX,GAAA,GAA0Bb,kBAAkB,CAC9CL,mBAAmB,CAACmB,UAD0B,CAAhD,CAAA;EAGA,OAAO7L,KAAK,CAAC2D,OAAN,CACL,MACEvB,OAAO,CAAC+B,GAAR,CAAaC,KAAD,IAAU;IACpB,IAAI;MAAErB,QAAF;AAAY6C,MAAAA,MAAAA;KAAWxB,GAAAA,KAA3B,CADoB;AAGpB;AACA;;IACA,OAAO;AACLyF,MAAAA,EAAE,EAAEzF,KAAK,CAACkC,KAAN,CAAYuD,EADX;MAEL9G,QAFK;MAGL6C,MAHK;MAILkG,IAAI,EAAEF,UAAU,CAACxH,KAAK,CAACkC,KAAN,CAAYuD,EAAb,CAJX;AAKLkC,MAAAA,MAAM,EAAE3H,KAAK,CAACkC,KAAN,CAAYyF,MAAAA;KALtB,CAAA;AAOD,GAZD,CAFG,EAeL,CAAC3J,OAAD,EAAUwJ,UAAV,CAfK,CAAP,CAAA;AAiBD,CAAA;AAED;;AAEG;;SACaI,gBAAa;AAC3B,EAAA,IAAI7G,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAACuB,aAArB,CAA9B,CAAA;AACA,EAAA,IAAIC,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAACuB,aAArB,CAA/B,CAAA;;EAEA,IAAI9G,KAAK,CAAC4E,MAAN,IAAgB5E,KAAK,CAAC4E,MAAN,CAAamC,OAAb,CAAyB,IAAA,IAA7C,EAAmD;AACjD3L,IAAAA,OAAO,CAACC,KAAR,8DAC+D0L,OAAO,CADtE,CAAA,CAAA,CAAA,CAAA;AAGA,IAAA,OAAOhF,SAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAO/B,KAAK,CAACyG,UAAN,CAAiBM,OAAjB,CAAP,CAAA;AACD,CAAA;AAED;;AAEG;;AACG,SAAUC,kBAAV,CAA6BD,OAA7B,EAA4C;AAChD,EAAA,IAAI/G,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAAC0B,kBAArB,CAA9B,CAAA;AACA,EAAA,OAAOjH,KAAK,CAACyG,UAAN,CAAiBM,OAAjB,CAAP,CAAA;AACD,CAAA;AAED;;AAEG;;SACaG,gBAAa;AAC3B,EAAA,IAAIlH,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAAC4B,aAArB,CAA9B,CAAA;AAEA,EAAA,IAAIhG,KAAK,GAAGtG,KAAK,CAAC6C,UAAN,CAAiBX,YAAjB,CAAZ,CAAA;EACA,CAAUoE,KAAV,GAAA5D,SAAS,CAAA,KAAA,EAAQ,kDAAR,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;EAEA,OAAO/C,MAAM,CAAC4M,MAAP,CAAcpH,KAAK,EAAEqH,UAAP,IAAqB,EAAnC,CAAuC,CAAA,CAAvC,CAAP,CAAA;AACD,CAAA;AAED;;;;AAIG;;SACa7E,gBAAa;AAC3B,EAAA,IAAInH,KAAK,GAAGR,KAAK,CAAC6C,UAAN,CAAiBR,iBAAjB,CAAZ,CAAA;AACA,EAAA,IAAI8C,KAAK,GAAG4F,kBAAkB,CAACL,mBAAmB,CAAC+B,aAArB,CAA9B,CAAA;EACA,IAAIP,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAAC+B,aAArB,CAA/B,CAH2B;AAM3B;;AACA,EAAA,IAAIjM,KAAJ,EAAW;AACT,IAAA,OAAOA,KAAP,CAAA;AACD,GAT0B;;;AAY3B,EAAA,OAAO2E,KAAK,CAAC4E,MAAN,GAAemC,OAAf,CAAP,CAAA;AACD,CAAA;AAED;;AAEG;;SACaQ,gBAAa;AAC3B,EAAA,IAAIjM,KAAK,GAAGT,KAAK,CAAC6C,UAAN,CAAiBd,YAAjB,CAAZ,CAAA;EACA,OAAOtB,KAAK,EAAEkM,KAAd,CAAA;AACD,CAAA;AAED;;AAEG;;SACaC,gBAAa;AAC3B,EAAA,IAAInM,KAAK,GAAGT,KAAK,CAAC6C,UAAN,CAAiBd,YAAjB,CAAZ,CAAA;EACA,OAAOtB,KAAK,EAAEoM,MAAd,CAAA;AACD,CAAA;AAED,IAAIC,SAAS,GAAG,CAAhB,CAAA;AAEA;;;;;AAKG;;AACG,SAAUC,UAAV,CAAqBC,WAArB,EAA2D;EAC/D,IAAI;AAAEvB,IAAAA,MAAAA;AAAF,GAAA,GAAaZ,oBAAoB,CAACJ,cAAc,CAACwC,UAAhB,CAArC,CAAA;AACA,EAAA,IAAI,CAACC,UAAD,CAAelN,GAAAA,KAAK,CAACJ,QAAN,CAAe,MAAMuN,MAAM,CAAC,EAAEL,SAAH,CAA3B,CAAnB,CAAA;AAEA,EAAA,IAAIM,eAAe,GAAGpN,KAAK,CAAC0E,WAAN,CACnB2I,IAAD,IAAS;AACP,IAAA,OAAO,OAAOL,WAAP,KAAuB,UAAvB,GACH,CAAC,CAACA,WAAW,CAACK,IAAD,CADV,GAEH,CAAC,CAACL,WAFN,CAAA;AAGD,GALmB,EAMpB,CAACA,WAAD,CANoB,CAAtB,CAAA;EASA,IAAIM,OAAO,GAAG7B,MAAM,CAAC8B,UAAP,CAAkBL,UAAlB,EAA8BE,eAA9B,CAAd,CAb+D;;AAgB/DpN,EAAAA,KAAK,CAACH,SAAN,CACE,MAAM,MAAM4L,MAAM,CAAC+B,aAAP,CAAqBN,UAArB,CADd,EAEE,CAACzB,MAAD,EAASyB,UAAT,CAFF,CAAA,CAAA;AAKA,EAAA,OAAOI,OAAP,CAAA;AACD,CAAA;AAED,MAAMG,aAAa,GAA4B,EAA/C,CAAA;;AAEA,SAASjH,WAAT,CAAqBe,GAArB,EAAkCmG,IAAlC,EAAiD9F,OAAjD,EAAgE;EAC9D,IAAI,CAAC8F,IAAD,IAAS,CAACD,aAAa,CAAClG,GAAD,CAA3B,EAAkC;AAChCkG,IAAAA,aAAa,CAAClG,GAAD,CAAb,GAAqB,IAArB,CAAA;AACA,IAAA3C,OAAO,CAAC,KAAD,EAAQgD,OAAR,CAAP,CAAA,CAAA;AACD,GAAA;AACF;;ACrzBD;;AAEG;;SACa+F,eAAe;EAC7BC,eAD6B;AAE7BnC,EAAAA,MAAAA;AAF6B,GAGT;AACpB;AACA,EAAA,IAAItG,KAAK,GAAgB0I,oBAAwB,CAC/CpC,MAAM,CAACrL,SADwC,EAE/C,MAAMqL,MAAM,CAACtG,KAFkC;AAI/C;AACA;EACA,MAAMsG,MAAM,CAACtG,KANkC,CAAjD,CAAA;AASA,EAAA,IAAIvC,SAAS,GAAG5C,KAAK,CAAC2D,OAAN,CAAc,MAAgB;IAC5C,OAAO;MACLP,UAAU,EAAEqI,MAAM,CAACrI,UADd;MAELkE,cAAc,EAAEmE,MAAM,CAACnE,cAFlB;MAGLzC,EAAE,EAAGiJ,CAAD,IAAOrC,MAAM,CAAChH,QAAP,CAAgBqJ,CAAhB,CAHN;AAIL5I,MAAAA,IAAI,EAAE,CAAC3C,EAAD,EAAK4C,KAAL,EAAY4I,IAAZ,KACJtC,MAAM,CAAChH,QAAP,CAAgBlC,EAAhB,EAAoB;QAClB4C,KADkB;QAElB6I,kBAAkB,EAAED,IAAI,EAAEC,kBAAAA;AAFR,OAApB,CALG;AASL/I,MAAAA,OAAO,EAAE,CAAC1C,EAAD,EAAK4C,KAAL,EAAY4I,IAAZ,KACPtC,MAAM,CAAChH,QAAP,CAAgBlC,EAAhB,EAAoB;AAClB0C,QAAAA,OAAO,EAAE,IADS;QAElBE,KAFkB;QAGlB6I,kBAAkB,EAAED,IAAI,EAAEC,kBAAAA;OAH5B,CAAA;KAVJ,CAAA;AAgBD,GAjBe,EAiBb,CAACvC,MAAD,CAjBa,CAAhB,CAAA;EAmBA,IAAI9I,QAAQ,GAAG8I,MAAM,CAAC9I,QAAP,IAAmB,GAAlC,CA9BoB;AAiCpB;AACA;AACA;AACA;AACA;;AACA,EAAA,oBACE3C,KAAA,CAAAqB,aAAA,CAAArB,KAAA,CAAAwI,QAAA,EAAA,IAAA,eACExI,KAAA,CAAAqB,aAAA,CAACM,iBAAiB,CAAC6D,QAAnB,EAA2B;AACzB/E,IAAAA,KAAK,EAAE;MACLgL,MADK;MAEL7I,SAFK;AAGL6G,MAAAA,MAAM,EAAE,KAHH;AAIL;AACA9G,MAAAA,QAAAA;AALK,KAAA;GADT,eASE3C,KAAA,CAAAqB,aAAA,CAACS,sBAAsB,CAAC0D,QAAxB,EAAiC;AAAA/E,IAAAA,KAAK,EAAE0E,KAAAA;AAAP,GAAjC,eACEnF,KAAC,CAAAqB,aAAD,CAAC4M,MAAD;IACEtL,QAAQ,EAAE8I,MAAM,CAAC9I;AACjBW,IAAAA,QAAQ,EAAEmI,MAAM,CAACtG,KAAP,CAAa7B;AACvBE,IAAAA,cAAc,EAAEiI,MAAM,CAACtG,KAAP,CAAa+I;AAC7BtL,IAAAA,SAAS,EAAEA,SAAAA;GAJb,EAMG6I,MAAM,CAACtG,KAAP,CAAagJ,WAAb,gBAA2BnO,KAAA,CAAAqB,aAAA,CAAC+M,MAAD,EAAU,IAAV,CAA3B,GAAwCR,eAN3C,CADF,CATF,CADF,EAqBG,IArBH,CADF,CAAA;AAyBD,CAAA;AASD;;;;AAIG;;AACG,SAAUS,YAAV,CAAuB;EAC3B1L,QAD2B;EAE3B0G,QAF2B;EAG3BiF,cAH2B;AAI3BC,EAAAA,YAAAA;AAJ2B,CAAvB,EAKc;AAClB,EAAA,IAAIC,UAAU,GAAGxO,KAAK,CAACuE,MAAN,EAAjB,CAAA;;AACA,EAAA,IAAIiK,UAAU,CAAChK,OAAX,IAAsB,IAA1B,EAAgC;AAC9BgK,IAAAA,UAAU,CAAChK,OAAX,GAAqBiK,mBAAmB,CAAC;MACvCH,cADuC;MAEvCC,YAFuC;AAGvCG,MAAAA,QAAQ,EAAE,IAAA;AAH6B,KAAD,CAAxC,CAAA;AAKD,GAAA;;AAED,EAAA,IAAIC,OAAO,GAAGH,UAAU,CAAChK,OAAzB,CAAA;EACA,IAAI,CAACW,KAAD,EAAQyJ,QAAR,IAAoB5O,KAAK,CAACJ,QAAN,CAAe;IACrCiP,MAAM,EAAEF,OAAO,CAACE,MADqB;IAErCvL,QAAQ,EAAEqL,OAAO,CAACrL,QAAAA;AAFmB,GAAf,CAAxB,CAAA;AAKAtD,EAAAA,KAAK,CAACF,eAAN,CAAsB,MAAM6O,OAAO,CAACG,MAAR,CAAeF,QAAf,CAA5B,EAAsD,CAACD,OAAD,CAAtD,CAAA,CAAA;AAEA,EAAA,oBACE3O,KAAA,CAAAqB,aAAA,CAAC4M,MAAD,EAAO;AACLtL,IAAAA,QAAQ,EAAEA,QADL;AAEL0G,IAAAA,QAAQ,EAAEA,QAFL;IAGL/F,QAAQ,EAAE6B,KAAK,CAAC7B,QAHX;IAILE,cAAc,EAAE2B,KAAK,CAAC0J,MAJjB;AAKLjM,IAAAA,SAAS,EAAE+L,OAAAA;AALN,GAAP,CADF,CAAA;AASD,CAAA;AASD;;;;;;;;AAQG;;AACG,SAAUI,QAAV,CAAmB;EACvBxM,EADuB;EAEvB0C,OAFuB;EAGvBE,KAHuB;AAIvB3C,EAAAA,QAAAA;AAJuB,CAAnB,EAKU;AACd,EAAA,CACEC,kBAAkB,EADpB,GAAAC,SAAS,CAEP,KAAA;AACA;EACA,CAJO,mEAAA,CAAA,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AAOA,EAAAkC,OAAO,CACL,CAAC5E,KAAK,CAAC6C,UAAN,CAAiBb,iBAAjB,CAAoCyH,CAAAA,MADhC,EAEoE,CAAzE,uEAAA,CAAA,GAC0E,CAD1E,sEAAA,CAAA,GAEE,0EAJG,CAAP,CAAA,CAAA;AAOA,EAAA,IAAIK,eAAe,GAAG9J,KAAK,CAAC6C,UAAN,CAAiBf,sBAAjB,CAAtB,CAAA;EACA,IAAI2C,QAAQ,GAAGZ,WAAW,EAA1B,CAAA;EAEA7D,KAAK,CAACH,SAAN,CAAgB,MAAK;AACnB;AACA;AACA;IACA,IAAIiK,eAAe,IAAIA,eAAe,CAACuB,UAAhB,CAA2BlG,KAA3B,KAAqC,MAA5D,EAAoE;AAClE,MAAA,OAAA;AACD,KAAA;;IACDV,QAAQ,CAAClC,EAAD,EAAK;MAAE0C,OAAF;MAAWE,KAAX;AAAkB3C,MAAAA,QAAAA;AAAlB,KAAL,CAAR,CAAA;GAPF,CAAA,CAAA;AAUA,EAAA,OAAO,IAAP,CAAA;AACD,CAAA;AAMD;;;;AAIG;;AACG,SAAUwM,MAAV,CAAiBlG,KAAjB,EAAmC;AACvC,EAAA,OAAOxD,SAAS,CAACwD,KAAK,CAACvD,OAAP,CAAhB,CAAA;AACD,CAAA;AAoCD;;;;AAIG;;AACG,SAAU0J,KAAV,CAAgBC,MAAhB,EAAkC;EACtCxM,SAAS,CAE+D,KAAA,EAAA,CAAA,oEAAA,CAAtE,GACE,CAHK,gEAAA,CAAA,CAAT,CAAA,CAAA,CAAA;AAKD,CAAA;AAWD;;;;;;;;AAQG;;AACa,SAAAuL,MAAA,CAAO;EACrBtL,QAAQ,EAAEwM,YAAY,GAAG,GADJ;AAErB9F,EAAAA,QAAQ,GAAG,IAFU;AAGrB/F,EAAAA,QAAQ,EAAE8L,YAHW;EAIrB5L,cAAc,GAAGgE,MAAc,CAACC,GAJX;EAKrB7E,SALqB;EAMrB6G,MAAM,EAAE4F,UAAU,GAAG,KAAA;AANA,CAAP,EAOF;AACZ,EAAA,CACE,CAAC5M,kBAAkB,EADrB,GAAAC,SAAS,CAEgD,KAAA,EAAA,CAAA,qDAAA,CAAvD,GACE,CAAA,iDAAA,CAHK,CAAT,CAAA,UADY;AAQZ;;EACA,IAAIC,QAAQ,GAAGwM,YAAY,CAAClK,OAAb,CAAqB,MAArB,EAA6B,GAA7B,CAAf,CAAA;AACA,EAAA,IAAIqK,iBAAiB,GAAGtP,KAAK,CAAC2D,OAAN,CACtB,OAAO;IAAEhB,QAAF;IAAYC,SAAZ;AAAuB6G,IAAAA,MAAM,EAAE4F,UAAAA;GAAtC,CADsB,EAEtB,CAAC1M,QAAD,EAAWC,SAAX,EAAsByM,UAAtB,CAFsB,CAAxB,CAAA;;AAKA,EAAA,IAAI,OAAOD,YAAP,KAAwB,QAA5B,EAAsC;AACpCA,IAAAA,YAAY,GAAGxI,SAAS,CAACwI,YAAD,CAAxB,CAAA;AACD,GAAA;;EAED,IAAI;AACFrM,IAAAA,QAAQ,GAAG,GADT;AAEFC,IAAAA,MAAM,GAAG,EAFP;AAGFF,IAAAA,IAAI,GAAG,EAHL;AAIFqC,IAAAA,KAAK,GAAG,IAJN;AAKFoC,IAAAA,GAAG,GAAG,SAAA;AALJ,GAAA,GAMA6H,YANJ,CAAA;AAQA,EAAA,IAAI9L,QAAQ,GAAGtD,KAAK,CAAC2D,OAAN,CAAc,MAAK;AAChC,IAAA,IAAI4L,gBAAgB,GAAGC,aAAa,CAACzM,QAAD,EAAWJ,QAAX,CAApC,CAAA;;IAEA,IAAI4M,gBAAgB,IAAI,IAAxB,EAA8B;AAC5B,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;IAED,OAAO;AACLxM,MAAAA,QAAQ,EAAEwM,gBADL;MAELvM,MAFK;MAGLF,IAHK;MAILqC,KAJK;AAKLoC,MAAAA,GAAAA;KALF,CAAA;AAOD,GAdc,EAcZ,CAAC5E,QAAD,EAAWI,QAAX,EAAqBC,MAArB,EAA6BF,IAA7B,EAAmCqC,KAAnC,EAA0CoC,GAA1C,CAdY,CAAf,CAAA;AAgBA,EAAA3C,OAAO,CACLtB,QAAQ,IAAI,IADP,EAEL,qBAAqBX,QAA0C,CAAA,gCAAA,CAA/D,GACE,CAAII,CAAAA,EAAAA,QAAQ,GAAGC,MAAM,CAAA,EAAGF,IAA2C,CADrE,qCAAA,CAAA,GAEE,kDAJG,CAAP,CAAA,CAAA;;EAOA,IAAIQ,QAAQ,IAAI,IAAhB,EAAsB;AACpB,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,oBACEtD,mBAAA,CAACgC,iBAAiB,CAACwD,QAAnB,EAA4B;AAAA/E,IAAAA,KAAK,EAAE6O,iBAAAA;GAAnC,eACEtP,KAAA,CAAAqB,aAAA,CAACY,eAAe,CAACuD,QAAjB;AACE6D,IAAAA,QAAQ,EAAEA;AACV5I,IAAAA,KAAK,EAAE;MAAE6C,QAAF;AAAYE,MAAAA,cAAAA;AAAZ,KAAA;GAFT,CADF,CADF,CAAA;AAQD,CAAA;AAOD;;;;;AAKG;;SACa4K,OAAO;EACrB/E,QADqB;AAErB/F,EAAAA,QAAAA;AAFqB,GAGT;EACZ,IAAIkG,iBAAiB,GAAGxJ,KAAK,CAAC6C,UAAN,CAAiBlB,iBAAjB,CAAxB,CADY;AAGZ;AACA;;AACA,EAAA,IAAImE,MAAM,GACR0D,iBAAiB,IAAI,CAACH,QAAtB,GACKG,iBAAiB,CAACiC,MAAlB,CAAyB3F,MAD9B,GAEI2J,wBAAwB,CAACpG,QAAD,CAH9B,CAAA;AAIA,EAAA,OAAOxD,SAAS,CAACC,MAAD,EAASxC,QAAT,CAAhB,CAAA;AACD,CAAA;AAYD;;;AAGG;;AACG,SAAUoM,KAAV,CAAgB;EAAErG,QAAF;EAAYM,YAAZ;AAA0BgG,EAAAA,OAAAA;AAA1B,CAAhB,EAA+D;AACnE,EAAA,oBACE3P,KAAC,CAAAqB,aAAD,CAACuO,kBAAD,EAAoB;AAAAD,IAAAA,OAAO,EAAEA,OAAT;AAAkBhG,IAAAA,YAAY,EAAEA,YAAAA;GAApD,eACE3J,KAAA,CAAAqB,aAAA,CAACwO,YAAD,EAAe,IAAf,EAAexG,QAAf,CADF,CADF,CAAA;AAKD,CAAA;AAWD,IAAKyG,iBAAL,CAAA;;AAAA,CAAA,UAAKA,iBAAL,EAAsB;EACpBA,iBAAA,CAAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EACAA,iBAAA,CAAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;EACAA,iBAAA,CAAAA,iBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA,CAAA;AACD,CAJD,EAAKA,iBAAiB,KAAjBA,iBAAiB,GAIrB,EAJqB,CAAtB,CAAA,CAAA;;AAMA,MAAMC,mBAAmB,GAAG,IAAIC,OAAJ,CAAY,MAAK,EAAjB,CAA5B,CAAA;;AAEA,MAAMJ,kBAAN,SAAiC5P,KAAK,CAAC4I,SAAvC,CAGC;EACCC,WAAA,CAAYC,KAAZ,EAA0C;AACxC,IAAA,KAAA,CAAMA,KAAN,CAAA,CAAA;AACA,IAAA,IAAA,CAAK3D,KAAL,GAAa;AAAE3E,MAAAA,KAAK,EAAE,IAAA;KAAtB,CAAA;AACD,GAAA;;EAE8B,OAAxBuI,wBAAwB,CAACvI,KAAD,EAAW;IACxC,OAAO;AAAEA,MAAAA,KAAAA;KAAT,CAAA;AACD,GAAA;;AAEDyI,EAAAA,iBAAiB,CAACzI,KAAD,EAAa0I,SAAb,EAA2B;AAC1C3I,IAAAA,OAAO,CAACC,KAAR,CACE,kDADF,EAEEA,KAFF,EAGE0I,SAHF,CAAA,CAAA;AAKD,GAAA;;AAEDC,EAAAA,MAAM,GAAA;IACJ,IAAI;MAAEE,QAAF;MAAYM,YAAZ;AAA0BgG,MAAAA,OAAAA;AAA1B,KAAA,GAAsC,KAAK7G,KAA/C,CAAA;IAEA,IAAImH,OAAO,GAA0B,IAArC,CAAA;AACA,IAAA,IAAInI,MAAM,GAAsBgI,iBAAiB,CAACI,OAAlD,CAAA;;AAEA,IAAA,IAAI,EAAEP,OAAO,YAAYK,OAArB,CAAJ,EAAmC;AACjC;MACAlI,MAAM,GAAGgI,iBAAiB,CAACK,OAA3B,CAAA;AACAF,MAAAA,OAAO,GAAGD,OAAO,CAACL,OAAR,EAAV,CAAA;AACAhQ,MAAAA,MAAM,CAACyQ,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;AAAEI,QAAAA,GAAG,EAAE,MAAM,IAAA;OAAxD,CAAA,CAAA;AACA1Q,MAAAA,MAAM,CAACyQ,cAAP,CAAsBH,OAAtB,EAA+B,OAA/B,EAAwC;AAAEI,QAAAA,GAAG,EAAE,MAAMV,OAAAA;OAArD,CAAA,CAAA;AACD,KAND,MAMO,IAAI,IAAA,CAAKxK,KAAL,CAAW3E,KAAf,EAAsB;AAC3B;MACAsH,MAAM,GAAGgI,iBAAiB,CAACtP,KAA3B,CAAA;AACA,MAAA,IAAI8P,WAAW,GAAG,IAAKnL,CAAAA,KAAL,CAAW3E,KAA7B,CAAA;AACAyP,MAAAA,OAAO,GAAGD,OAAO,CAACO,MAAR,EAAiBC,CAAAA,KAAjB,CAAuB,MAAO,EAA9B,CAAV,CAJ2B;;AAK3B7Q,MAAAA,MAAM,CAACyQ,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;AAAEI,QAAAA,GAAG,EAAE,MAAM,IAAA;OAAxD,CAAA,CAAA;AACA1Q,MAAAA,MAAM,CAACyQ,cAAP,CAAsBH,OAAtB,EAA+B,QAA/B,EAAyC;AAAEI,QAAAA,GAAG,EAAE,MAAMC,WAAAA;OAAtD,CAAA,CAAA;AACD,KAPM,MAOA,IAAKX,OAA0B,CAACc,QAAhC,EAA0C;AAC/C;AACAR,MAAAA,OAAO,GAAGN,OAAV,CAAA;MACA7H,MAAM,GACJmI,OAAO,CAACpD,MAAR,KAAmB3F,SAAnB,GACI4I,iBAAiB,CAACtP,KADtB,GAEIyP,OAAO,CAACtD,KAAR,KAAkBzF,SAAlB,GACA4I,iBAAiB,CAACK,OADlB,GAEAL,iBAAiB,CAACI,OALxB,CAAA;AAMD,KATM,MASA;AACL;MACApI,MAAM,GAAGgI,iBAAiB,CAACI,OAA3B,CAAA;AACAvQ,MAAAA,MAAM,CAACyQ,cAAP,CAAsBT,OAAtB,EAA+B,UAA/B,EAA2C;AAAEU,QAAAA,GAAG,EAAE,MAAM,IAAA;OAAxD,CAAA,CAAA;AACAJ,MAAAA,OAAO,GAAGN,OAAO,CAACe,IAAR,CACP5E,IAAD,IACEnM,MAAM,CAACyQ,cAAP,CAAsBT,OAAtB,EAA+B,OAA/B,EAAwC;AAAEU,QAAAA,GAAG,EAAE,MAAMvE,IAAAA;OAArD,CAFM,EAGPtL,KAAD,IACEb,MAAM,CAACyQ,cAAP,CAAsBT,OAAtB,EAA+B,QAA/B,EAAyC;AAAEU,QAAAA,GAAG,EAAE,MAAM7P,KAAAA;AAAb,OAAzC,CAJM,CAAV,CAAA;AAMD,KAAA;;IAED,IACEsH,MAAM,KAAKgI,iBAAiB,CAACtP,KAA7B,IACAyP,OAAO,CAACpD,MAAR,YAA0B8D,oBAF5B,EAGE;AACA;AACA,MAAA,MAAMZ,mBAAN,CAAA;AACD,KAAA;;IAED,IAAIjI,MAAM,KAAKgI,iBAAiB,CAACtP,KAA7B,IAAsC,CAACmJ,YAA3C,EAAyD;AACvD;MACA,MAAMsG,OAAO,CAACpD,MAAd,CAAA;AACD,KAAA;;AAED,IAAA,IAAI/E,MAAM,KAAKgI,iBAAiB,CAACtP,KAAjC,EAAwC;AACtC;AACA,MAAA,oBAAOR,KAAC,CAAAqB,aAAD,CAACU,YAAY,CAACyD,QAAd,EAAuB;AAAA/E,QAAAA,KAAK,EAAEwP,OAAP;AAAgB5G,QAAAA,QAAQ,EAAEM,YAAAA;AAA1B,OAAvB,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,IAAI7B,MAAM,KAAKgI,iBAAiB,CAACK,OAAjC,EAA0C;AACxC;AACA,MAAA,oBAAOnQ,KAAC,CAAAqB,aAAD,CAACU,YAAY,CAACyD,QAAd,EAAuB;AAAA/E,QAAAA,KAAK,EAAEwP,OAAP;AAAgB5G,QAAAA,QAAQ,EAAEA,QAAAA;AAA1B,OAAvB,CAAP,CAAA;AACD,KA7DG;;;AAgEJ,IAAA,MAAM4G,OAAN,CAAA;AACD,GAAA;;AAnFF,CAAA;AAsFD;;;AAGG;;;AACH,SAASJ,YAAT,CAAsB;AACpBxG,EAAAA,QAAAA;AADoB,CAAtB,EAIC;EACC,IAAIyC,IAAI,GAAGY,aAAa,EAAxB,CAAA;AACA,EAAA,IAAIkE,QAAQ,GAAG,OAAOvH,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAACyC,IAAD,CAAzC,GAAkDzC,QAAjE,CAAA;EACA,oBAAOrJ,KAAA,CAAAqB,aAAA,CAAArB,KAAA,CAAAwI,QAAA,EAAA,IAAA,EAAGoI,QAAH,CAAP,CAAA;AACD;AAGD;AACA;;AAEA;;;;;;AAMG;;;SACanB,yBACdpG,UACA9C,aAAuB,IAAE;EAEzB,IAAIT,MAAM,GAAkB,EAA5B,CAAA;EAEA9F,KAAK,CAAC6Q,QAAN,CAAeC,OAAf,CAAuBzH,QAAvB,EAAiC,CAACpC,OAAD,EAAUqD,KAAV,KAAmB;AAClD,IAAA,IAAI,eAACtK,KAAK,CAAC+Q,cAAN,CAAqB9J,OAArB,CAAL,EAAoC;AAClC;AACA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIA,OAAO,CAAC+J,IAAR,KAAiBhR,KAAK,CAACwI,QAA3B,EAAqC;AACnC;AACA1C,MAAAA,MAAM,CAACZ,IAAP,CAAY+L,KAAZ,CACEnL,MADF,EAEE2J,wBAAwB,CAACxI,OAAO,CAAC6B,KAAR,CAAcO,QAAf,EAAyB9C,UAAzB,CAF1B,CAAA,CAAA;AAIA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,EACEU,OAAO,CAAC+J,IAAR,KAAiB/B,KADnB,CAAA,GAAAvM,SAAS,CAAA,KAAA,EAEP,CACE,CAAA,EAAA,OAAOuE,OAAO,CAAC+J,IAAf,KAAwB,QAAxB,GAAmC/J,OAAO,CAAC+J,IAA3C,GAAkD/J,OAAO,CAAC+J,IAAR,CAAaE,IACjE,CAAA,sGAAA,CAJO,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;IAOA,EACE,CAACjK,OAAO,CAAC6B,KAAR,CAAcwB,KAAf,IAAwB,CAACrD,OAAO,CAAC6B,KAAR,CAAcO,QADzC,IAAA3G,SAAS,CAAA,KAAA,EAEP,0CAFO,CAAT,CAAA,GAAA,KAAA,CAAA,CAAA;AAKA,IAAA,IAAIyO,QAAQ,GAAG,CAAC,GAAG5K,UAAJ,EAAgB+D,KAAhB,CAAf,CAAA;AACA,IAAA,IAAIhE,KAAK,GAAgB;AACvBuD,MAAAA,EAAE,EAAE5C,OAAO,CAAC6B,KAAR,CAAce,EAAd,IAAoBsH,QAAQ,CAACC,IAAT,CAAc,GAAd,CADD;AAEvBC,MAAAA,aAAa,EAAEpK,OAAO,CAAC6B,KAAR,CAAcuI,aAFN;AAGvBpK,MAAAA,OAAO,EAAEA,OAAO,CAAC6B,KAAR,CAAc7B,OAHA;AAIvBqD,MAAAA,KAAK,EAAErD,OAAO,CAAC6B,KAAR,CAAcwB,KAJE;AAKvBxF,MAAAA,IAAI,EAAEmC,OAAO,CAAC6B,KAAR,CAAchE,IALG;AAMvBwM,MAAAA,MAAM,EAAErK,OAAO,CAAC6B,KAAR,CAAcwI,MANC;AAOvBzC,MAAAA,MAAM,EAAE5H,OAAO,CAAC6B,KAAR,CAAc+F,MAPC;AAQvBlF,MAAAA,YAAY,EAAE1C,OAAO,CAAC6B,KAAR,CAAca,YARL;AASvB4H,MAAAA,gBAAgB,EAAEtK,OAAO,CAAC6B,KAAR,CAAca,YAAd,IAA8B,IATzB;AAUvB6H,MAAAA,gBAAgB,EAAEvK,OAAO,CAAC6B,KAAR,CAAc0I,gBAVT;AAWvBzF,MAAAA,MAAM,EAAE9E,OAAO,CAAC6B,KAAR,CAAciD,MAAAA;KAXxB,CAAA;;AAcA,IAAA,IAAI9E,OAAO,CAAC6B,KAAR,CAAcO,QAAlB,EAA4B;AAC1B/C,MAAAA,KAAK,CAAC+C,QAAN,GAAiBoG,wBAAwB,CACvCxI,OAAO,CAAC6B,KAAR,CAAcO,QADyB,EAEvC8H,QAFuC,CAAzC,CAAA;AAID,KAAA;;IAEDrL,MAAM,CAACZ,IAAP,CAAYoB,KAAZ,CAAA,CAAA;GAlDF,CAAA,CAAA;AAqDA,EAAA,OAAOR,MAAP,CAAA;AACD,CAAA;AAED;;AAEG;;AACG,SAAU2L,aAAV,CACJrP,OADI,EACwB;EAE5B,OAAOgF,cAAc,CAAChF,OAAD,CAArB,CAAA;AACD,CAAA;AAED;;;;AAIG;;AACG,SAAUsP,yBAAV,CACJ5L,MADI,EACiB;AAErB,EAAA,OAAOA,MAAM,CAAC3B,GAAP,CAAYmC,KAAD,IAAU;IAC1B,IAAIqL,UAAU,GAAG,EAAE,GAAGrL,KAAAA;KAAtB,CAAA;;AACA,IAAA,IAAIqL,UAAU,CAACJ,gBAAX,IAA+B,IAAnC,EAAyC;AACvCI,MAAAA,UAAU,CAACJ,gBAAX,GAA8BI,UAAU,CAAChI,YAAX,IAA2B,IAAzD,CAAA;AACD,KAAA;;IACD,IAAIgI,UAAU,CAACtI,QAAf,EAAyB;MACvBsI,UAAU,CAACtI,QAAX,GAAsBqI,yBAAyB,CAACC,UAAU,CAACtI,QAAZ,CAA/C,CAAA;AACD,KAAA;;AACD,IAAA,OAAOsI,UAAP,CAAA;AACD,GATM,CAAP,CAAA;AAUD;;AC/ae,SAAAC,kBAAA,CACd9L,MADc,EAEdiI,IAFc,EAOb;AAED,EAAA,OAAO8D,YAAY,CAAC;IAClBlP,QAAQ,EAAEoL,IAAI,EAAEpL,QADE;IAElBgM,OAAO,EAAEF,mBAAmB,CAAC;MAC3BH,cAAc,EAAEP,IAAI,EAAEO,cADK;MAE3BC,YAAY,EAAER,IAAI,EAAEQ,YAAAA;AAFO,KAAD,CAFV;IAMlBuD,aAAa,EAAE/D,IAAI,EAAE+D,aANH;IAOlBhM,MAAM,EAAE4L,yBAAyB,CAAC5L,MAAD,CAAA;GAPhB,CAAZ,CAQJiM,UARI,EAAP,CAAA;AASD;;;;"}