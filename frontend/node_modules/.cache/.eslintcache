[{"/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx": "4", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx": "5", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx": "6", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx": "7", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx": "8", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx": "9", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx": "10", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx": "11", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx": "12", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx": "13", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts": "14", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts": "15", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts": "16", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx": "17", "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts": "18", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts": "19", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx": "20", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx": "21", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts": "23", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx": "24", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx": "25", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/UserManagement.tsx": "26", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx": "27", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx": "28"}, {"size": 554, "mtime": 1752443840172, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1752443840172, "results": "31", "hashOfConfig": "30"}, {"size": 4687, "mtime": 1752453248319, "results": "32", "hashOfConfig": "30"}, {"size": 5907, "mtime": 1752448603303, "results": "33", "hashOfConfig": "30"}, {"size": 2383, "mtime": 1752445656944, "results": "34", "hashOfConfig": "30"}, {"size": 713, "mtime": 1752445864875, "results": "35", "hashOfConfig": "30"}, {"size": 20116, "mtime": 1752447550752, "results": "36", "hashOfConfig": "30"}, {"size": 6892, "mtime": 1752446228270, "results": "37", "hashOfConfig": "30"}, {"size": 6717, "mtime": 1752449335674, "results": "38", "hashOfConfig": "30"}, {"size": 9486, "mtime": 1752448623400, "results": "39", "hashOfConfig": "30"}, {"size": 712, "mtime": 1752445874894, "results": "40", "hashOfConfig": "30"}, {"size": 7795, "mtime": 1752453071636, "results": "41", "hashOfConfig": "30"}, {"size": 1852, "mtime": 1752453060855, "results": "42", "hashOfConfig": "30"}, {"size": 1093, "mtime": 1752447642315, "results": "43", "hashOfConfig": "30"}, {"size": 1358, "mtime": 1752446795876, "results": "44", "hashOfConfig": "30"}, {"size": 1898, "mtime": 1752446765254, "results": "45", "hashOfConfig": "30"}, {"size": 10179, "mtime": 1752447659921, "results": "46", "hashOfConfig": "30"}, {"size": 5436, "mtime": 1752445621134, "results": "47", "hashOfConfig": "30"}, {"size": 7537, "mtime": 1752447429488, "results": "48", "hashOfConfig": "30"}, {"size": 1213, "mtime": 1752453049758, "results": "49", "hashOfConfig": "30"}, {"size": 8580, "mtime": 1752450975341, "results": "50", "hashOfConfig": "30"}, {"size": 11838, "mtime": 1752452610058, "results": "51", "hashOfConfig": "30"}, {"size": 12984, "mtime": 1752454459689, "results": "52", "hashOfConfig": "30"}, {"size": 10766, "mtime": 1752451070885, "results": "53", "hashOfConfig": "30"}, {"size": 13253, "mtime": 1752453764777, "results": "54", "hashOfConfig": "30"}, {"size": 12710, "mtime": 1752453014938, "results": "55", "hashOfConfig": "30"}, {"size": 14822, "mtime": 1752452987000, "results": "56", "hashOfConfig": "30"}, {"size": 8733, "mtime": 1752452729451, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kpkiyt", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx", [], ["142"], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx", [], ["143"], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx", ["144"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx", ["145"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx", ["146"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/UserManagement.tsx", ["147"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx", ["148", "149"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx", ["150"], [], {"ruleId": "151", "severity": 1, "message": "152", "line": 46, "column": 6, "nodeType": "153", "endLine": 46, "endColumn": 8, "suggestions": "154", "suppressions": "155"}, {"ruleId": "151", "severity": 1, "message": "152", "line": 70, "column": 6, "nodeType": "153", "endLine": 70, "endColumn": 8, "suggestions": "156", "suppressions": "157"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 16, "column": 3, "nodeType": "160", "messageId": "161", "endLine": 16, "endColumn": 8}, {"ruleId": "151", "severity": 1, "message": "162", "line": 84, "column": 6, "nodeType": "153", "endLine": 84, "endColumn": 12, "suggestions": "163"}, {"ruleId": "158", "severity": 1, "message": "164", "line": 142, "column": 17, "nodeType": "160", "messageId": "161", "endLine": 142, "endColumn": 23}, {"ruleId": "151", "severity": 1, "message": "165", "line": 109, "column": 6, "nodeType": "153", "endLine": 109, "endColumn": 12, "suggestions": "166"}, {"ruleId": "158", "severity": 1, "message": "159", "line": 15, "column": 3, "nodeType": "160", "messageId": "161", "endLine": 15, "endColumn": 8}, {"ruleId": "158", "severity": 1, "message": "167", "line": 71, "column": 10, "nodeType": "160", "messageId": "161", "endLine": 71, "endColumn": 17}, {"ruleId": "158", "severity": 1, "message": "159", "line": 13, "column": 3, "nodeType": "160", "messageId": "161", "endLine": 13, "endColumn": 8}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'clearError'. Either include it or remove the dependency array.", "ArrayExpression", ["168"], ["169"], ["170"], ["171"], "@typescript-eslint/no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array.", ["172"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["173"], "'loading' is assigned a value but never used.", {"desc": "174", "fix": "175"}, {"kind": "176", "justification": "177"}, {"desc": "174", "fix": "178"}, {"kind": "176", "justification": "177"}, {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, "Update the dependencies array to be: [clearError]", {"range": "183", "text": "184"}, "directive", "", {"range": "185", "text": "184"}, "Update the dependencies array to be: [fetchBooks, page]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [fetchUsers, page]", {"range": "188", "text": "189"}, [1203, 1205], "[clearError]", [1706, 1708], [2146, 2152], "[fetchBooks, page]", [2865, 2871], "[fetchUsers, page]"]