[{"/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx": "4", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx": "5", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx": "6", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx": "7", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx": "8", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx": "9", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx": "10", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx": "11", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx": "12", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx": "13", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts": "14", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts": "15", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts": "16", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx": "17", "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts": "18", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts": "19", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx": "20", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx": "21", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts": "23", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx": "24", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx": "25", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/UserManagement.tsx": "26", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx": "27", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx": "28", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/TopicViewer.tsx": "29", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/TestPage.tsx": "30"}, {"size": 554, "mtime": 1752443840172, "results": "31", "hashOfConfig": "32"}, {"size": 425, "mtime": 1752443840172, "results": "33", "hashOfConfig": "32"}, {"size": 4917, "mtime": 1752461265623, "results": "34", "hashOfConfig": "32"}, {"size": 5907, "mtime": 1752448603303, "results": "35", "hashOfConfig": "32"}, {"size": 2383, "mtime": 1752445656944, "results": "36", "hashOfConfig": "32"}, {"size": 713, "mtime": 1752445864875, "results": "37", "hashOfConfig": "32"}, {"size": 20116, "mtime": 1752447550752, "results": "38", "hashOfConfig": "32"}, {"size": 6892, "mtime": 1752446228270, "results": "39", "hashOfConfig": "32"}, {"size": 6717, "mtime": 1752449335674, "results": "40", "hashOfConfig": "32"}, {"size": 9486, "mtime": 1752448623400, "results": "41", "hashOfConfig": "32"}, {"size": 712, "mtime": 1752445874894, "results": "42", "hashOfConfig": "32"}, {"size": 7909, "mtime": 1752458713122, "results": "43", "hashOfConfig": "32"}, {"size": 1852, "mtime": 1752453060855, "results": "44", "hashOfConfig": "32"}, {"size": 1093, "mtime": 1752447642315, "results": "45", "hashOfConfig": "32"}, {"size": 1358, "mtime": 1752446795876, "results": "46", "hashOfConfig": "32"}, {"size": 1898, "mtime": 1752446765254, "results": "47", "hashOfConfig": "32"}, {"size": 10179, "mtime": 1752447659921, "results": "48", "hashOfConfig": "32"}, {"size": 5436, "mtime": 1752445621134, "results": "49", "hashOfConfig": "32"}, {"size": 7967, "mtime": 1752461176558, "results": "50", "hashOfConfig": "32"}, {"size": 1213, "mtime": 1752453049758, "results": "51", "hashOfConfig": "32"}, {"size": 8580, "mtime": 1752450975341, "results": "52", "hashOfConfig": "32"}, {"size": 12188, "mtime": 1752461080907, "results": "53", "hashOfConfig": "32"}, {"size": 14623, "mtime": 1752463005277, "results": "54", "hashOfConfig": "32"}, {"size": 12094, "mtime": 1752463224946, "results": "55", "hashOfConfig": "32"}, {"size": 13227, "mtime": 1752461459741, "results": "56", "hashOfConfig": "32"}, {"size": 13058, "mtime": 1752461104325, "results": "57", "hashOfConfig": "32"}, {"size": 14822, "mtime": 1752452987000, "results": "58", "hashOfConfig": "32"}, {"size": 8504, "mtime": 1752457078090, "results": "59", "hashOfConfig": "32"}, {"size": 14985, "mtime": 1752458630967, "results": "60", "hashOfConfig": "32"}, {"size": 3550, "mtime": 1752461215322, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kpkiyt", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx", [], ["152"], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx", [], ["153"], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx", ["154"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx", ["155"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx", ["156"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/UserManagement.tsx", ["157"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx", ["158", "159"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx", ["160"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/TopicViewer.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/TestPage.tsx", [], [], {"ruleId": "161", "severity": 1, "message": "162", "line": 46, "column": 6, "nodeType": "163", "endLine": 46, "endColumn": 8, "suggestions": "164", "suppressions": "165"}, {"ruleId": "161", "severity": 1, "message": "162", "line": 70, "column": 6, "nodeType": "163", "endLine": 70, "endColumn": 8, "suggestions": "166", "suppressions": "167"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 16, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 16, "endColumn": 8}, {"ruleId": "161", "severity": 1, "message": "172", "line": 86, "column": 6, "nodeType": "163", "endLine": 86, "endColumn": 12, "suggestions": "173"}, {"ruleId": "168", "severity": 1, "message": "174", "line": 142, "column": 17, "nodeType": "170", "messageId": "171", "endLine": 142, "endColumn": 23}, {"ruleId": "161", "severity": 1, "message": "175", "line": 117, "column": 6, "nodeType": "163", "endLine": 117, "endColumn": 12, "suggestions": "176"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 15, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 15, "endColumn": 8}, {"ruleId": "168", "severity": 1, "message": "177", "line": 71, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 71, "endColumn": 17}, {"ruleId": "168", "severity": 1, "message": "169", "line": 13, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 13, "endColumn": 8}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'clearError'. Either include it or remove the dependency array.", "ArrayExpression", ["178"], ["179"], ["180"], ["181"], "@typescript-eslint/no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array.", ["182"], "'result' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["183"], "'loading' is assigned a value but never used.", {"desc": "184", "fix": "185"}, {"kind": "186", "justification": "187"}, {"desc": "184", "fix": "188"}, {"kind": "186", "justification": "187"}, {"desc": "189", "fix": "190"}, {"desc": "191", "fix": "192"}, "Update the dependencies array to be: [clearError]", {"range": "193", "text": "194"}, "directive", "", {"range": "195", "text": "194"}, "Update the dependencies array to be: [fetchBooks, page]", {"range": "196", "text": "197"}, "Update the dependencies array to be: [fetchUsers, page]", {"range": "198", "text": "199"}, [1203, 1205], "[clearError]", [1706, 1708], [2256, 2262], "[fetchBooks, page]", [3207, 3213], "[fetchUsers, page]"]