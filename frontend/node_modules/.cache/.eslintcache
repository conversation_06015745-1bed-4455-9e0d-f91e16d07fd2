[{"/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx": "4", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx": "5", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx": "6", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx": "7", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx": "8", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx": "9", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx": "10", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx": "11", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx": "12", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx": "13", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts": "14", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts": "15", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts": "16", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx": "17", "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts": "18", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts": "19", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx": "20", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx": "21", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts": "23", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx": "24", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx": "25"}, {"size": 554, "mtime": 1752443840172, "results": "26", "hashOfConfig": "27"}, {"size": 425, "mtime": 1752443840172, "results": "28", "hashOfConfig": "27"}, {"size": 4687, "mtime": 1752450796393, "results": "29", "hashOfConfig": "27"}, {"size": 5907, "mtime": 1752448603303, "results": "30", "hashOfConfig": "27"}, {"size": 2383, "mtime": 1752445656944, "results": "31", "hashOfConfig": "27"}, {"size": 713, "mtime": 1752445864875, "results": "32", "hashOfConfig": "27"}, {"size": 20116, "mtime": 1752447550752, "results": "33", "hashOfConfig": "27"}, {"size": 6892, "mtime": 1752446228270, "results": "34", "hashOfConfig": "27"}, {"size": 6717, "mtime": 1752449335674, "results": "35", "hashOfConfig": "27"}, {"size": 9486, "mtime": 1752448623400, "results": "36", "hashOfConfig": "27"}, {"size": 712, "mtime": 1752445874894, "results": "37", "hashOfConfig": "27"}, {"size": 7795, "mtime": 1752450837337, "results": "38", "hashOfConfig": "27"}, {"size": 1852, "mtime": 1752450856424, "results": "39", "hashOfConfig": "27"}, {"size": 1093, "mtime": 1752447642315, "results": "40", "hashOfConfig": "27"}, {"size": 1358, "mtime": 1752446795876, "results": "41", "hashOfConfig": "27"}, {"size": 1898, "mtime": 1752446765254, "results": "42", "hashOfConfig": "27"}, {"size": 10179, "mtime": 1752447659921, "results": "43", "hashOfConfig": "27"}, {"size": 5436, "mtime": 1752445621134, "results": "44", "hashOfConfig": "27"}, {"size": 7537, "mtime": 1752447429488, "results": "45", "hashOfConfig": "27"}, {"size": 1213, "mtime": 1752450943772, "results": "46", "hashOfConfig": "27"}, {"size": 8580, "mtime": 1752450975341, "results": "47", "hashOfConfig": "27"}, {"size": 12305, "mtime": 1752451018559, "results": "48", "hashOfConfig": "27"}, {"size": 3904, "mtime": 1752446895088, "results": "49", "hashOfConfig": "27"}, {"size": 10766, "mtime": 1752451070885, "results": "50", "hashOfConfig": "27"}, {"size": 13715, "mtime": 1752451169845, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kpkiyt", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx", [], ["127"], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx", [], ["128"], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/AdminDashboard.tsx", ["129"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookManagement.tsx", ["130"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx", ["131"], [], {"ruleId": "132", "severity": 1, "message": "133", "line": 46, "column": 6, "nodeType": "134", "endLine": 46, "endColumn": 8, "suggestions": "135", "suppressions": "136"}, {"ruleId": "132", "severity": 1, "message": "133", "line": 70, "column": 6, "nodeType": "134", "endLine": 70, "endColumn": 8, "suggestions": "137", "suppressions": "138"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 17, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 17, "endColumn": 8}, {"ruleId": "132", "severity": 1, "message": "143", "line": 84, "column": 6, "nodeType": "134", "endLine": 84, "endColumn": 12, "suggestions": "144"}, {"ruleId": "139", "severity": 1, "message": "145", "line": 143, "column": 17, "nodeType": "141", "messageId": "142", "endLine": 143, "endColumn": 23}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'clearError'. Either include it or remove the dependency array.", "ArrayExpression", ["146"], ["147"], ["148"], ["149"], "@typescript-eslint/no-unused-vars", "'Paper' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array.", ["150"], "'result' is assigned a value but never used.", {"desc": "151", "fix": "152"}, {"kind": "153", "justification": "154"}, {"desc": "151", "fix": "155"}, {"kind": "153", "justification": "154"}, {"desc": "156", "fix": "157"}, "Update the dependencies array to be: [clearError]", {"range": "158", "text": "159"}, "directive", "", {"range": "160", "text": "159"}, "Update the dependencies array to be: [fetchBooks, page]", {"range": "161", "text": "162"}, [1203, 1205], "[clearError]", [1706, 1708], [2146, 2152], "[fetchBooks, page]"]