[{"/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx": "4", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx": "6", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx": "7", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx": "8", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx": "9", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx": "10", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx": "11", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx": "12", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx": "13", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts": "14", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts": "15", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts": "16", "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts": "17", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx": "18", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts": "19"}, {"size": 554, "mtime": 1752443840172, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1752443840172, "results": "22", "hashOfConfig": "21"}, {"size": 3415, "mtime": 1752445589242, "results": "23", "hashOfConfig": "21"}, {"size": 2383, "mtime": 1752445656944, "results": "24", "hashOfConfig": "21"}, {"size": 5439, "mtime": 1752446383095, "results": "25", "hashOfConfig": "21"}, {"size": 5866, "mtime": 1752446369143, "results": "26", "hashOfConfig": "21"}, {"size": 6892, "mtime": 1752446228270, "results": "27", "hashOfConfig": "21"}, {"size": 713, "mtime": 1752445864875, "results": "28", "hashOfConfig": "21"}, {"size": 20010, "mtime": 1752446650995, "results": "29", "hashOfConfig": "21"}, {"size": 9453, "mtime": 1752446358688, "results": "30", "hashOfConfig": "21"}, {"size": 712, "mtime": 1752445874894, "results": "31", "hashOfConfig": "21"}, {"size": 1852, "mtime": 1752445777255, "results": "32", "hashOfConfig": "21"}, {"size": 7795, "mtime": 1752445762327, "results": "33", "hashOfConfig": "21"}, {"size": 1898, "mtime": 1752446765254, "results": "34", "hashOfConfig": "21"}, {"size": 1105, "mtime": 1752446824401, "results": "35", "hashOfConfig": "21"}, {"size": 1358, "mtime": 1752446795876, "results": "36", "hashOfConfig": "21"}, {"size": 5436, "mtime": 1752445621134, "results": "37", "hashOfConfig": "21"}, {"size": 10064, "mtime": 1752446913635, "results": "38", "hashOfConfig": "21"}, {"size": 7477, "mtime": 1752446938928, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kpkiyt", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx", ["97"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx", ["98"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts", ["99"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx", ["100", "101"], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts", [], [], {"ruleId": "102", "severity": 1, "message": "103", "line": 76, "column": 6, "nodeType": "104", "endLine": 76, "endColumn": 20, "suggestions": "105"}, {"ruleId": "106", "severity": 1, "message": "107", "line": 8, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 7}, {"ruleId": "106", "severity": 1, "message": "110", "line": 8, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 11}, {"ruleId": "106", "severity": 1, "message": "111", "line": 29, "column": 10, "nodeType": "108", "messageId": "109", "endLine": 29, "endColumn": 19}, {"ruleId": "102", "severity": 1, "message": "112", "line": 76, "column": 6, "nodeType": "104", "endLine": 76, "endColumn": 22, "suggestions": "113"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'handleSearch' and 'query'. Either include them or remove the dependency array.", "ArrayExpression", ["114"], "@typescript-eslint/no-unused-vars", "'Card' is defined but never used.", "Identifier", "unusedVar", "'Citation' is defined but never used.", "'qaService' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCitationDetails'. Either include it or remove the dependency array.", ["115"], {"desc": "116", "fix": "117"}, {"desc": "118", "fix": "119"}, "Update the dependencies array to be: [handleSearch, query, searchParams]", {"range": "120", "text": "121"}, "Update the dependencies array to be: [open, citation, fetchCitationDetails]", {"range": "122", "text": "123"}, [2271, 2285], "[handleSearch, query, searchParams]", [1699, 1715], "[open, citation, fetchCitationDetails]"]