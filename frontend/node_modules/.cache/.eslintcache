[{"/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx": "3", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx": "4", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx": "5", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx": "6", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx": "7", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx": "8", "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx": "9", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx": "10", "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx": "11", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx": "12", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx": "13", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts": "14", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts": "15", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts": "16", "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx": "17", "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts": "18", "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts": "19"}, {"size": 554, "mtime": 1752443840172, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1752443840172, "results": "22", "hashOfConfig": "21"}, {"size": 3415, "mtime": 1752445589242, "results": "23", "hashOfConfig": "21"}, {"size": 5907, "mtime": 1752448603303, "results": "24", "hashOfConfig": "21"}, {"size": 2383, "mtime": 1752445656944, "results": "25", "hashOfConfig": "21"}, {"size": 713, "mtime": 1752445864875, "results": "26", "hashOfConfig": "21"}, {"size": 20116, "mtime": 1752447550752, "results": "27", "hashOfConfig": "21"}, {"size": 6892, "mtime": 1752446228270, "results": "28", "hashOfConfig": "21"}, {"size": 6690, "mtime": 1752448653230, "results": "29", "hashOfConfig": "21"}, {"size": 9486, "mtime": 1752448623400, "results": "30", "hashOfConfig": "21"}, {"size": 712, "mtime": 1752445874894, "results": "31", "hashOfConfig": "21"}, {"size": 7795, "mtime": 1752445762327, "results": "32", "hashOfConfig": "21"}, {"size": 1852, "mtime": 1752445777255, "results": "33", "hashOfConfig": "21"}, {"size": 1093, "mtime": 1752447642315, "results": "34", "hashOfConfig": "21"}, {"size": 1358, "mtime": 1752446795876, "results": "35", "hashOfConfig": "21"}, {"size": 1898, "mtime": 1752446765254, "results": "36", "hashOfConfig": "21"}, {"size": 10179, "mtime": 1752447659921, "results": "37", "hashOfConfig": "21"}, {"size": 5436, "mtime": 1752445621134, "results": "38", "hashOfConfig": "21"}, {"size": 7537, "mtime": 1752447429488, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kpkiyt", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MedPrep/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/LoginPage.tsx", [], ["97"], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/NotificationContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/ProfilePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/HomePage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx", [], ["98"], "/Users/<USER>/Desktop/MedPrep/frontend/src/pages/AdminPage.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts", [], [], "/Users/<USER>/Desktop/MedPrep/frontend/src/services/apiClient.ts", [], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 46, "column": 6, "nodeType": "101", "endLine": 46, "endColumn": 8, "suggestions": "102", "suppressions": "103"}, {"ruleId": "99", "severity": 1, "message": "100", "line": 70, "column": 6, "nodeType": "101", "endLine": 70, "endColumn": 8, "suggestions": "104", "suppressions": "105"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'clearError'. Either include it or remove the dependency array.", "ArrayExpression", ["106"], ["107"], ["108"], ["109"], {"desc": "110", "fix": "111"}, {"kind": "112", "justification": "113"}, {"desc": "110", "fix": "114"}, {"kind": "112", "justification": "113"}, "Update the dependencies array to be: [clearError]", {"range": "115", "text": "116"}, "directive", "", {"range": "117", "text": "116"}, [1203, 1205], "[clearError]", [1706, 1708]]