{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}", "map": {"version": 3, "names": ["React", "clsx", "styled", "styleFunctionSx", "extendSxProp", "useTheme", "jsx", "_jsx", "createBox", "options", "themeId", "defaultTheme", "defaultClassName", "generateClassName", "BoxRoot", "shouldForwardProp", "prop", "Box", "forwardRef", "inProps", "ref", "theme", "className", "component", "other", "as"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/createBox/createBox.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,eAAe,IAAIC,YAAY,QAAQ,6BAA6B;AAC3E,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,SAASA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC9C,MAAM;IACJC,OAAO;IACPC,YAAY;IACZC,gBAAgB,GAAG,aAAa;IAChCC;EACF,CAAC,GAAGJ,OAAO;EACX,MAAMK,OAAO,GAAGZ,MAAM,CAAC,KAAK,EAAE;IAC5Ba,iBAAiB,EAAEC,IAAI,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK;EAC3E,CAAC,CAAC,CAACb,eAAe,CAAC;EACnB,MAAMc,GAAG,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,GAAGA,CAACE,OAAO,EAAEC,GAAG,EAAE;IACnE,MAAMC,KAAK,GAAGhB,QAAQ,CAACM,YAAY,CAAC;IACpC,MAAM;MACJW,SAAS;MACTC,SAAS,GAAG,KAAK;MACjB,GAAGC;IACL,CAAC,GAAGpB,YAAY,CAACe,OAAO,CAAC;IACzB,OAAO,aAAaZ,IAAI,CAACO,OAAO,EAAE;MAChCW,EAAE,EAAEF,SAAS;MACbH,GAAG,EAAEA,GAAG;MACRE,SAAS,EAAErB,IAAI,CAACqB,SAAS,EAAET,iBAAiB,GAAGA,iBAAiB,CAACD,gBAAgB,CAAC,GAAGA,gBAAgB,CAAC;MACtGS,KAAK,EAAEX,OAAO,GAAGW,KAAK,CAACX,OAAO,CAAC,IAAIW,KAAK,GAAGA,KAAK;MAChD,GAAGG;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOP,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}