{"ast": null, "code": "export { default } from \"./getValidReactChildren.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/getValidReactChildren/index.js"], "sourcesContent": ["export { default } from \"./getValidReactChildren.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}