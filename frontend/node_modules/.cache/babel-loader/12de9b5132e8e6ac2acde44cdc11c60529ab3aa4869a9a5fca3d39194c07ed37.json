{"ast": null, "code": "/**\n * Search service for API calls\n */\nimport axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nclass SearchService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: `${API_URL}/api/v1`,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Add request interceptor to include auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n  }\n  async search(searchRequest) {\n    const response = await this.api.post('/search', searchRequest);\n    return response.data;\n  }\n  async getSuggestions(query, limit = 10) {\n    const response = await this.api.get('/search/suggestions', {\n      params: {\n        q: query,\n        limit\n      }\n    });\n    return response.data.suggestions;\n  }\n  async findSimilarChunks(chunkId, limit = 10, scoreThreshold = 0.8, excludeSameBook = false) {\n    const response = await this.api.post('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook\n    });\n    return response.data;\n  }\n  async getSearchHistory(limit = 50) {\n    const response = await this.api.get('/search/history', {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  }\n  async getSearchAnalytics() {\n    const response = await this.api.get('/search/analytics');\n    return response.data;\n  }\n}\nexport const searchService = new SearchService();", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "SearchService", "constructor", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "search", "searchRequest", "response", "post", "data", "getSuggestions", "query", "limit", "get", "params", "q", "suggestions", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunkId", "scoreThreshold", "excludeSameBook", "chunk_id", "score_threshold", "exclude_same_book", "getSearchHistory", "getSearchAnalytics", "searchService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts"], "sourcesContent": ["/**\n * Search service for API calls\n */\nimport axios, { AxiosInstance } from 'axios';\nimport { \n  SearchRequest, \n  SearchResponse, \n  SearchResult, \n  SearchHistoryItem,\n  PaginatedResponse \n} from '../types';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nclass SearchService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: `${API_URL}/api/v1`,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Add request interceptor to include auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  async search(searchRequest: SearchRequest): Promise<SearchResponse> {\n    const response = await this.api.post('/search', searchRequest);\n    return response.data;\n  }\n\n  async getSuggestions(query: string, limit: number = 10): Promise<string[]> {\n    const response = await this.api.get('/search/suggestions', {\n      params: { q: query, limit },\n    });\n    return response.data.suggestions;\n  }\n\n  async findSimilarChunks(\n    chunkId: string,\n    limit: number = 10,\n    scoreThreshold: number = 0.8,\n    excludeSameBook: boolean = false\n  ): Promise<SearchResult[]> {\n    const response = await this.api.post('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook,\n    });\n    return response.data;\n  }\n\n  async getSearchHistory(limit: number = 50): Promise<PaginatedResponse<SearchHistoryItem>> {\n    const response = await this.api.get('/search/history', {\n      params: { limit },\n    });\n    return response.data;\n  }\n\n  async getSearchAnalytics(): Promise<any> {\n    const response = await this.api.get('/search/analytics');\n    return response.data;\n  }\n}\n\nexport const searchService = new SearchService();\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAyB,OAAO;AAS5C,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAExE,MAAMC,aAAa,CAAC;EAGlBC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;MACtBC,OAAO,EAAE,GAAGR,OAAO,SAAS;MAC5BS,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEA,MAAMG,MAAMA,CAACC,aAA4B,EAA2B;IAClE,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkB,IAAI,CAAC,SAAS,EAAEF,aAAa,CAAC;IAC9D,OAAOC,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMC,cAAcA,CAACC,KAAa,EAAEC,KAAa,GAAG,EAAE,EAAqB;IACzE,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACuB,GAAG,CAAC,qBAAqB,EAAE;MACzDC,MAAM,EAAE;QAAEC,CAAC,EAAEJ,KAAK;QAAEC;MAAM;IAC5B,CAAC,CAAC;IACF,OAAOL,QAAQ,CAACE,IAAI,CAACO,WAAW;EAClC;EAEA,MAAMC,iBAAiBA,CACrBC,OAAe,EACfN,KAAa,GAAG,EAAE,EAClBO,cAAsB,GAAG,GAAG,EAC5BC,eAAwB,GAAG,KAAK,EACP;IACzB,MAAMb,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkB,IAAI,CAAC,iBAAiB,EAAE;MACtDa,QAAQ,EAAEH,OAAO;MACjBN,KAAK;MACLU,eAAe,EAAEH,cAAc;MAC/BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMe,gBAAgBA,CAACZ,KAAa,GAAG,EAAE,EAAiD;IACxF,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACuB,GAAG,CAAC,iBAAiB,EAAE;MACrDC,MAAM,EAAE;QAAEF;MAAM;IAClB,CAAC,CAAC;IACF,OAAOL,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMgB,kBAAkBA,CAAA,EAAiB;IACvC,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACuB,GAAG,CAAC,mBAAmB,CAAC;IACxD,OAAON,QAAQ,CAACE,IAAI;EACtB;AACF;AAEA,OAAO,MAAMiB,aAAa,GAAG,IAAItC,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}