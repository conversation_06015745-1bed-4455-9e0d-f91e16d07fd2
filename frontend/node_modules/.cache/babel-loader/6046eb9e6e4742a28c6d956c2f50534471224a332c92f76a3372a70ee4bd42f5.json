{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, LinearProgress, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Alert, IconButton, Avatar, List, ListItem, ListItemText, ListItemIcon } from '@mui/material';\nimport { CheckCircle as CheckCircleIcon, Error as ErrorIcon, Warning as WarningIcon, Refresh as RefreshIcon, NetworkCheck as NetworkIcon, Computer as ComputerIcon, Security as SecurityIcon, Storage as StorageIcon } from '@mui/icons-material';\nimport { adminService } from '../../services/adminService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SystemMonitoring = () => {\n  _s();\n  const [systemHealth, setSystemHealth] = useState(null);\n  const [processingStatus, setProcessingStatus] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n  const fetchSystemData = async () => {\n    try {\n      setLoading(true);\n      const [healthData, processingData] = await Promise.all([adminService.getSystemHealth(), adminService.getProcessingStatus()]);\n      setSystemHealth(healthData);\n      setProcessingStatus(processingData);\n      setLastUpdated(new Date());\n      setError(null);\n    } catch (err) {\n      setError('Failed to load system data');\n      console.error('System monitoring error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchSystemData();\n    // Auto-refresh every 30 seconds\n    const interval = setInterval(fetchSystemData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          sx: {\n            color: 'success.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          sx: {\n            color: 'warning.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 16\n        }, this);\n      case 'disconnected':\n      case 'critical':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          sx: {\n            color: 'error.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          sx: {\n            color: 'grey.500'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n      case 'completed':\n        return 'success';\n      case 'warning':\n      case 'processing':\n        return 'warning';\n      case 'disconnected':\n      case 'critical':\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const formatBytes = bytes => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n  const formatResponseTime = ms => {\n    return `${ms}ms`;\n  };\n  const ServiceStatusCard = ({\n    title,\n    status,\n    icon,\n    description\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            mr: 2,\n            backgroundColor: status ? 'success.main' : 'error.main'\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: status ? 'Online' : 'Offline',\n          color: status ? 'success' : 'error',\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"System Monitoring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: [\"Last updated: \", lastUpdated.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchSystemData,\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), systemHealth && systemHealth.status !== 'healthy' && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: systemHealth.status === 'warning' ? 'warning' : 'error',\n      sx: {\n        mb: 3\n      },\n      children: [\"System status: \", systemHealth.status, \". Please check the components below for details.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'grid',\n        gridTemplateColumns: {\n          xs: '1fr',\n          md: '1fr 1fr'\n        },\n        gap: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Database Connection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [getStatusIcon((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.database.status) || 'disconnected'), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                ml: 2,\n                flexGrow: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: \"PostgreSQL Database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: [\"Response time: \", formatResponseTime((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.database.response_time) || 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.database.status) || 'Unknown',\n              color: getStatusColor((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.database.status) || 'disconnected'),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Vector Database\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [getStatusIcon((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.vector_db.status) || 'disconnected'), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                ml: 2,\n                flexGrow: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: \"Qdrant Vector DB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: [\"Response time: \", formatResponseTime((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.vector_db.response_time) || 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.vector_db.status) || 'Unknown',\n              color: getStatusColor((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.vector_db.status) || 'disconnected'),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'grid',\n        gridTemplateColumns: {\n          xs: '1fr',\n          md: '1fr 1fr'\n        },\n        gap: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Storage Usage\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [\"Used: \", formatBytes(((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.total_space) || 0) - ((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.available_space) || 0))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [\"Available: \", formatBytes((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.available_space) || 0)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.usage_percentage) || 0,\n              sx: {\n                height: 8,\n                borderRadius: 4,\n                '& .MuiLinearProgress-bar': {\n                  backgroundColor: ((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.usage_percentage) || 0) > 80 ? 'error.main' : ((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.usage_percentage) || 0) > 60 ? 'warning.main' : 'success.main'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              sx: {\n                mt: 1\n              },\n              children: [(systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.usage_percentage) || 0, \"% used of \", formatBytes((systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.storage.total_space) || 0)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"System Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: getStatusIcon(systemHealth !== null && systemHealth !== void 0 && systemHealth.services.embedding_service ? 'connected' : 'disconnected')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Embedding Service\",\n                secondary: \"Text embedding generation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: getStatusIcon(systemHealth !== null && systemHealth !== void 0 && systemHealth.services.llm_service ? 'connected' : 'disconnected')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"LLM Service\",\n                secondary: \"Language model processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: getStatusIcon(systemHealth !== null && systemHealth !== void 0 && systemHealth.services.pdf_service ? 'connected' : 'disconnected')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"PDF Service\",\n                secondary: \"Document processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Processing Queue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), processingStatus.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"No active processing tasks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Book ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Started\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: processingStatus.map(item => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontFamily: \"monospace\",\n                    children: [item.book_id.slice(0, 8), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: item.status,\n                    color: getStatusColor(item.status),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                      variant: \"determinate\",\n                      value: item.progress,\n                      sx: {\n                        flexGrow: 1,\n                        height: 6\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [item.progress, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: item.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 25\n                  }, this), item.error_message && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"error\",\n                    children: item.error_message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: new Date(item.started_at).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this)]\n              }, item.book_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Service Status\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(ServiceStatusCard, {\n          title: \"Embedding Service\",\n          status: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.services.embedding_service) || false,\n          icon: /*#__PURE__*/_jsxDEV(NetworkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 19\n          }, this),\n          description: \"Text vectorization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(ServiceStatusCard, {\n          title: \"LLM Service\",\n          status: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.services.llm_service) || false,\n          icon: /*#__PURE__*/_jsxDEV(ComputerIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 19\n          }, this),\n          description: \"AI question answering\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(ServiceStatusCard, {\n          title: \"PDF Service\",\n          status: (systemHealth === null || systemHealth === void 0 ? void 0 : systemHealth.services.pdf_service) || false,\n          icon: /*#__PURE__*/_jsxDEV(StorageIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 19\n          }, this),\n          description: \"Document processing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(ServiceStatusCard, {\n          title: \"Security\",\n          status: true,\n          icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 19\n          }, this),\n          description: \"Authentication & authorization\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemMonitoring, \"MXiEmKfxJrBoneI3MLHztWxu+1U=\");\n_c = SystemMonitoring;\nexport default SystemMonitoring;\nvar _c;\n$RefreshReg$(_c, \"SystemMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "LinearProgress", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "IconButton", "Avatar", "List", "ListItem", "ListItemText", "ListItemIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Warning", "WarningIcon", "Refresh", "RefreshIcon", "NetworkCheck", "NetworkIcon", "Computer", "ComputerIcon", "Security", "SecurityIcon", "Storage", "StorageIcon", "adminService", "jsxDEV", "_jsxDEV", "SystemMonitoring", "_s", "systemHealth", "setSystemHealth", "processingStatus", "setProcessingStatus", "loading", "setLoading", "error", "setError", "lastUpdated", "setLastUpdated", "Date", "fetchSystemData", "healthData", "processingData", "Promise", "all", "getSystemHealth", "getProcessingStatus", "err", "console", "interval", "setInterval", "clearInterval", "getStatusIcon", "status", "sx", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "formatBytes", "bytes", "sizes", "i", "Math", "floor", "log", "round", "pow", "formatResponseTime", "ms", "ServiceStatusCard", "title", "icon", "description", "children", "display", "alignItems", "mb", "mr", "backgroundColor", "flexGrow", "variant", "label", "size", "justifyContent", "gutterBottom", "gap", "toLocaleTimeString", "onClick", "severity", "onClose", "gridTemplateColumns", "xs", "md", "database", "ml", "response_time", "vector_db", "storage", "total_space", "available_space", "value", "usage_percentage", "height", "borderRadius", "mt", "dense", "services", "embedding_service", "primary", "secondary", "llm_service", "pdf_service", "length", "map", "item", "fontFamily", "book_id", "slice", "progress", "message", "error_message", "started_at", "Grid", "container", "spacing", "sm", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/SystemMonitoring.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  LinearProgress,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Alert,\n  IconButton,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n} from '@mui/material';\nimport {\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Warning as WarningIcon,\n  Refresh as RefreshIcon,\n  NetworkCheck as NetworkIcon,\n  Computer as ComputerIcon,\n  Security as SecurityIcon,\n  Storage as StorageIcon,\n} from '@mui/icons-material';\nimport { adminService } from '../../services/adminService';\n\ninterface SystemHealth {\n  status: 'healthy' | 'warning' | 'critical';\n  database: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  vector_db: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  storage: {\n    available_space: number;\n    total_space: number;\n    usage_percentage: number;\n  };\n  services: {\n    embedding_service: boolean;\n    llm_service: boolean;\n    pdf_service: boolean;\n  };\n}\n\ninterface ProcessingStatus {\n  book_id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  message: string;\n  started_at: string;\n  completed_at?: string;\n  error_message?: string;\n}\n\nconst SystemMonitoring: React.FC = () => {\n  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);\n  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());\n\n  const fetchSystemData = async () => {\n    try {\n      setLoading(true);\n      const [healthData, processingData] = await Promise.all([\n        adminService.getSystemHealth(),\n        adminService.getProcessingStatus(),\n      ]);\n      \n      setSystemHealth(healthData);\n      setProcessingStatus(processingData);\n      setLastUpdated(new Date());\n      setError(null);\n    } catch (err) {\n      setError('Failed to load system data');\n      console.error('System monitoring error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchSystemData();\n    // Auto-refresh every 30 seconds\n    const interval = setInterval(fetchSystemData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n        return <CheckCircleIcon sx={{ color: 'success.main' }} />;\n      case 'warning':\n        return <WarningIcon sx={{ color: 'warning.main' }} />;\n      case 'disconnected':\n      case 'critical':\n        return <ErrorIcon sx={{ color: 'error.main' }} />;\n      default:\n        return <WarningIcon sx={{ color: 'grey.500' }} />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n      case 'completed':\n        return 'success';\n      case 'warning':\n      case 'processing':\n        return 'warning';\n      case 'disconnected':\n      case 'critical':\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const formatBytes = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatResponseTime = (ms: number) => {\n    return `${ms}ms`;\n  };\n\n  const ServiceStatusCard: React.FC<{\n    title: string;\n    status: boolean;\n    icon: React.ReactNode;\n    description: string;\n  }> = ({ title, status, icon, description }) => (\n    <Card>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n          <Avatar sx={{ mr: 2, backgroundColor: status ? 'success.main' : 'error.main' }}>\n            {icon}\n          </Avatar>\n          <Box sx={{ flexGrow: 1 }}>\n            <Typography variant=\"h6\">{title}</Typography>\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              {description}\n            </Typography>\n          </Box>\n          <Chip\n            label={status ? 'Online' : 'Offline'}\n            color={status ? 'success' : 'error'}\n            size=\"small\"\n          />\n        </Box>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          System Monitoring\n        </Typography>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Last updated: {lastUpdated.toLocaleTimeString()}\n          </Typography>\n          <IconButton onClick={fetchSystemData} color=\"primary\">\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n      </Box>\n\n      {/* System Health Alert */}\n      {systemHealth && systemHealth.status !== 'healthy' && (\n        <Alert\n          severity={systemHealth.status === 'warning' ? 'warning' : 'error'}\n          sx={{ mb: 3 }}\n        >\n          System status: {systemHealth.status}. Please check the components below for details.\n        </Alert>\n      )}\n\n      {/* Error Alert */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n\n      {/* System Overview */}\n      <Box sx={{\n        display: 'grid',\n        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },\n        gap: 3,\n        mb: 4\n      }}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Database Connection\n            </Typography>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              {getStatusIcon(systemHealth?.database.status || 'disconnected')}\n              <Box sx={{ ml: 2, flexGrow: 1 }}>\n                <Typography variant=\"body1\">\n                  PostgreSQL Database\n                </Typography>\n                <Typography variant=\"body2\" color=\"textSecondary\">\n                  Response time: {formatResponseTime(systemHealth?.database.response_time || 0)}\n                </Typography>\n              </Box>\n              <Chip\n                label={systemHealth?.database.status || 'Unknown'}\n                color={getStatusColor(systemHealth?.database.status || 'disconnected') as any}\n                size=\"small\"\n              />\n            </Box>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Vector Database\n            </Typography>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              {getStatusIcon(systemHealth?.vector_db.status || 'disconnected')}\n              <Box sx={{ ml: 2, flexGrow: 1 }}>\n                <Typography variant=\"body1\">\n                  Qdrant Vector DB\n                </Typography>\n                <Typography variant=\"body2\" color=\"textSecondary\">\n                  Response time: {formatResponseTime(systemHealth?.vector_db.response_time || 0)}\n                </Typography>\n              </Box>\n              <Chip\n                label={systemHealth?.vector_db.status || 'Unknown'}\n                color={getStatusColor(systemHealth?.vector_db.status || 'disconnected') as any}\n                size=\"small\"\n              />\n            </Box>\n          </CardContent>\n        </Card>\n      </Box>\n\n      {/* Storage Information */}\n      <Box sx={{\n        display: 'grid',\n        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },\n        gap: 3,\n        mb: 4\n      }}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Storage Usage\n            </Typography>\n            <Box sx={{ mb: 2 }}>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                <Typography variant=\"body2\">\n                  Used: {formatBytes((systemHealth?.storage.total_space || 0) - (systemHealth?.storage.available_space || 0))}\n                </Typography>\n                <Typography variant=\"body2\">\n                  Available: {formatBytes(systemHealth?.storage.available_space || 0)}\n                </Typography>\n              </Box>\n              <LinearProgress\n                variant=\"determinate\"\n                value={systemHealth?.storage.usage_percentage || 0}\n                sx={{\n                  height: 8,\n                  borderRadius: 4,\n                  '& .MuiLinearProgress-bar': {\n                    backgroundColor:\n                      (systemHealth?.storage.usage_percentage || 0) > 80\n                        ? 'error.main'\n                        : (systemHealth?.storage.usage_percentage || 0) > 60\n                          ? 'warning.main'\n                          : 'success.main',\n                  },\n                }}\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mt: 1 }}>\n                {systemHealth?.storage.usage_percentage || 0}% used of {formatBytes(systemHealth?.storage.total_space || 0)}\n              </Typography>\n            </Box>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              System Services\n            </Typography>\n            <List dense>\n              <ListItem>\n                <ListItemIcon>\n                  {getStatusIcon(systemHealth?.services.embedding_service ? 'connected' : 'disconnected')}\n                </ListItemIcon>\n                <ListItemText\n                  primary=\"Embedding Service\"\n                  secondary=\"Text embedding generation\"\n                />\n              </ListItem>\n              <ListItem>\n                <ListItemIcon>\n                  {getStatusIcon(systemHealth?.services.llm_service ? 'connected' : 'disconnected')}\n                </ListItemIcon>\n                <ListItemText\n                  primary=\"LLM Service\"\n                  secondary=\"Language model processing\"\n                />\n              </ListItem>\n              <ListItem>\n                <ListItemIcon>\n                  {getStatusIcon(systemHealth?.services.pdf_service ? 'connected' : 'disconnected')}\n                </ListItemIcon>\n                <ListItemText\n                  primary=\"PDF Service\"\n                  secondary=\"Document processing\"\n                />\n              </ListItem>\n            </List>\n          </CardContent>\n        </Card>\n      </Box>\n\n      {/* Processing Status */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Processing Queue\n          </Typography>\n          {processingStatus.length === 0 ? (\n            <Typography variant=\"body2\" color=\"textSecondary\">\n              No active processing tasks\n            </Typography>\n          ) : (\n            <TableContainer>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Book ID</TableCell>\n                    <TableCell>Status</TableCell>\n                    <TableCell>Progress</TableCell>\n                    <TableCell>Message</TableCell>\n                    <TableCell>Started</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {processingStatus.map((item) => (\n                    <TableRow key={item.book_id}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontFamily=\"monospace\">\n                          {item.book_id.slice(0, 8)}...\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={item.status}\n                          color={getStatusColor(item.status) as any}\n                          size=\"small\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          <LinearProgress\n                            variant=\"determinate\"\n                            value={item.progress}\n                            sx={{ flexGrow: 1, height: 6 }}\n                          />\n                          <Typography variant=\"body2\">\n                            {item.progress}%\n                          </Typography>\n                        </Box>\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {item.message}\n                        </Typography>\n                        {item.error_message && (\n                          <Typography variant=\"caption\" color=\"error\">\n                            {item.error_message}\n                          </Typography>\n                        )}\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {new Date(item.started_at).toLocaleTimeString()}\n                        </Typography>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Service Status Cards */}\n      <Typography variant=\"h6\" gutterBottom>\n        Service Status\n      </Typography>\n      <Grid container spacing={2}>\n        <Grid xs={12} sm={6} md={3}>\n          <ServiceStatusCard\n            title=\"Embedding Service\"\n            status={systemHealth?.services.embedding_service || false}\n            icon={<NetworkIcon />}\n            description=\"Text vectorization\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <ServiceStatusCard\n            title=\"LLM Service\"\n            status={systemHealth?.services.llm_service || false}\n            icon={<ComputerIcon />}\n            description=\"AI question answering\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <ServiceStatusCard\n            title=\"PDF Service\"\n            status={systemHealth?.services.pdf_service || false}\n            icon={<StorageIcon />}\n            description=\"Document processing\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <ServiceStatusCard\n            title=\"Security\"\n            status={true}\n            icon={<SecurityIcon />}\n            description=\"Authentication & authorization\"\n          />\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default SystemMonitoring;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,cAAc,EACdC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EAERC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,OAAO,IAAIC,WAAW,EACtBC,YAAY,IAAIC,WAAW,EAC3BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkC3D,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAsB,IAAI,CAAC;EAC3E,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAqB,EAAE,CAAC;EAChF,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAO,IAAIoD,IAAI,CAAC,CAAC,CAAC;EAEhE,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACO,UAAU,EAAEC,cAAc,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrDpB,YAAY,CAACqB,eAAe,CAAC,CAAC,EAC9BrB,YAAY,CAACsB,mBAAmB,CAAC,CAAC,CACnC,CAAC;MAEFhB,eAAe,CAACW,UAAU,CAAC;MAC3BT,mBAAmB,CAACU,cAAc,CAAC;MACnCJ,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;MAC1BH,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZX,QAAQ,CAAC,4BAA4B,CAAC;MACtCY,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEY,GAAG,CAAC;IAChD,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED9C,SAAS,CAAC,MAAM;IACdoD,eAAe,CAAC,CAAC;IACjB;IACA,MAAMS,QAAQ,GAAGC,WAAW,CAACV,eAAe,EAAE,KAAK,CAAC;IACpD,OAAO,MAAMW,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,WAAW;MAChB,KAAK,SAAS;QACZ,oBAAO3B,OAAA,CAACjB,eAAe;UAAC6C,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,SAAS;QACZ,oBAAOjC,OAAA,CAACb,WAAW;UAACyC,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,cAAc;MACnB,KAAK,UAAU;QACb,oBAAOjC,OAAA,CAACf,SAAS;UAAC2C,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAa;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD;QACE,oBAAOjC,OAAA,CAACb,WAAW;UAACyC,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAW;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACrD;EACF,CAAC;EAED,MAAMC,cAAc,GAAIP,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,WAAW;MAChB,KAAK,SAAS;MACd,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,SAAS;MACd,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,cAAc;MACnB,KAAK,UAAU;MACf,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAIC,KAAa,IAAK;IACrC,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/C,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAME,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,CAAC,GAAGG,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,OAAOF,IAAI,CAACG,KAAK,CAACN,KAAK,GAAGG,IAAI,CAACI,GAAG,CAAC,IAAI,EAAEL,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGD,KAAK,CAACC,CAAC,CAAC;EAC3E,CAAC;EAED,MAAMM,kBAAkB,GAAIC,EAAU,IAAK;IACzC,OAAO,GAAGA,EAAE,IAAI;EAClB,CAAC;EAED,MAAMC,iBAKJ,GAAGA,CAAC;IAAEC,KAAK;IAAEpB,MAAM;IAAEqB,IAAI;IAAEC;EAAY,CAAC,kBACxCjD,OAAA,CAACnC,IAAI;IAAAqF,QAAA,eACHlD,OAAA,CAAClC,WAAW;MAAAoF,QAAA,eACVlD,OAAA,CAACrC,GAAG;QAACiE,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACxDlD,OAAA,CAACvB,MAAM;UAACmD,EAAE,EAAE;YAAE0B,EAAE,EAAE,CAAC;YAAEC,eAAe,EAAE5B,MAAM,GAAG,cAAc,GAAG;UAAa,CAAE;UAAAuB,QAAA,EAC5EF;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACTjC,OAAA,CAACrC,GAAG;UAACiE,EAAE,EAAE;YAAE4B,QAAQ,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACvBlD,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAAAP,QAAA,EAAEH;UAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7CjC,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,OAAO;YAAC5B,KAAK,EAAC,eAAe;YAAAqB,QAAA,EAC9CD;UAAW;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjC,OAAA,CAAChC,IAAI;UACH0F,KAAK,EAAE/B,MAAM,GAAG,QAAQ,GAAG,SAAU;UACrCE,KAAK,EAAEF,MAAM,GAAG,SAAS,GAAG,OAAQ;UACpCgC,IAAI,EAAC;QAAO;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,oBACEjC,OAAA,CAACrC,GAAG;IAAAuF,QAAA,gBAEFlD,OAAA,CAACrC,GAAG;MAACiE,EAAE,EAAE;QAAEuB,OAAO,EAAE,MAAM;QAAES,cAAc,EAAE,eAAe;QAAER,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzFlD,OAAA,CAACpC,UAAU;QAAC6F,OAAO,EAAC,IAAI;QAACI,YAAY;QAAAX,QAAA,EAAC;MAEtC;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjC,OAAA,CAACrC,GAAG;QAACiE,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEU,GAAG,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBACzDlD,OAAA,CAACpC,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAAC5B,KAAK,EAAC,eAAe;UAAAqB,QAAA,GAAC,gBAClC,EAACvC,WAAW,CAACoD,kBAAkB,CAAC,CAAC;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACbjC,OAAA,CAACxB,UAAU;UAACwF,OAAO,EAAElD,eAAgB;UAACe,KAAK,EAAC,SAAS;UAAAqB,QAAA,eACnDlD,OAAA,CAACX,WAAW;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,YAAY,IAAIA,YAAY,CAACwB,MAAM,KAAK,SAAS,iBAChD3B,OAAA,CAACzB,KAAK;MACJ0F,QAAQ,EAAE9D,YAAY,CAACwB,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,OAAQ;MAClEC,EAAE,EAAE;QAAEyB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,GACf,iBACgB,EAAC/C,YAAY,CAACwB,MAAM,EAAC,kDACtC;IAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGAxB,KAAK,iBACJT,OAAA,CAACzB,KAAK;MAAC0F,QAAQ,EAAC,OAAO;MAACrC,EAAE,EAAE;QAAEyB,EAAE,EAAE;MAAE,CAAE;MAACa,OAAO,EAAEA,CAAA,KAAMxD,QAAQ,CAAC,IAAI,CAAE;MAAAwC,QAAA,EAClEzC;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDjC,OAAA,CAACrC,GAAG;MAACiE,EAAE,EAAE;QACPuB,OAAO,EAAE,MAAM;QACfgB,mBAAmB,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAU,CAAC;QACjDP,GAAG,EAAE,CAAC;QACNT,EAAE,EAAE;MACN,CAAE;MAAAH,QAAA,gBACAlD,OAAA,CAACnC,IAAI;QAAAqF,QAAA,eACHlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACI,YAAY;YAAAX,QAAA,EAAC;UAEtC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACrC,GAAG;YAACiE,EAAE,EAAE;cAAEuB,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,GACvDxB,aAAa,CAAC,CAAAvB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,QAAQ,CAAC3C,MAAM,KAAI,cAAc,CAAC,eAC/D3B,OAAA,CAACrC,GAAG;cAACiE,EAAE,EAAE;gBAAE2C,EAAE,EAAE,CAAC;gBAAEf,QAAQ,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC9BlD,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAE5B;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjC,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAC5B,KAAK,EAAC,eAAe;gBAAAqB,QAAA,GAAC,iBACjC,EAACN,kBAAkB,CAAC,CAAAzC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,QAAQ,CAACE,aAAa,KAAI,CAAC,CAAC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAAChC,IAAI;cACH0F,KAAK,EAAE,CAAAvD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,QAAQ,CAAC3C,MAAM,KAAI,SAAU;cAClDE,KAAK,EAAEK,cAAc,CAAC,CAAA/B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmE,QAAQ,CAAC3C,MAAM,KAAI,cAAc,CAAS;cAC9EgC,IAAI,EAAC;YAAO;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPjC,OAAA,CAACnC,IAAI;QAAAqF,QAAA,eACHlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACI,YAAY;YAAAX,QAAA,EAAC;UAEtC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACrC,GAAG;YAACiE,EAAE,EAAE;cAAEuB,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,GACvDxB,aAAa,CAAC,CAAAvB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsE,SAAS,CAAC9C,MAAM,KAAI,cAAc,CAAC,eAChE3B,OAAA,CAACrC,GAAG;cAACiE,EAAE,EAAE;gBAAE2C,EAAE,EAAE,CAAC;gBAAEf,QAAQ,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC9BlD,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAE5B;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjC,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAC5B,KAAK,EAAC,eAAe;gBAAAqB,QAAA,GAAC,iBACjC,EAACN,kBAAkB,CAAC,CAAAzC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsE,SAAS,CAACD,aAAa,KAAI,CAAC,CAAC;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAAChC,IAAI;cACH0F,KAAK,EAAE,CAAAvD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsE,SAAS,CAAC9C,MAAM,KAAI,SAAU;cACnDE,KAAK,EAAEK,cAAc,CAAC,CAAA/B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsE,SAAS,CAAC9C,MAAM,KAAI,cAAc,CAAS;cAC/EgC,IAAI,EAAC;YAAO;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjC,OAAA,CAACrC,GAAG;MAACiE,EAAE,EAAE;QACPuB,OAAO,EAAE,MAAM;QACfgB,mBAAmB,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAU,CAAC;QACjDP,GAAG,EAAE,CAAC;QACNT,EAAE,EAAE;MACN,CAAE;MAAAH,QAAA,gBACAlD,OAAA,CAACnC,IAAI;QAAAqF,QAAA,eACHlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACI,YAAY;YAAAX,QAAA,EAAC;UAEtC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACrC,GAAG;YAACiE,EAAE,EAAE;cAAEyB,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACjBlD,OAAA,CAACrC,GAAG;cAACiE,EAAE,EAAE;gBAAEuB,OAAO,EAAE,MAAM;gBAAES,cAAc,EAAE,eAAe;gBAAEP,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACnElD,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAAP,QAAA,GAAC,QACpB,EAACf,WAAW,CAAC,CAAC,CAAAhC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACC,WAAW,KAAI,CAAC,KAAK,CAAAxE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACE,eAAe,KAAI,CAAC,CAAC,CAAC;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACbjC,OAAA,CAACpC,UAAU;gBAAC6F,OAAO,EAAC,OAAO;gBAAAP,QAAA,GAAC,aACf,EAACf,WAAW,CAAC,CAAAhC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACE,eAAe,KAAI,CAAC,CAAC;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,cAAc;cACb0F,OAAO,EAAC,aAAa;cACrBoB,KAAK,EAAE,CAAA1E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACI,gBAAgB,KAAI,CAAE;cACnDlD,EAAE,EAAE;gBACFmD,MAAM,EAAE,CAAC;gBACTC,YAAY,EAAE,CAAC;gBACf,0BAA0B,EAAE;kBAC1BzB,eAAe,EACb,CAAC,CAAApD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACI,gBAAgB,KAAI,CAAC,IAAI,EAAE,GAC9C,YAAY,GACZ,CAAC,CAAA3E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACI,gBAAgB,KAAI,CAAC,IAAI,EAAE,GAChD,cAAc,GACd;gBACV;cACF;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFjC,OAAA,CAACpC,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAAC5B,KAAK,EAAC,eAAe;cAACD,EAAE,EAAE;gBAAEqD,EAAE,EAAE;cAAE,CAAE;cAAA/B,QAAA,GAC7D,CAAA/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACI,gBAAgB,KAAI,CAAC,EAAC,YAAU,EAAC3C,WAAW,CAAC,CAAAhC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEuE,OAAO,CAACC,WAAW,KAAI,CAAC,CAAC;YAAA;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEPjC,OAAA,CAACnC,IAAI;QAAAqF,QAAA,eACHlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACpC,UAAU;YAAC6F,OAAO,EAAC,IAAI;YAACI,YAAY;YAAAX,QAAA,EAAC;UAEtC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACtB,IAAI;YAACwG,KAAK;YAAAhC,QAAA,gBACTlD,OAAA,CAACrB,QAAQ;cAAAuE,QAAA,gBACPlD,OAAA,CAACnB,YAAY;gBAAAqE,QAAA,EACVxB,aAAa,CAACvB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEgF,QAAQ,CAACC,iBAAiB,GAAG,WAAW,GAAG,cAAc;cAAC;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACfjC,OAAA,CAACpB,YAAY;gBACXyG,OAAO,EAAC,mBAAmB;gBAC3BC,SAAS,EAAC;cAA2B;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjC,OAAA,CAACrB,QAAQ;cAAAuE,QAAA,gBACPlD,OAAA,CAACnB,YAAY;gBAAAqE,QAAA,EACVxB,aAAa,CAACvB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEgF,QAAQ,CAACI,WAAW,GAAG,WAAW,GAAG,cAAc;cAAC;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACfjC,OAAA,CAACpB,YAAY;gBACXyG,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAC;cAA2B;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjC,OAAA,CAACrB,QAAQ;cAAAuE,QAAA,gBACPlD,OAAA,CAACnB,YAAY;gBAAAqE,QAAA,EACVxB,aAAa,CAACvB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEgF,QAAQ,CAACK,WAAW,GAAG,WAAW,GAAG,cAAc;cAAC;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACfjC,OAAA,CAACpB,YAAY;gBACXyG,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAC;cAAqB;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjC,OAAA,CAACnC,IAAI;MAAC+D,EAAE,EAAE;QAAEyB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClBlD,OAAA,CAAClC,WAAW;QAAAoF,QAAA,gBACVlD,OAAA,CAACpC,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACI,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ5B,gBAAgB,CAACoF,MAAM,KAAK,CAAC,gBAC5BzF,OAAA,CAACpC,UAAU;UAAC6F,OAAO,EAAC,OAAO;UAAC5B,KAAK,EAAC,eAAe;UAAAqB,QAAA,EAAC;QAElD;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEbjC,OAAA,CAAC5B,cAAc;UAAA8E,QAAA,eACblD,OAAA,CAAC/B,KAAK;YAAAiF,QAAA,gBACJlD,OAAA,CAAC3B,SAAS;cAAA6E,QAAA,eACRlD,OAAA,CAAC1B,QAAQ;gBAAA4E,QAAA,gBACPlD,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,EAAC;gBAAO;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,EAAC;gBAAM;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,EAAC;gBAAQ;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/BjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,EAAC;gBAAO;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,EAAC;gBAAO;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZjC,OAAA,CAAC9B,SAAS;cAAAgF,QAAA,EACP7C,gBAAgB,CAACqF,GAAG,CAAEC,IAAI,iBACzB3F,OAAA,CAAC1B,QAAQ;gBAAA4E,QAAA,gBACPlD,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,eACRlD,OAAA,CAACpC,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAACmC,UAAU,EAAC,WAAW;oBAAA1C,QAAA,GAC/CyC,IAAI,CAACE,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KAC5B;kBAAA;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,eACRlD,OAAA,CAAChC,IAAI;oBACH0F,KAAK,EAAEiC,IAAI,CAAChE,MAAO;oBACnBE,KAAK,EAAEK,cAAc,CAACyD,IAAI,CAAChE,MAAM,CAAS;oBAC1CgC,IAAI,EAAC;kBAAO;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,eACRlD,OAAA,CAACrC,GAAG;oBAACiE,EAAE,EAAE;sBAAEuB,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEU,GAAG,EAAE;oBAAE,CAAE;oBAAAZ,QAAA,gBACzDlD,OAAA,CAACjC,cAAc;sBACb0F,OAAO,EAAC,aAAa;sBACrBoB,KAAK,EAAEc,IAAI,CAACI,QAAS;sBACrBnE,EAAE,EAAE;wBAAE4B,QAAQ,EAAE,CAAC;wBAAEuB,MAAM,EAAE;sBAAE;oBAAE;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACFjC,OAAA,CAACpC,UAAU;sBAAC6F,OAAO,EAAC,OAAO;sBAAAP,QAAA,GACxByC,IAAI,CAACI,QAAQ,EAAC,GACjB;oBAAA;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,gBACRlD,OAAA,CAACpC,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAAAP,QAAA,EACxByC,IAAI,CAACK;kBAAO;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACZ0D,IAAI,CAACM,aAAa,iBACjBjG,OAAA,CAACpC,UAAU;oBAAC6F,OAAO,EAAC,SAAS;oBAAC5B,KAAK,EAAC,OAAO;oBAAAqB,QAAA,EACxCyC,IAAI,CAACM;kBAAa;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZjC,OAAA,CAAC7B,SAAS;kBAAA+E,QAAA,eACRlD,OAAA,CAACpC,UAAU;oBAAC6F,OAAO,EAAC,OAAO;oBAAAP,QAAA,EACxB,IAAIrC,IAAI,CAAC8E,IAAI,CAACO,UAAU,CAAC,CAACnC,kBAAkB,CAAC;kBAAC;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAvCC0D,IAAI,CAACE,OAAO;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwCjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjC,OAAA,CAACpC,UAAU;MAAC6F,OAAO,EAAC,IAAI;MAACI,YAAY;MAAAX,QAAA,EAAC;IAEtC;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbjC,OAAA,CAACmG,IAAI;MAACC,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnD,QAAA,gBACzBlD,OAAA,CAACmG,IAAI;QAAC/B,EAAE,EAAE,EAAG;QAACkC,EAAE,EAAE,CAAE;QAACjC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACzBlD,OAAA,CAAC8C,iBAAiB;UAChBC,KAAK,EAAC,mBAAmB;UACzBpB,MAAM,EAAE,CAAAxB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgF,QAAQ,CAACC,iBAAiB,KAAI,KAAM;UAC1DpC,IAAI,eAAEhD,OAAA,CAACT,WAAW;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBgB,WAAW,EAAC;QAAoB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPjC,OAAA,CAACmG,IAAI;QAAC/B,EAAE,EAAE,EAAG;QAACkC,EAAE,EAAE,CAAE;QAACjC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACzBlD,OAAA,CAAC8C,iBAAiB;UAChBC,KAAK,EAAC,aAAa;UACnBpB,MAAM,EAAE,CAAAxB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgF,QAAQ,CAACI,WAAW,KAAI,KAAM;UACpDvC,IAAI,eAAEhD,OAAA,CAACP,YAAY;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBgB,WAAW,EAAC;QAAuB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPjC,OAAA,CAACmG,IAAI;QAAC/B,EAAE,EAAE,EAAG;QAACkC,EAAE,EAAE,CAAE;QAACjC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACzBlD,OAAA,CAAC8C,iBAAiB;UAChBC,KAAK,EAAC,aAAa;UACnBpB,MAAM,EAAE,CAAAxB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgF,QAAQ,CAACK,WAAW,KAAI,KAAM;UACpDxC,IAAI,eAAEhD,OAAA,CAACH,WAAW;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBgB,WAAW,EAAC;QAAqB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPjC,OAAA,CAACmG,IAAI;QAAC/B,EAAE,EAAE,EAAG;QAACkC,EAAE,EAAE,CAAE;QAACjC,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACzBlD,OAAA,CAAC8C,iBAAiB;UAChBC,KAAK,EAAC,UAAU;UAChBpB,MAAM,EAAE,IAAK;UACbqB,IAAI,eAAEhD,OAAA,CAACL,YAAY;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBgB,WAAW,EAAC;QAAgC;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvYID,gBAA0B;AAAAsG,EAAA,GAA1BtG,gBAA0B;AAyYhC,eAAeA,gBAAgB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}