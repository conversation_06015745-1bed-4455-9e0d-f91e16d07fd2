{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 10V2c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1v8c0 1.86 1.28 3.41 3 3.86V21h-1c-.55 0-1 .45-1 1s.45 1 1 1h4c.55 0 1-.45 1-1s-.45-1-1-1h-1v-7.14c1.72-.45 3-2 3-3.86m-2-7v3h-4V3zM9.86 9H8V8h1.86c1.31 0 2.5-.94 2.63-2.24C12.64 4.26 11.47 3 10 3H8V2c0-.55-.45-1-1-1s-1 .45-1 1v1H4.14c-1.31 0-2.5.94-2.63 2.24C1.36 6.74 2.53 8 4 8h2v1H4.14c-1.31 0-2.5.94-2.63 2.24C1.36 12.74 2.53 14 4 14h2v8c0 .55.45 1 1 1s1-.45 1-1v-8h2c1.47 0 2.64-1.26 2.49-2.76C12.36 9.94 11.17 9 9.86 9\"\n}), '<PERSON>pasRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/icons-material/esm/TapasRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 10V2c0-.55-.45-1-1-1h-6c-.55 0-1 .45-1 1v8c0 1.86 1.28 3.41 3 3.86V21h-1c-.55 0-1 .45-1 1s.45 1 1 1h4c.55 0 1-.45 1-1s-.45-1-1-1h-1v-7.14c1.72-.45 3-2 3-3.86m-2-7v3h-4V3zM9.86 9H8V8h1.86c1.31 0 2.5-.94 2.63-2.24C12.64 4.26 11.47 3 10 3H8V2c0-.55-.45-1-1-1s-1 .45-1 1v1H4.14c-1.31 0-2.5.94-2.63 2.24C1.36 6.74 2.53 8 4 8h2v1H4.14c-1.31 0-2.5.94-2.63 2.24C1.36 12.74 2.53 14 4 14h2v8c0 .55.45 1 1 1s1-.45 1-1v-8h2c1.47 0 2.64-1.26 2.49-2.76C12.36 9.94 11.17 9 9.86 9\"\n}), '<PERSON>pasRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}