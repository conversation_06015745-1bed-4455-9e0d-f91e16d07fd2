{"ast": null, "code": "import utils from './../utils.js';\nimport platform from '../platform/index.js';\nexport default platform.hasStandardBrowserEnv ?\n// Standard browser envs support document.cookie\n{\n  write(name, value, expires, path, domain, secure) {\n    const cookie = [name + '=' + encodeURIComponent(value)];\n    utils.isNumber(expires) && cookie.push('expires=' + new Date(expires).toGMTString());\n    utils.isString(path) && cookie.push('path=' + path);\n    utils.isString(domain) && cookie.push('domain=' + domain);\n    secure === true && cookie.push('secure');\n    document.cookie = cookie.join('; ');\n  },\n  read(name) {\n    const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n    return match ? decodeURIComponent(match[3]) : null;\n  },\n  remove(name) {\n    this.write(name, '', Date.now() - 86400000);\n  }\n} :\n// Non-standard browser env (web workers, react-native) lack needed support.\n{\n  write() {},\n  read() {\n    return null;\n  },\n  remove() {}\n};", "map": {"version": 3, "names": ["utils", "platform", "hasStandardBrowserEnv", "write", "name", "value", "expires", "path", "domain", "secure", "cookie", "encodeURIComponent", "isNumber", "push", "Date", "toGMTString", "isString", "document", "join", "read", "match", "RegExp", "decodeURIComponent", "remove", "now"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/axios/lib/helpers/cookies.js"], "sourcesContent": ["import utils from './../utils.js';\nimport platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ?\n\n  // Standard browser envs support document.cookie\n  {\n    write(name, value, expires, path, domain, secure) {\n      const cookie = [name + '=' + encodeURIComponent(value)];\n\n      utils.isNumber(expires) && cookie.push('expires=' + new Date(expires).toGMTString());\n\n      utils.isString(path) && cookie.push('path=' + path);\n\n      utils.isString(domain) && cookie.push('domain=' + domain);\n\n      secure === true && cookie.push('secure');\n\n      document.cookie = cookie.join('; ');\n    },\n\n    read(name) {\n      const match = document.cookie.match(new RegExp('(^|;\\\\s*)(' + name + ')=([^;]*)'));\n      return (match ? decodeURIComponent(match[3]) : null);\n    },\n\n    remove(name) {\n      this.write(name, '', Date.now() - 86400000);\n    }\n  }\n\n  :\n\n  // Non-standard browser env (web workers, react-native) lack needed support.\n  {\n    write() {},\n    read() {\n      return null;\n    },\n    remove() {}\n  };\n\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,sBAAsB;AAE3C,eAAeA,QAAQ,CAACC,qBAAqB;AAE3C;AACA;EACEC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAChD,MAAMC,MAAM,GAAG,CAACN,IAAI,GAAG,GAAG,GAAGO,kBAAkB,CAACN,KAAK,CAAC,CAAC;IAEvDL,KAAK,CAACY,QAAQ,CAACN,OAAO,CAAC,IAAII,MAAM,CAACG,IAAI,CAAC,UAAU,GAAG,IAAIC,IAAI,CAACR,OAAO,CAAC,CAACS,WAAW,CAAC,CAAC,CAAC;IAEpFf,KAAK,CAACgB,QAAQ,CAACT,IAAI,CAAC,IAAIG,MAAM,CAACG,IAAI,CAAC,OAAO,GAAGN,IAAI,CAAC;IAEnDP,KAAK,CAACgB,QAAQ,CAACR,MAAM,CAAC,IAAIE,MAAM,CAACG,IAAI,CAAC,SAAS,GAAGL,MAAM,CAAC;IAEzDC,MAAM,KAAK,IAAI,IAAIC,MAAM,CAACG,IAAI,CAAC,QAAQ,CAAC;IAExCI,QAAQ,CAACP,MAAM,GAAGA,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC;EACrC,CAAC;EAEDC,IAAIA,CAACf,IAAI,EAAE;IACT,MAAMgB,KAAK,GAAGH,QAAQ,CAACP,MAAM,CAACU,KAAK,CAAC,IAAIC,MAAM,CAAC,YAAY,GAAGjB,IAAI,GAAG,WAAW,CAAC,CAAC;IAClF,OAAQgB,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EACrD,CAAC;EAEDG,MAAMA,CAACnB,IAAI,EAAE;IACX,IAAI,CAACD,KAAK,CAACC,IAAI,EAAE,EAAE,EAAEU,IAAI,CAACU,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;EAC7C;AACF,CAAC;AAID;AACA;EACErB,KAAKA,CAAA,EAAG,CAAC,CAAC;EACVgB,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI;EACb,CAAC;EACDI,MAAMA,CAAA,EAAG,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}