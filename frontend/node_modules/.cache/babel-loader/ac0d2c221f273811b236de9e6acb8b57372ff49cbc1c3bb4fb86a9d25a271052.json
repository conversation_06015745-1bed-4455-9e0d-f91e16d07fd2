{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Authentication context for managing user authentication state\n */\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null\n};\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.access_token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...initialState\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    case 'SET_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    default:\n      return state;\n  }\n};\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          authService.setToken(token);\n\n          // Verify token is still valid by fetching user profile\n          const currentUser = await authService.getCurrentUser();\n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: {\n              access_token: token,\n              token_type: 'bearer',\n              expires_in: 0,\n              // We don't track expiry on frontend\n              user: currentUser\n            }\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          authService.clearToken();\n        }\n      }\n    };\n    initializeAuth();\n  }, []);\n  const login = async credentials => {\n    dispatch({\n      type: 'AUTH_START'\n    });\n    try {\n      const response = await authService.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n\n      // Set token in service\n      authService.setToken(response.access_token);\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: response\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message || 'Login failed';\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n  const signup = async userData => {\n    dispatch({\n      type: 'AUTH_START'\n    });\n    try {\n      await authService.signup(userData);\n      // After successful signup, automatically log in\n      await login({\n        email: userData.email,\n        password: userData.password\n      });\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message || 'Signup failed';\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Clear token in service\n    authService.clearToken();\n    dispatch({\n      type: 'AUTH_LOGOUT'\n    });\n  };\n  const clearError = () => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  };\n  const refreshUser = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      dispatch({\n        type: 'SET_USER',\n        payload: user\n      });\n      localStorage.setItem('user', JSON.stringify(user));\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n    }\n  };\n  const value = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    refreshUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 10\n  }, this);\n};\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "access_token", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "localStorage", "getItem", "userStr", "setToken", "currentUser", "getCurrentUser", "token_type", "expires_in", "removeItem", "clearToken", "login", "credentials", "response", "setItem", "JSON", "stringify", "_error$response", "_error$response$data", "errorMessage", "data", "detail", "message", "signup", "userData", "email", "password", "_error$response2", "_error$response2$data", "logout", "clearError", "refreshUser", "console", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Authentication context for managing user authentication state\n */\nimport React, { create<PERSON>ontext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, AuthResponse, LoginRequest, SignupRequest } from '../types';\nimport { authService } from '../services/authService';\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginRequest) => Promise<void>;\n  signup: (userData: SignupRequest) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  refreshUser: () => Promise<void>;\n}\n\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: AuthResponse }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'C<PERSON>AR_ERROR' }\n  | { type: 'SET_USER'; payload: User };\n\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.access_token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...initialState,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    case 'SET_USER':\n      return {\n        ...state,\n        user: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n\n      if (token && userStr) {\n        try {\n          authService.setToken(token);\n\n          // Verify token is still valid by fetching user profile\n          const currentUser = await authService.getCurrentUser();\n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: {\n              access_token: token,\n              token_type: 'bearer',\n              expires_in: 0, // We don't track expiry on frontend\n              user: currentUser,\n            },\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          authService.clearToken();\n        }\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (credentials: LoginRequest) => {\n    dispatch({ type: 'AUTH_START' });\n    try {\n      const response = await authService.login(credentials);\n      \n      // Store in localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      // Set token in service\n      authService.setToken(response.access_token);\n      \n      dispatch({ type: 'AUTH_SUCCESS', payload: response });\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Login failed';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  const signup = async (userData: SignupRequest) => {\n    dispatch({ type: 'AUTH_START' });\n    try {\n      await authService.signup(userData);\n      // After successful signup, automatically log in\n      await login({ email: userData.email, password: userData.password });\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Signup failed';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    \n    // Clear token in service\n    authService.clearToken();\n    \n    dispatch({ type: 'AUTH_LOGOUT' });\n  };\n\n  const clearError = () => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  const refreshUser = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      dispatch({ type: 'SET_USER', payload: user });\n      localStorage.setItem('user', JSON.stringify(user));\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n    }\n  };\n\n  const value: AuthContextType = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    refreshUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BtD,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE,OAAO,CAACT,IAAI;QACzBC,KAAK,EAAEM,MAAM,CAACE,OAAO,CAACC,YAAY;QAClCR,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGV;MACL,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGO,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH,KAAK,UAAU;MACb,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE;MACf,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;AAED,MAAMK,WAAW,gBAAGnB,aAAa,CAA8BoB,SAAS,CAAC;AAMzE,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACT,KAAK,EAAEU,QAAQ,CAAC,GAAGtB,UAAU,CAACW,WAAW,EAAEN,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMsB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMhB,KAAK,GAAGiB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIlB,KAAK,IAAImB,OAAO,EAAE;QACpB,IAAI;UACFxB,WAAW,CAACyB,QAAQ,CAACpB,KAAK,CAAC;;UAE3B;UACA,MAAMqB,WAAW,GAAG,MAAM1B,WAAW,CAAC2B,cAAc,CAAC,CAAC;UACtDP,QAAQ,CAAC;YACPR,IAAI,EAAE,cAAc;YACpBC,OAAO,EAAE;cACPC,YAAY,EAAET,KAAK;cACnBuB,UAAU,EAAE,QAAQ;cACpBC,UAAU,EAAE,CAAC;cAAE;cACfzB,IAAI,EAAEsB;YACR;UACF,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACd;UACAc,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;UAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;UAC/B9B,WAAW,CAAC+B,UAAU,CAAC,CAAC;QAC1B;MACF;IACF,CAAC;IAEDV,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,KAAK,GAAG,MAAOC,WAAyB,IAAK;IACjDb,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAa,CAAC,CAAC;IAChC,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMlC,WAAW,CAACgC,KAAK,CAACC,WAAW,CAAC;;MAErD;MACAX,YAAY,CAACa,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACpB,YAAY,CAAC;MACpDQ,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC9B,IAAI,CAAC,CAAC;;MAE3D;MACAJ,WAAW,CAACyB,QAAQ,CAACS,QAAQ,CAACpB,YAAY,CAAC;MAE3CM,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEqB;MAAS,CAAC,CAAC;IACvD,CAAC,CAAC,OAAO1B,KAAU,EAAE;MAAA,IAAA8B,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAA9B,KAAK,CAAC0B,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,KAAIlC,KAAK,CAACmC,OAAO,IAAI,cAAc;MACpFvB,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAE2B;MAAa,CAAC,CAAC;MACzD,MAAMhC,KAAK;IACb;EACF,CAAC;EAED,MAAMoC,MAAM,GAAG,MAAOC,QAAuB,IAAK;IAChDzB,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAa,CAAC,CAAC;IAChC,IAAI;MACF,MAAMZ,WAAW,CAAC4C,MAAM,CAACC,QAAQ,CAAC;MAClC;MACA,MAAMb,KAAK,CAAC;QAAEc,KAAK,EAAED,QAAQ,CAACC,KAAK;QAAEC,QAAQ,EAAEF,QAAQ,CAACE;MAAS,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOvC,KAAU,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACnB,MAAMT,YAAY,GAAG,EAAAQ,gBAAA,GAAAxC,KAAK,CAAC0B,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAIlC,KAAK,CAACmC,OAAO,IAAI,eAAe;MACrFvB,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAE2B;MAAa,CAAC,CAAC;MACzD,MAAMhC,KAAK;IACb;EACF,CAAC;EAED,MAAM0C,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA5B,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA9B,WAAW,CAAC+B,UAAU,CAAC,CAAC;IAExBX,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;EAED,MAAMuC,UAAU,GAAGA,CAAA,KAAM;IACvB/B,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;EAED,MAAMwC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMhD,IAAI,GAAG,MAAMJ,WAAW,CAAC2B,cAAc,CAAC,CAAC;MAC/CP,QAAQ,CAAC;QAAER,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAET;MAAK,CAAC,CAAC;MAC7CkB,YAAY,CAACa,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACjC,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAM8C,KAAsB,GAAG;IAC7B,GAAG5C,KAAK;IACRsB,KAAK;IACLY,MAAM;IACNM,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EAED,oBAAOlD,OAAA,CAACa,WAAW,CAACwC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApC,QAAA,EAAEA;EAAQ;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACxC,EAAA,CAxGWF,YAAyC;AAAA2C,EAAA,GAAzC3C,YAAyC;AA0GtD,OAAO,MAAM4C,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGlE,UAAU,CAACkB,WAAW,CAAC;EACvC,IAAIgD,OAAO,KAAK/C,SAAS,EAAE;IACzB,MAAM,IAAIgD,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}