{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid2 as Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, FormControl, InputLabel, Select, MenuItem, LinearProgress } from '@mui/material';\nimport { TrendingUp, Search as SearchIcon, QuestionAnswer as QAIcon, Book as BookIcon, People as PeopleIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Analytics = () => {\n  _s();\n  const [period, setPeriod] = useState('week');\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState(null);\n  useEffect(() => {\n    // Simulate loading analytics data\n    setLoading(true);\n    setTimeout(() => {\n      setData({\n        period,\n        totalSearches: 3421,\n        totalQuestions: 892,\n        totalUsers: 1247,\n        totalBooks: 156,\n        searchTrend: 12.5,\n        questionTrend: 8.3,\n        userTrend: 15.2,\n        popularBooks: [{\n          title: \"Gray's Anatomy\",\n          authors: \"Henry Gray\",\n          searchCount: 245\n        }, {\n          title: \"Harrison's Principles\",\n          authors: \"Dennis Kasper\",\n          searchCount: 198\n        }, {\n          title: \"Robbins Basic Pathology\",\n          authors: \"Vinay Kumar\",\n          searchCount: 167\n        }, {\n          title: \"Netter's Atlas\",\n          authors: \"Frank Netter\",\n          searchCount: 134\n        }, {\n          title: \"First Aid USMLE\",\n          authors: \"Tao Le\",\n          searchCount: 112\n        }],\n        popularQueries: [{\n          query: \"cardiac anatomy\",\n          count: 89\n        }, {\n          query: \"diabetes pathophysiology\",\n          count: 76\n        }, {\n          query: \"respiratory system\",\n          count: 65\n        }, {\n          query: \"neurological disorders\",\n          count: 54\n        }, {\n          query: \"pharmacology basics\",\n          count: 43\n        }],\n        userActivity: [{\n          date: \"2024-01-01\",\n          searches: 45,\n          questions: 12\n        }, {\n          date: \"2024-01-02\",\n          searches: 52,\n          questions: 15\n        }, {\n          date: \"2024-01-03\",\n          searches: 38,\n          questions: 9\n        }, {\n          date: \"2024-01-04\",\n          searches: 61,\n          questions: 18\n        }, {\n          date: \"2024-01-05\",\n          searches: 47,\n          questions: 13\n        }]\n      });\n      setLoading(false);\n    }, 1000);\n  }, [period]);\n  const MetricCard = ({\n    title,\n    value,\n    trend,\n    icon,\n    color\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: color,\n            borderRadius: '50%',\n            p: 1,\n            mr: 2,\n            color: 'white'\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"textSecondary\",\n            gutterBottom: true,\n            variant: \"body2\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"div\",\n            fontWeight: \"bold\",\n            children: value.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n          sx: {\n            color: 'success.main',\n            mr: 0.5,\n            fontSize: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'success.main',\n            fontWeight: 'medium'\n          },\n          children: [\"+\", trend, \"% from last \", period]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        sx: {\n          minWidth: 120\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Period\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: period,\n          label: \"Period\",\n          onChange: e => setPeriod(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"day\",\n            children: \"Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"week\",\n            children: \"This Week\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"month\",\n            children: \"This Month\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"year\",\n            children: \"This Year\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Searches\",\n          value: (data === null || data === void 0 ? void 0 : data.totalSearches) || 0,\n          trend: (data === null || data === void 0 ? void 0 : data.searchTrend) || 0,\n          icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 19\n          }, this),\n          color: \"#1976d2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Q&A Sessions\",\n          value: (data === null || data === void 0 ? void 0 : data.totalQuestions) || 0,\n          trend: (data === null || data === void 0 ? void 0 : data.questionTrend) || 0,\n          icon: /*#__PURE__*/_jsxDEV(QAIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 19\n          }, this),\n          color: \"#388e3c\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Active Users\",\n          value: (data === null || data === void 0 ? void 0 : data.totalUsers) || 0,\n          trend: (data === null || data === void 0 ? void 0 : data.userTrend) || 0,\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 19\n          }, this),\n          color: \"#f57c00\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Total Books\",\n          value: (data === null || data === void 0 ? void 0 : data.totalBooks) || 0,\n          trend: 5.2,\n          icon: /*#__PURE__*/_jsxDEV(BookIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 19\n          }, this),\n          color: \"#7b1fa2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Most Searched Books\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Book\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Searches\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: data === null || data === void 0 ? void 0 : data.popularBooks.map((book, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"medium\",\n                        children: book.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"textSecondary\",\n                        children: book.authors\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 238,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: book.searchCount,\n                        size: \"small\",\n                        color: \"primary\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Popular Search Queries\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Query\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: \"Count\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: data === null || data === void 0 ? void 0 : data.popularQueries.map((query, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"medium\",\n                        children: [\"\\\"\", query.query, \"\\\"\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: query.count,\n                        size: \"small\",\n                        color: \"secondary\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 282,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(Analytics, \"l0n49m3L9bWQ4n2W4SwiZf04CtM=\");\n_c = Analytics;\nexport default Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid2", "Grid", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "LinearProgress", "TrendingUp", "Search", "SearchIcon", "QuestionAnswer", "QAIcon", "Book", "BookIcon", "People", "PeopleIcon", "jsxDEV", "_jsxDEV", "Analytics", "_s", "period", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "data", "setData", "setTimeout", "totalSearches", "totalQuestions", "totalUsers", "totalBooks", "searchTrend", "questionTrend", "userTrend", "popularBooks", "title", "authors", "searchCount", "popularQueries", "query", "count", "userActivity", "date", "searches", "questions", "MetricCard", "value", "trend", "icon", "color", "children", "sx", "display", "alignItems", "mb", "backgroundColor", "borderRadius", "p", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexGrow", "gutterBottom", "variant", "component", "fontWeight", "toLocaleString", "fontSize", "justifyContent", "min<PERSON><PERSON><PERSON>", "label", "onChange", "e", "target", "container", "spacing", "xs", "sm", "md", "size", "align", "map", "book", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/Analytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Typo<PERSON>,\n  Card,\n  CardContent,\n  Grid2 as Grid,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  Search as SearchIcon,\n  QuestionAnswer as QAIcon,\n  Book as BookIcon,\n  People as PeopleIcon,\n} from '@mui/icons-material';\n\ninterface AnalyticsData {\n  period: string;\n  totalSearches: number;\n  totalQuestions: number;\n  totalUsers: number;\n  totalBooks: number;\n  searchTrend: number;\n  questionTrend: number;\n  userTrend: number;\n  popularBooks: Array<{\n    title: string;\n    authors: string;\n    searchCount: number;\n  }>;\n  popularQueries: Array<{\n    query: string;\n    count: number;\n  }>;\n  userActivity: Array<{\n    date: string;\n    searches: number;\n    questions: number;\n  }>;\n}\n\nconst Analytics: React.FC = () => {\n  const [period, setPeriod] = useState('week');\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState<AnalyticsData | null>(null);\n\n  useEffect(() => {\n    // Simulate loading analytics data\n    setLoading(true);\n    setTimeout(() => {\n      setData({\n        period,\n        totalSearches: 3421,\n        totalQuestions: 892,\n        totalUsers: 1247,\n        totalBooks: 156,\n        searchTrend: 12.5,\n        questionTrend: 8.3,\n        userTrend: 15.2,\n        popularBooks: [\n          { title: \"Gray's Anatomy\", authors: \"Henry Gray\", searchCount: 245 },\n          { title: \"Harrison's Principles\", authors: \"Dennis Kasper\", searchCount: 198 },\n          { title: \"Robbins Basic Pathology\", authors: \"Vinay Kumar\", searchCount: 167 },\n          { title: \"Netter's Atlas\", authors: \"Frank Netter\", searchCount: 134 },\n          { title: \"First Aid USMLE\", authors: \"Tao Le\", searchCount: 112 },\n        ],\n        popularQueries: [\n          { query: \"cardiac anatomy\", count: 89 },\n          { query: \"diabetes pathophysiology\", count: 76 },\n          { query: \"respiratory system\", count: 65 },\n          { query: \"neurological disorders\", count: 54 },\n          { query: \"pharmacology basics\", count: 43 },\n        ],\n        userActivity: [\n          { date: \"2024-01-01\", searches: 45, questions: 12 },\n          { date: \"2024-01-02\", searches: 52, questions: 15 },\n          { date: \"2024-01-03\", searches: 38, questions: 9 },\n          { date: \"2024-01-04\", searches: 61, questions: 18 },\n          { date: \"2024-01-05\", searches: 47, questions: 13 },\n        ],\n      });\n      setLoading(false);\n    }, 1000);\n  }, [period]);\n\n  const MetricCard: React.FC<{\n    title: string;\n    value: number;\n    trend: number;\n    icon: React.ReactNode;\n    color: string;\n  }> = ({ title, value, trend, icon, color }) => (\n    <Card>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <Box\n            sx={{\n              backgroundColor: color,\n              borderRadius: '50%',\n              p: 1,\n              mr: 2,\n              color: 'white',\n            }}\n          >\n            {icon}\n          </Box>\n          <Box sx={{ flexGrow: 1 }}>\n            <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n              {title}\n            </Typography>\n            <Typography variant=\"h4\" component=\"div\" fontWeight=\"bold\">\n              {value.toLocaleString()}\n            </Typography>\n          </Box>\n        </Box>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <TrendingUp sx={{ color: 'success.main', mr: 0.5, fontSize: 16 }} />\n          <Typography\n            variant=\"body2\"\n            sx={{ color: 'success.main', fontWeight: 'medium' }}\n          >\n            +{trend}% from last {period}\n          </Typography>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n\n  if (loading) {\n    return (\n      <Box>\n        <Typography variant=\"h4\" gutterBottom>\n          Analytics\n        </Typography>\n        <LinearProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          Analytics Dashboard\n        </Typography>\n        <FormControl sx={{ minWidth: 120 }}>\n          <InputLabel>Period</InputLabel>\n          <Select\n            value={period}\n            label=\"Period\"\n            onChange={(e) => setPeriod(e.target.value)}\n          >\n            <MenuItem value=\"day\">Today</MenuItem>\n            <MenuItem value=\"week\">This Week</MenuItem>\n            <MenuItem value=\"month\">This Month</MenuItem>\n            <MenuItem value=\"year\">This Year</MenuItem>\n          </Select>\n        </FormControl>\n      </Box>\n\n      {/* Metrics Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Searches\"\n            value={data?.totalSearches || 0}\n            trend={data?.searchTrend || 0}\n            icon={<SearchIcon />}\n            color=\"#1976d2\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Q&A Sessions\"\n            value={data?.totalQuestions || 0}\n            trend={data?.questionTrend || 0}\n            icon={<QAIcon />}\n            color=\"#388e3c\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Active Users\"\n            value={data?.totalUsers || 0}\n            trend={data?.userTrend || 0}\n            icon={<PeopleIcon />}\n            color=\"#f57c00\"\n          />\n        </Grid>\n        <Grid xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Total Books\"\n            value={data?.totalBooks || 0}\n            trend={5.2}\n            icon={<BookIcon />}\n            color=\"#7b1fa2\"\n          />\n        </Grid>\n      </Grid>\n\n      {/* Popular Content */}\n      <Grid container spacing={3}>\n        <Grid xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Most Searched Books\n              </Typography>\n              <TableContainer>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Book</TableCell>\n                      <TableCell align=\"right\">Searches</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {data?.popularBooks.map((book, index) => (\n                      <TableRow key={index}>\n                        <TableCell>\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            {book.title}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"textSecondary\">\n                            {book.authors}\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Chip\n                            label={book.searchCount}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                          />\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Popular Search Queries\n              </Typography>\n              <TableContainer>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Query</TableCell>\n                      <TableCell align=\"right\">Count</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {data?.popularQueries.map((query, index) => (\n                      <TableRow key={index}>\n                        <TableCell>\n                          <Typography variant=\"body2\" fontWeight=\"medium\">\n                            \"{query.query}\"\n                          </Typography>\n                        </TableCell>\n                        <TableCell align=\"right\">\n                          <Chip\n                            label={query.count}\n                            size=\"small\"\n                            color=\"secondary\"\n                            variant=\"outlined\"\n                          />\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Analytics;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,KAAK,IAAIC,IAAI,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EAERC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,EACVC,MAAM,IAAIC,UAAU,EACpBC,cAAc,IAAIC,MAAM,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA2B7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAuB,IAAI,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACd;IACAmC,UAAU,CAAC,IAAI,CAAC;IAChBG,UAAU,CAAC,MAAM;MACfD,OAAO,CAAC;QACNL,MAAM;QACNO,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,GAAG;QACnBC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,GAAG;QACfC,WAAW,EAAE,IAAI;QACjBC,aAAa,EAAE,GAAG;QAClBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,CACZ;UAAEC,KAAK,EAAE,gBAAgB;UAAEC,OAAO,EAAE,YAAY;UAAEC,WAAW,EAAE;QAAI,CAAC,EACpE;UAAEF,KAAK,EAAE,uBAAuB;UAAEC,OAAO,EAAE,eAAe;UAAEC,WAAW,EAAE;QAAI,CAAC,EAC9E;UAAEF,KAAK,EAAE,yBAAyB;UAAEC,OAAO,EAAE,aAAa;UAAEC,WAAW,EAAE;QAAI,CAAC,EAC9E;UAAEF,KAAK,EAAE,gBAAgB;UAAEC,OAAO,EAAE,cAAc;UAAEC,WAAW,EAAE;QAAI,CAAC,EACtE;UAAEF,KAAK,EAAE,iBAAiB;UAAEC,OAAO,EAAE,QAAQ;UAAEC,WAAW,EAAE;QAAI,CAAC,CAClE;QACDC,cAAc,EAAE,CACd;UAAEC,KAAK,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAG,CAAC,EACvC;UAAED,KAAK,EAAE,0BAA0B;UAAEC,KAAK,EAAE;QAAG,CAAC,EAChD;UAAED,KAAK,EAAE,oBAAoB;UAAEC,KAAK,EAAE;QAAG,CAAC,EAC1C;UAAED,KAAK,EAAE,wBAAwB;UAAEC,KAAK,EAAE;QAAG,CAAC,EAC9C;UAAED,KAAK,EAAE,qBAAqB;UAAEC,KAAK,EAAE;QAAG,CAAC,CAC5C;QACDC,YAAY,EAAE,CACZ;UAAEC,IAAI,EAAE,YAAY;UAAEC,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAC,EACnD;UAAEF,IAAI,EAAE,YAAY;UAAEC,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAC,EACnD;UAAEF,IAAI,EAAE,YAAY;UAAEC,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAE,CAAC,EAClD;UAAEF,IAAI,EAAE,YAAY;UAAEC,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAC,EACnD;UAAEF,IAAI,EAAE,YAAY;UAAEC,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAC;MAEvD,CAAC,CAAC;MACFrB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EAEZ,MAAMyB,UAMJ,GAAGA,CAAC;IAAEV,KAAK;IAAEW,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAM,CAAC,kBACxChC,OAAA,CAAC1B,IAAI;IAAA2D,QAAA,eACHjC,OAAA,CAACzB,WAAW;MAAA0D,QAAA,gBACVjC,OAAA,CAAC5B,GAAG;QAAC8D,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACxDjC,OAAA,CAAC5B,GAAG;UACF8D,EAAE,EAAE;YACFI,eAAe,EAAEN,KAAK;YACtBO,YAAY,EAAE,KAAK;YACnBC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLT,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAEDF;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN7C,OAAA,CAAC5B,GAAG;UAAC8D,EAAE,EAAE;YAAEY,QAAQ,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACvBjC,OAAA,CAAC3B,UAAU;YAAC2D,KAAK,EAAC,eAAe;YAACe,YAAY;YAACC,OAAO,EAAC,OAAO;YAAAf,QAAA,EAC3Df;UAAK;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACb7C,OAAA,CAAC3B,UAAU;YAAC2E,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,KAAK;YAACC,UAAU,EAAC,MAAM;YAAAjB,QAAA,EACvDJ,KAAK,CAACsB,cAAc,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN7C,OAAA,CAAC5B,GAAG;QAAC8D,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACjDjC,OAAA,CAACV,UAAU;UAAC4C,EAAE,EAAE;YAAEF,KAAK,EAAE,cAAc;YAAES,EAAE,EAAE,GAAG;YAAEW,QAAQ,EAAE;UAAG;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpE7C,OAAA,CAAC3B,UAAU;UACT2E,OAAO,EAAC,OAAO;UACfd,EAAE,EAAE;YAAEF,KAAK,EAAE,cAAc;YAAEkB,UAAU,EAAE;UAAS,CAAE;UAAAjB,QAAA,GACrD,GACE,EAACH,KAAK,EAAC,cAAY,EAAC3B,MAAM;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,IAAIxC,OAAO,EAAE;IACX,oBACEL,OAAA,CAAC5B,GAAG;MAAA6D,QAAA,gBACFjC,OAAA,CAAC3B,UAAU;QAAC2E,OAAO,EAAC,IAAI;QAACD,YAAY;QAAAd,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7C,OAAA,CAACX,cAAc;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,oBACE7C,OAAA,CAAC5B,GAAG;IAAA6D,QAAA,gBAEFjC,OAAA,CAAC5B,GAAG;MAAC8D,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEkB,cAAc,EAAE,eAAe;QAAEjB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFjC,OAAA,CAAC3B,UAAU;QAAC2E,OAAO,EAAC,IAAI;QAACD,YAAY;QAAAd,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7C,OAAA,CAACf,WAAW;QAACiD,EAAE,EAAE;UAAEoB,QAAQ,EAAE;QAAI,CAAE;QAAArB,QAAA,gBACjCjC,OAAA,CAACd,UAAU;UAAA+C,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC/B7C,OAAA,CAACb,MAAM;UACL0C,KAAK,EAAE1B,MAAO;UACdoD,KAAK,EAAC,QAAQ;UACdC,QAAQ,EAAGC,CAAC,IAAKrD,SAAS,CAACqD,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;UAAAI,QAAA,gBAE3CjC,OAAA,CAACZ,QAAQ;YAACyC,KAAK,EAAC,KAAK;YAAAI,QAAA,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtC7C,OAAA,CAACZ,QAAQ;YAACyC,KAAK,EAAC,MAAM;YAAAI,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC3C7C,OAAA,CAACZ,QAAQ;YAACyC,KAAK,EAAC,OAAO;YAAAI,QAAA,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC7C7C,OAAA,CAACZ,QAAQ;YAACyC,KAAK,EAAC,MAAM;YAAAI,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGN7C,OAAA,CAACvB,IAAI;MAACkF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC1B,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCjC,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACzBjC,OAAA,CAAC4B,UAAU;UACTV,KAAK,EAAC,gBAAgB;UACtBW,KAAK,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,aAAa,KAAI,CAAE;UAChCoB,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,WAAW,KAAI,CAAE;UAC9BiB,IAAI,eAAE/B,OAAA,CAACR,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7C,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACzBjC,OAAA,CAAC4B,UAAU;UACTV,KAAK,EAAC,cAAc;UACpBW,KAAK,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,cAAc,KAAI,CAAE;UACjCmB,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,aAAa,KAAI,CAAE;UAChCgB,IAAI,eAAE/B,OAAA,CAACN,MAAM;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjBb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7C,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACzBjC,OAAA,CAAC4B,UAAU;UACTV,KAAK,EAAC,cAAc;UACpBW,KAAK,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,UAAU,KAAI,CAAE;UAC7BkB,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,SAAS,KAAI,CAAE;UAC5Be,IAAI,eAAE/B,OAAA,CAACF,UAAU;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP7C,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACzBjC,OAAA,CAAC4B,UAAU;UACTV,KAAK,EAAC,aAAa;UACnBW,KAAK,EAAE,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,UAAU,KAAI,CAAE;UAC7BiB,KAAK,EAAE,GAAI;UACXC,IAAI,eAAE/B,OAAA,CAACJ,QAAQ;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP7C,OAAA,CAACvB,IAAI;MAACkF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3B,QAAA,gBACzBjC,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAClBjC,OAAA,CAAC1B,IAAI;UAAA2D,QAAA,eACHjC,OAAA,CAACzB,WAAW;YAAA0D,QAAA,gBACVjC,OAAA,CAAC3B,UAAU;cAAC2E,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7C,OAAA,CAACnB,cAAc;cAAAoD,QAAA,eACbjC,OAAA,CAACtB,KAAK;gBAACsF,IAAI,EAAC,OAAO;gBAAA/B,QAAA,gBACjBjC,OAAA,CAAClB,SAAS;kBAAAmD,QAAA,eACRjC,OAAA,CAACjB,QAAQ;oBAAAkD,QAAA,gBACPjC,OAAA,CAACpB,SAAS;sBAAAqD,QAAA,EAAC;oBAAI;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3B7C,OAAA,CAACpB,SAAS;sBAACqF,KAAK,EAAC,OAAO;sBAAAhC,QAAA,EAAC;oBAAQ;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ7C,OAAA,CAACrB,SAAS;kBAAAsD,QAAA,EACP1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,YAAY,CAACiD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClCpE,OAAA,CAACjB,QAAQ;oBAAAkD,QAAA,gBACPjC,OAAA,CAACpB,SAAS;sBAAAqD,QAAA,gBACRjC,OAAA,CAAC3B,UAAU;wBAAC2E,OAAO,EAAC,OAAO;wBAACE,UAAU,EAAC,QAAQ;wBAAAjB,QAAA,EAC5CkC,IAAI,CAACjD;sBAAK;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACb7C,OAAA,CAAC3B,UAAU;wBAAC2E,OAAO,EAAC,SAAS;wBAAChB,KAAK,EAAC,eAAe;wBAAAC,QAAA,EAChDkC,IAAI,CAAChD;sBAAO;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ7C,OAAA,CAACpB,SAAS;sBAACqF,KAAK,EAAC,OAAO;sBAAAhC,QAAA,eACtBjC,OAAA,CAAChB,IAAI;wBACHuE,KAAK,EAAEY,IAAI,CAAC/C,WAAY;wBACxB4C,IAAI,EAAC,OAAO;wBACZhC,KAAK,EAAC,SAAS;wBACfgB,OAAO,EAAC;sBAAU;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA,GAhBCuB,KAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiBV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP7C,OAAA,CAACvB,IAAI;QAACoF,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA9B,QAAA,eAClBjC,OAAA,CAAC1B,IAAI;UAAA2D,QAAA,eACHjC,OAAA,CAACzB,WAAW;YAAA0D,QAAA,gBACVjC,OAAA,CAAC3B,UAAU;cAAC2E,OAAO,EAAC,IAAI;cAACD,YAAY;cAAAd,QAAA,EAAC;YAEtC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7C,OAAA,CAACnB,cAAc;cAAAoD,QAAA,eACbjC,OAAA,CAACtB,KAAK;gBAACsF,IAAI,EAAC,OAAO;gBAAA/B,QAAA,gBACjBjC,OAAA,CAAClB,SAAS;kBAAAmD,QAAA,eACRjC,OAAA,CAACjB,QAAQ;oBAAAkD,QAAA,gBACPjC,OAAA,CAACpB,SAAS;sBAAAqD,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5B7C,OAAA,CAACpB,SAAS;sBAACqF,KAAK,EAAC,OAAO;sBAAAhC,QAAA,EAAC;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ7C,OAAA,CAACrB,SAAS;kBAAAsD,QAAA,EACP1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,cAAc,CAAC6C,GAAG,CAAC,CAAC5C,KAAK,EAAE8C,KAAK,kBACrCpE,OAAA,CAACjB,QAAQ;oBAAAkD,QAAA,gBACPjC,OAAA,CAACpB,SAAS;sBAAAqD,QAAA,eACRjC,OAAA,CAAC3B,UAAU;wBAAC2E,OAAO,EAAC,OAAO;wBAACE,UAAU,EAAC,QAAQ;wBAAAjB,QAAA,GAAC,IAC7C,EAACX,KAAK,CAACA,KAAK,EAAC,IAChB;sBAAA;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZ7C,OAAA,CAACpB,SAAS;sBAACqF,KAAK,EAAC,OAAO;sBAAAhC,QAAA,eACtBjC,OAAA,CAAChB,IAAI;wBACHuE,KAAK,EAAEjC,KAAK,CAACC,KAAM;wBACnByC,IAAI,EAAC,OAAO;wBACZhC,KAAK,EAAC,WAAW;wBACjBgB,OAAO,EAAC;sBAAU;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA,GAbCuB,KAAK;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAcV,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3C,EAAA,CArPID,SAAmB;AAAAoE,EAAA,GAAnBpE,SAAmB;AAuPzB,eAAeA,SAAS;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}