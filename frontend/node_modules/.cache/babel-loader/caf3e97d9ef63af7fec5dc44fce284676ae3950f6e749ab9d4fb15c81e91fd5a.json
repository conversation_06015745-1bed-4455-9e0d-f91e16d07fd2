{"ast": null, "code": "/* eslint-disable import/prefer-default-export */\nexport function createGetColorSchemeSelector(selector) {\n  return function getColorSchemeSelector(colorScheme) {\n    if (selector === 'media') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (colorScheme !== 'light' && colorScheme !== 'dark') {\n          console.error(`MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '${colorScheme}'.`);\n        }\n      }\n      return `@media (prefers-color-scheme: ${colorScheme})`;\n    }\n    if (selector) {\n      if (selector.startsWith('data-') && !selector.includes('%s')) {\n        return `[${selector}=\"${colorScheme}\"] &`;\n      }\n      if (selector === 'class') {\n        return `.${colorScheme} &`;\n      }\n      if (selector === 'data') {\n        return `[data-${colorScheme}] &`;\n      }\n      return `${selector.replace('%s', colorScheme)} &`;\n    }\n    return '&';\n  };\n}", "map": {"version": 3, "names": ["createGetColorSchemeSelector", "selector", "getColorSchemeSelector", "colorScheme", "process", "env", "NODE_ENV", "console", "error", "startsWith", "includes", "replace"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/cssVars/getColorSchemeSelector.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nexport function createGetColorSchemeSelector(selector) {\n  return function getColorSchemeSelector(colorScheme) {\n    if (selector === 'media') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (colorScheme !== 'light' && colorScheme !== 'dark') {\n          console.error(`MUI: @media (prefers-color-scheme) supports only 'light' or 'dark', but receive '${colorScheme}'.`);\n        }\n      }\n      return `@media (prefers-color-scheme: ${colorScheme})`;\n    }\n    if (selector) {\n      if (selector.startsWith('data-') && !selector.includes('%s')) {\n        return `[${selector}=\"${colorScheme}\"] &`;\n      }\n      if (selector === 'class') {\n        return `.${colorScheme} &`;\n      }\n      if (selector === 'data') {\n        return `[data-${colorScheme}] &`;\n      }\n      return `${selector.replace('%s', colorScheme)} &`;\n    }\n    return '&';\n  };\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,4BAA4BA,CAACC,QAAQ,EAAE;EACrD,OAAO,SAASC,sBAAsBA,CAACC,WAAW,EAAE;IAClD,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACxB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIH,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,EAAE;UACrDI,OAAO,CAACC,KAAK,CAAC,oFAAoFL,WAAW,IAAI,CAAC;QACpH;MACF;MACA,OAAO,iCAAiCA,WAAW,GAAG;IACxD;IACA,IAAIF,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAACQ,UAAU,CAAC,OAAO,CAAC,IAAI,CAACR,QAAQ,CAACS,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC5D,OAAO,IAAIT,QAAQ,KAAKE,WAAW,MAAM;MAC3C;MACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;QACxB,OAAO,IAAIE,WAAW,IAAI;MAC5B;MACA,IAAIF,QAAQ,KAAK,MAAM,EAAE;QACvB,OAAO,SAASE,WAAW,KAAK;MAClC;MACA,OAAO,GAAGF,QAAQ,CAACU,OAAO,CAAC,IAAI,EAAER,WAAW,CAAC,IAAI;IACnD;IACA,OAAO,GAAG;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}