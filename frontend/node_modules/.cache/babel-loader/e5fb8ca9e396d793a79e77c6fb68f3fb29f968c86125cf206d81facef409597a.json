{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/TestPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Alert, Card, CardContent } from '@mui/material';\nimport { adminService } from '../services/adminService';\nimport { apiClient } from '../services/apiClient';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestPage = () => {\n  _s();\n  const [testResults, setTestResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const runTests = async () => {\n    setLoading(true);\n    const results = [];\n\n    // Test 1: Check auth token\n    const token = localStorage.getItem('token');\n    results.push({\n      test: 'Auth Token Check',\n      status: token ? 'PASS' : 'FAIL',\n      details: token ? `Token exists (${token.length} chars)` : 'No token found'\n    });\n\n    // Test 2: Test admin stats API\n    try {\n      console.log('🧪 Testing admin stats...');\n      const stats = await adminService.getSystemStats();\n      results.push({\n        test: 'Admin Stats API',\n        status: 'PASS',\n        details: `Books: ${stats.totalBooks}, Users: ${stats.totalUsers}`\n      });\n    } catch (error) {\n      results.push({\n        test: 'Admin Stats API',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n\n    // Test 3: Test users API\n    try {\n      console.log('🧪 Testing users API...');\n      const users = await adminService.getUsers(1, 5);\n      results.push({\n        test: 'Users API',\n        status: 'PASS',\n        details: `Found ${users.total} users, showing ${users.users.length}`\n      });\n    } catch (error) {\n      results.push({\n        test: 'Users API',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n\n    // Test 4: Test direct API call\n    try {\n      console.log('🧪 Testing direct API call...');\n      const response = await apiClient.get('/admin/stats');\n      results.push({\n        test: 'Direct API Call',\n        status: 'PASS',\n        details: `Response received: ${JSON.stringify(response).substring(0, 100)}...`\n      });\n    } catch (error) {\n      results.push({\n        test: 'Direct API Call',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n    setTestResults(results);\n    setLoading(false);\n  };\n  useEffect(() => {\n    runTests();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Frontend API Test Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: runTests,\n      disabled: loading,\n      sx: {\n        mb: 3\n      },\n      children: loading ? 'Running Tests...' : 'Run Tests'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), testResults.map((result, index) => /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: result.status === 'PASS' ? 'success.main' : 'error.main',\n          children: [result.status === 'PASS' ? '✅' : '❌', \" \", result.test]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: result.details\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mt: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"How to use this test page:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 54\n        }, this), \"1. Make sure you're logged in as an admin user\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 57\n        }, this), \"2. Click \\\"Run Tests\\\" to test all API endpoints\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 57\n        }, this), \"3. Check the results to see what's working and what's not\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 68\n        }, this), \"4. Open browser console (F12) to see detailed logs\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(TestPage, \"Hw8nS21gkcQt9beSm6Ntm5e7Dyw=\");\n_c = TestPage;\nexport default TestPage;\nvar _c;\n$RefreshReg$(_c, \"TestPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "adminService", "apiClient", "jsxDEV", "_jsxDEV", "TestPage", "_s", "testResults", "setTestResults", "loading", "setLoading", "runTests", "results", "token", "localStorage", "getItem", "push", "test", "status", "details", "length", "console", "log", "stats", "getSystemStats", "totalBooks", "totalUsers", "error", "message", "users", "getUsers", "total", "response", "get", "JSON", "stringify", "substring", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "mb", "map", "result", "index", "color", "severity", "mt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/TestPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON>po<PERSON>, Button, Al<PERSON>, <PERSON>, CardContent } from '@mui/material';\nimport { adminService } from '../services/adminService';\nimport { apiClient } from '../services/apiClient';\n\nconst TestPage: React.FC = () => {\n  const [testResults, setTestResults] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  const runTests = async () => {\n    setLoading(true);\n    const results: any[] = [];\n\n    // Test 1: Check auth token\n    const token = localStorage.getItem('token');\n    results.push({\n      test: 'Auth Token Check',\n      status: token ? 'PASS' : 'FAIL',\n      details: token ? `Token exists (${token.length} chars)` : 'No token found'\n    });\n\n    // Test 2: Test admin stats API\n    try {\n      console.log('🧪 Testing admin stats...');\n      const stats = await adminService.getSystemStats();\n      results.push({\n        test: 'Admin Stats API',\n        status: 'PASS',\n        details: `Books: ${stats.totalBooks}, Users: ${stats.totalUsers}`\n      });\n    } catch (error: any) {\n      results.push({\n        test: 'Admin Stats API',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n\n    // Test 3: Test users API\n    try {\n      console.log('🧪 Testing users API...');\n      const users = await adminService.getUsers(1, 5);\n      results.push({\n        test: 'Users API',\n        status: 'PASS',\n        details: `Found ${users.total} users, showing ${users.users.length}`\n      });\n    } catch (error: any) {\n      results.push({\n        test: 'Users API',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n\n    // Test 4: Test direct API call\n    try {\n      console.log('🧪 Testing direct API call...');\n      const response = await apiClient.get('/admin/stats');\n      results.push({\n        test: 'Direct API Call',\n        status: 'PASS',\n        details: `Response received: ${JSON.stringify(response).substring(0, 100)}...`\n      });\n    } catch (error: any) {\n      results.push({\n        test: 'Direct API Call',\n        status: 'FAIL',\n        details: error.message\n      });\n    }\n\n    setTestResults(results);\n    setLoading(false);\n  };\n\n  useEffect(() => {\n    runTests();\n  }, []);\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Frontend API Test Page\n      </Typography>\n      \n      <Button \n        variant=\"contained\" \n        onClick={runTests} \n        disabled={loading}\n        sx={{ mb: 3 }}\n      >\n        {loading ? 'Running Tests...' : 'Run Tests'}\n      </Button>\n\n      {testResults.map((result, index) => (\n        <Card key={index} sx={{ mb: 2 }}>\n          <CardContent>\n            <Typography variant=\"h6\" color={result.status === 'PASS' ? 'success.main' : 'error.main'}>\n              {result.status === 'PASS' ? '✅' : '❌'} {result.test}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              {result.details}\n            </Typography>\n          </CardContent>\n        </Card>\n      ))}\n\n      <Alert severity=\"info\" sx={{ mt: 3 }}>\n        <Typography variant=\"body2\">\n          <strong>How to use this test page:</strong><br/>\n          1. Make sure you're logged in as an admin user<br/>\n          2. Click \"Run Tests\" to test all API endpoints<br/>\n          3. Check the results to see what's working and what's not<br/>\n          4. Open browser console (F12) to see detailed logs\n        </Typography>\n      </Alert>\n    </Box>\n  );\n};\n\nexport default TestPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,QAAQ,eAAe;AACjF,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,SAAS,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAQ,EAAE,CAAC;EACzD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMkB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BD,UAAU,CAAC,IAAI,CAAC;IAChB,MAAME,OAAc,GAAG,EAAE;;IAEzB;IACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CH,OAAO,CAACI,IAAI,CAAC;MACXC,IAAI,EAAE,kBAAkB;MACxBC,MAAM,EAAEL,KAAK,GAAG,MAAM,GAAG,MAAM;MAC/BM,OAAO,EAAEN,KAAK,GAAG,iBAAiBA,KAAK,CAACO,MAAM,SAAS,GAAG;IAC5D,CAAC,CAAC;;IAEF;IACA,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAMC,KAAK,GAAG,MAAMtB,YAAY,CAACuB,cAAc,CAAC,CAAC;MACjDZ,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,UAAUI,KAAK,CAACE,UAAU,YAAYF,KAAK,CAACG,UAAU;MACjE,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBf,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAEQ,KAAK,CAACC;MACjB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,MAAMO,KAAK,GAAG,MAAM5B,YAAY,CAAC6B,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/ClB,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,SAASU,KAAK,CAACE,KAAK,mBAAmBF,KAAK,CAACA,KAAK,CAACT,MAAM;MACpE,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAU,EAAE;MACnBf,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAEQ,KAAK,CAACC;MACjB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMU,QAAQ,GAAG,MAAM9B,SAAS,CAAC+B,GAAG,CAAC,cAAc,CAAC;MACpDrB,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,sBAAsBe,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAACI,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MAC3E,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOT,KAAU,EAAE;MACnBf,OAAO,CAACI,IAAI,CAAC;QACXC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAEQ,KAAK,CAACC;MACjB,CAAC,CAAC;IACJ;IAEApB,cAAc,CAACI,OAAO,CAAC;IACvBF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACdiB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEP,OAAA,CAACT,GAAG;IAAC0C,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBnC,OAAA,CAACR,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbzC,OAAA,CAACP,MAAM;MACL2C,OAAO,EAAC,WAAW;MACnBM,OAAO,EAAEnC,QAAS;MAClBoC,QAAQ,EAAEtC,OAAQ;MAClB4B,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAEb9B,OAAO,GAAG,kBAAkB,GAAG;IAAW;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,EAERtC,WAAW,CAAC0C,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7B/C,OAAA,CAACL,IAAI;MAAasC,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,eAC9BnC,OAAA,CAACJ,WAAW;QAAAuC,QAAA,gBACVnC,OAAA,CAACR,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAACY,KAAK,EAAEF,MAAM,CAAChC,MAAM,KAAK,MAAM,GAAG,cAAc,GAAG,YAAa;UAAAqB,QAAA,GACtFW,MAAM,CAAChC,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACgC,MAAM,CAACjC,IAAI;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACbzC,OAAA,CAACR,UAAU;UAAC4C,OAAO,EAAC,OAAO;UAACY,KAAK,EAAC,gBAAgB;UAAAb,QAAA,EAC/CW,MAAM,CAAC/B;QAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC,GARLM,KAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASV,CACP,CAAC,eAEFzC,OAAA,CAACN,KAAK;MAACuD,QAAQ,EAAC,MAAM;MAAChB,EAAE,EAAE;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,eACnCnC,OAAA,CAACR,UAAU;QAAC4C,OAAO,EAAC,OAAO;QAAAD,QAAA,gBACzBnC,OAAA;UAAAmC,QAAA,EAAQ;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAAAzC,OAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,kDACF,eAAAzC,OAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,oDACL,eAAAzC,OAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,6DACM,eAAAzC,OAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,sDAEhE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvC,EAAA,CAlHID,QAAkB;AAAAkD,EAAA,GAAlBlD,QAAkB;AAoHxB,eAAeA,QAAQ;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}