{"ast": null, "code": "export { default } from \"./compose.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/compose/index.js"], "sourcesContent": ["export { default } from \"./compose.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}