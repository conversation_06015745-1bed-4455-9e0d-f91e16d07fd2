{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Box, CircularProgress, Alert } from '@mui/material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check if user has admin role\n  if (user.role !== 'ADMIN') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Access denied. You need administrator privileges to access this page.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this);\n  }\n\n  // User is authenticated and has admin role\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(AdminRoute, \"zPafkKLdz6KrRvMe2id3iDpNU34=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = AdminRoute;\nexport default AdminRoute;\nvar _c;\n$RefreshReg$(_c, \"AdminRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "Box", "CircularProgress", "<PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminRoute", "children", "_s", "user", "loading", "location", "sx", "display", "justifyContent", "alignItems", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "role", "p", "severity", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/components/admin/AdminRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { Box, CircularProgress, Alert } from '@mui/material';\nimport { useAuth } from '../../contexts/AuthContext';\n\ninterface AdminRouteProps {\n  children: React.ReactNode;\n}\n\nconst AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {\n  const { user, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '100vh',\n        }}\n      >\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!user) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check if user has admin role\n  if (user.role !== 'ADMIN') {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"error\">\n          Access denied. You need administrator privileges to access this page.\n        </Alert>\n      </Box>\n    );\n  }\n\n  // User is authenticated and has admin role\n  return <>{children}</>;\n};\n\nexport default AdminRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,KAAK,QAAQ,eAAe;AAC5D,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMrD,MAAMC,UAAqC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnC,MAAMU,QAAQ,GAAGd,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIa,OAAO,EAAE;IACX,oBACEP,OAAA,CAACL,GAAG;MACFc,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAT,QAAA,eAEFJ,OAAA,CAACJ,gBAAgB;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;;EAEA;EACA,IAAI,CAACX,IAAI,EAAE;IACT,oBAAON,OAAA,CAACP,QAAQ;MAACyB,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEZ;MAAS,CAAE;MAACa,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIX,IAAI,CAACgB,IAAI,KAAK,OAAO,EAAE;IACzB,oBACEtB,OAAA,CAACL,GAAG;MAACc,EAAE,EAAE;QAAEc,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,eAChBJ,OAAA,CAACH,KAAK;QAAC2B,QAAQ,EAAC,OAAO;QAAApB,QAAA,EAAC;MAExB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,oBAAOjB,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAtCIF,UAAqC;EAAA,QACfL,OAAO,EAChBJ,WAAW;AAAA;AAAA+B,EAAA,GAFxBtB,UAAqC;AAwC3C,eAAeA,UAAU;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}