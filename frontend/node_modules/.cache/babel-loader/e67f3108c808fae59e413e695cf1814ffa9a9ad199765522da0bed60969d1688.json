{"ast": null, "code": "/**\n * Q&A service for API calls\n */\nimport { apiClient } from './apiClient';\nclass QAService {\n  async askQuestion(qaRequest) {\n    return apiClient.post('/qa/ask', qaRequest);\n  }\n  async getCitationDetails(citationId) {\n    return apiClient.get(`/qa/citation/${citationId}`);\n  }\n  async getQAHistory(limit = 50) {\n    return apiClient.get('/qa/history', {\n      params: {\n        limit\n      }\n    });\n  }\n  async submitFeedback(answerId, rating, feedbackText, helpfulCitations) {\n    return apiClient.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations\n    });\n  }\n  async getQAHealth() {\n    return apiClient.get('/qa/health');\n  }\n}\nexport const qaService = new QAService();", "map": {"version": 3, "names": ["apiClient", "QAService", "askQuestion", "qaRequest", "post", "getCitationDetails", "citationId", "get", "getQAHistory", "limit", "params", "submitFeedback", "answerId", "rating", "feedbackText", "helpfulCitations", "answer_id", "feedback_text", "helpful_citations", "getQAHealth", "qaService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts"], "sourcesContent": ["/**\n * Q&A service for API calls\n */\nimport { apiClient } from './apiClient';\nimport {\n  QARequest,\n  QAResponse,\n  Citation,\n  QAHistoryItem,\n  PaginatedResponse\n} from '../types';\n\nclass QAService {\n\n  async askQuestion(qaRequest: QARequest): Promise<QAResponse> {\n    return apiClient.post<QAResponse>('/qa/ask', qaRequest);\n  }\n\n  async getCitationDetails(citationId: string): Promise<any> {\n    return apiClient.get(`/qa/citation/${citationId}`);\n  }\n\n  async getQAHistory(limit: number = 50): Promise<PaginatedResponse<QAHistoryItem>> {\n    return apiClient.get<PaginatedResponse<QAHistoryItem>>('/qa/history', {\n      params: { limit },\n    });\n  }\n\n  async submitFeedback(\n    answerId: string,\n    rating: number,\n    feedbackText?: string,\n    helpfulCitations?: string[]\n  ): Promise<any> {\n    return apiClient.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations,\n    });\n  }\n\n  async getQAHealth(): Promise<any> {\n    return apiClient.get('/qa/health');\n  }\n}\n\nexport const qaService = new QAService();\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,aAAa;AASvC,MAAMC,SAAS,CAAC;EAEd,MAAMC,WAAWA,CAACC,SAAoB,EAAuB;IAC3D,OAAOH,SAAS,CAACI,IAAI,CAAa,SAAS,EAAED,SAAS,CAAC;EACzD;EAEA,MAAME,kBAAkBA,CAACC,UAAkB,EAAgB;IACzD,OAAON,SAAS,CAACO,GAAG,CAAC,gBAAgBD,UAAU,EAAE,CAAC;EACpD;EAEA,MAAME,YAAYA,CAACC,KAAa,GAAG,EAAE,EAA6C;IAChF,OAAOT,SAAS,CAACO,GAAG,CAAmC,aAAa,EAAE;MACpEG,MAAM,EAAE;QAAED;MAAM;IAClB,CAAC,CAAC;EACJ;EAEA,MAAME,cAAcA,CAClBC,QAAgB,EAChBC,MAAc,EACdC,YAAqB,EACrBC,gBAA2B,EACb;IACd,OAAOf,SAAS,CAACI,IAAI,CAAC,cAAc,EAAE;MACpCY,SAAS,EAAEJ,QAAQ;MACnBC,MAAM;MACNI,aAAa,EAAEH,YAAY;MAC3BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;EACJ;EAEA,MAAMI,WAAWA,CAAA,EAAiB;IAChC,OAAOnB,SAAS,CAACO,GAAG,CAAC,YAAY,CAAC;EACpC;AACF;AAEA,OAAO,MAAMa,SAAS,GAAG,IAAInB,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}