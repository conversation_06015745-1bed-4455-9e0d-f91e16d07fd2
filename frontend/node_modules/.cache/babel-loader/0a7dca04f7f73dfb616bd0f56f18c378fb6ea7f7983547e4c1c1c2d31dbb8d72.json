{"ast": null, "code": "export { default as applyStyles } from \"./applyStyles.js\";\nexport { default as arrow } from \"./arrow.js\";\nexport { default as computeStyles } from \"./computeStyles.js\";\nexport { default as eventListeners } from \"./eventListeners.js\";\nexport { default as flip } from \"./flip.js\";\nexport { default as hide } from \"./hide.js\";\nexport { default as offset } from \"./offset.js\";\nexport { default as popperOffsets } from \"./popperOffsets.js\";\nexport { default as preventOverflow } from \"./preventOverflow.js\";", "map": {"version": 3, "names": ["default", "applyStyles", "arrow", "computeStyles", "eventListeners", "flip", "hide", "offset", "popperOffsets", "preventOverflow"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@popperjs/core/lib/modifiers/index.js"], "sourcesContent": ["export { default as applyStyles } from \"./applyStyles.js\";\nexport { default as arrow } from \"./arrow.js\";\nexport { default as computeStyles } from \"./computeStyles.js\";\nexport { default as eventListeners } from \"./eventListeners.js\";\nexport { default as flip } from \"./flip.js\";\nexport { default as hide } from \"./hide.js\";\nexport { default as offset } from \"./offset.js\";\nexport { default as popperOffsets } from \"./popperOffsets.js\";\nexport { default as preventOverflow } from \"./preventOverflow.js\";"], "mappings": "AAAA,SAASA,OAAO,IAAIC,WAAW,QAAQ,kBAAkB;AACzD,SAASD,OAAO,IAAIE,KAAK,QAAQ,YAAY;AAC7C,SAASF,OAAO,IAAIG,aAAa,QAAQ,oBAAoB;AAC7D,SAASH,OAAO,IAAII,cAAc,QAAQ,qBAAqB;AAC/D,SAASJ,OAAO,IAAIK,IAAI,QAAQ,WAAW;AAC3C,SAASL,OAAO,IAAIM,IAAI,QAAQ,WAAW;AAC3C,SAASN,OAAO,IAAIO,MAAM,QAAQ,aAAa;AAC/C,SAASP,OAAO,IAAIQ,aAAa,QAAQ,oBAAoB;AAC7D,SAASR,OAAO,IAAIS,eAAe,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}