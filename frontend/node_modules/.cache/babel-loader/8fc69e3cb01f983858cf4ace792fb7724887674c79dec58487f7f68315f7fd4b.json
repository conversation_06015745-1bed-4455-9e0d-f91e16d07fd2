{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx\",\n  _s = $RefreshSig$();\n/**\n * Signup page component\n */\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation, Link as RouterLink } from 'react-router-dom';\nimport { Box, CardContent, TextField, Button, Typography, Link, Alert, CircularProgress, Container, Paper, MenuItem } from '@mui/material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignupPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    signup,\n    isAuthenticated,\n    isLoading,\n    error,\n    clearError\n  } = useAuth();\n  const {\n    showSuccess\n  } = useNotification();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    full_name: '',\n    institution: '',\n    specialization: '',\n    year_of_study: undefined\n  });\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const specializations = ['Internal Medicine', 'Surgery', 'Pediatrics', 'Obstetrics & Gynecology', 'Psychiatry', 'Emergency Medicine', 'Radiology', 'Anesthesiology', 'Pathology', 'Dermatology', 'Ophthalmology', 'Orthopedics', 'Cardiology', 'Neurology', 'Other'];\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n  const validateForm = () => {\n    const errors = {};\n    if (!formData.full_name.trim()) {\n      errors.full_name = 'Full name is required';\n    }\n    if (!formData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      errors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      errors.password = 'Password must be at least 8 characters';\n    }\n    if (!confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      var _location$state2, _location$state2$from;\n      await signup(formData);\n      showSuccess('Account created successfully! Welcome to MedPrep!');\n      const from = ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : (_location$state2$from = _location$state2.from) === null || _location$state2$from === void 0 ? void 0 : _location$state2$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'year_of_study') {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value ? parseInt(value) : undefined\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const handleConfirmPasswordChange = e => {\n    setConfirmPassword(e.target.value);\n    if (formErrors.confirmPassword) {\n      setFormErrors(prev => ({\n        ...prev,\n        confirmPassword: ''\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      p: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"md\",\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 10,\n        sx: {\n          borderRadius: 3,\n          overflow: 'hidden'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',\n            color: 'white',\n            p: 4,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Join MedPrep\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            children: \"Start your medical preparation journey\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h2\",\n            gutterBottom: true,\n            textAlign: \"center\",\n            sx: {\n              mb: 3\n            },\n            children: \"Create Your Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 3\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            component: \"form\",\n            onSubmit: handleSubmit,\n            noValidate: true,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"full_name\",\n                name: \"full_name\",\n                label: \"Full Name\",\n                value: formData.full_name,\n                onChange: handleChange,\n                error: !!formErrors.full_name,\n                helperText: formErrors.full_name,\n                required: true,\n                autoComplete: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"email\",\n                name: \"email\",\n                label: \"Email Address\",\n                type: \"email\",\n                value: formData.email,\n                onChange: handleChange,\n                error: !!formErrors.email,\n                helperText: formErrors.email,\n                required: true,\n                autoComplete: \"email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"password\",\n                name: \"password\",\n                label: \"Password\",\n                type: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                error: !!formErrors.password,\n                helperText: formErrors.password,\n                required: true,\n                autoComplete: \"new-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                label: \"Confirm Password\",\n                type: \"password\",\n                value: confirmPassword,\n                onChange: handleConfirmPasswordChange,\n                error: !!formErrors.confirmPassword,\n                helperText: formErrors.confirmPassword,\n                required: true,\n                autoComplete: \"new-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"institution\",\n                name: \"institution\",\n                label: \"Institution (Optional)\",\n                value: formData.institution,\n                onChange: handleChange,\n                autoComplete: \"organization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                id: \"specialization\",\n                name: \"specialization\",\n                label: \"Specialization (Optional)\",\n                select: true,\n                value: formData.specialization,\n                onChange: handleChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: /*#__PURE__*/_jsxDEV(\"em\", {\n                    children: \"Select specialization\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), specializations.map(spec => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: spec,\n                  children: spec\n                }, spec, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              id: \"year_of_study\",\n              name: \"year_of_study\",\n              label: \"Year of Study (Optional)\",\n              type: \"number\",\n              value: formData.year_of_study || '',\n              onChange: handleChange,\n              inputProps: {\n                min: 1,\n                max: 10\n              },\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              fullWidth: true,\n              variant: \"contained\",\n              size: \"large\",\n              disabled: isLoading,\n              sx: {\n                mt: 2,\n                mb: 2,\n                py: 1.5\n              },\n              children: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24,\n                color: \"inherit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this) : 'Create Account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                  component: RouterLink,\n                  to: \"/login\",\n                  underline: \"hover\",\n                  children: \"Sign in here\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(SignupPage, \"lg8HA8z7xcKoZ2sEkRNAUtv63ow=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useNotification];\n});\n_c = SignupPage;\nexport default SignupPage;\nvar _c;\n$RefreshReg$(_c, \"SignupPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "Link", "RouterLink", "Box", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "Container", "Paper", "MenuItem", "useAuth", "useNotification", "jsxDEV", "_jsxDEV", "SignupPage", "_s", "navigate", "location", "signup", "isAuthenticated", "isLoading", "error", "clearError", "showSuccess", "formData", "setFormData", "email", "password", "full_name", "institution", "specialization", "year_of_study", "undefined", "confirmPassword", "setConfirmPassword", "formErrors", "setFormErrors", "specializations", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "validateForm", "errors", "trim", "test", "length", "Object", "keys", "handleSubmit", "e", "preventDefault", "_location$state2", "_location$state2$from", "handleChange", "name", "value", "target", "prev", "parseInt", "handleConfirmPasswordChange", "sx", "minHeight", "display", "alignItems", "justifyContent", "background", "p", "children", "max<PERSON><PERSON><PERSON>", "elevation", "borderRadius", "overflow", "color", "textAlign", "variant", "component", "gutterBottom", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "severity", "onSubmit", "noValidate", "gap", "fullWidth", "id", "label", "onChange", "helperText", "required", "autoComplete", "type", "select", "map", "spec", "inputProps", "min", "max", "size", "disabled", "mt", "py", "to", "underline", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SignupPage.tsx"], "sourcesContent": ["/**\n * Signup page component\n */\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation, Link as RouterLink } from 'react-router-dom';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Link,\n  Alert,\n  CircularProgress,\n  Container,\n  Paper,\n  MenuItem,\n} from '@mui/material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { SignupRequest } from '../types';\n\nconst SignupPage: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { signup, isAuthenticated, isLoading, error, clearError } = useAuth();\n  const { showSuccess } = useNotification();\n\n  const [formData, setFormData] = useState<SignupRequest>({\n    email: '',\n    password: '',\n    full_name: '',\n    institution: '',\n    specialization: '',\n    year_of_study: undefined,\n  });\n\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});\n\n  const specializations = [\n    'Internal Medicine',\n    'Surgery',\n    'Pediatrics',\n    'Obstetrics & Gynecology',\n    'Psychiatry',\n    'Emergency Medicine',\n    'Radiology',\n    'Anesthesiology',\n    'Pathology',\n    'Dermatology',\n    'Ophthalmology',\n    'Orthopedics',\n    'Cardiology',\n    'Neurology',\n    'Other',\n  ];\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = location.state?.from?.pathname || '/';\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  const validateForm = (): boolean => {\n    const errors: { [key: string]: string } = {};\n\n    if (!formData.full_name.trim()) {\n      errors.full_name = 'Full name is required';\n    }\n\n    if (!formData.email) {\n      errors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      errors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      errors.password = 'Password must be at least 8 characters';\n    }\n\n    if (!confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await signup(formData);\n      showSuccess('Account created successfully! Welcome to MedPrep!');\n      const from = location.state?.from?.pathname || '/';\n      navigate(from, { replace: true });\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n\n    if (name === 'year_of_study') {\n      setFormData(prev => ({ ...prev, [name]: value ? parseInt(value) : undefined }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setConfirmPassword(e.target.value);\n    if (formErrors.confirmPassword) {\n      setFormErrors(prev => ({ ...prev, confirmPassword: '' }));\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        p: 2,\n      }}\n    >\n      <Container maxWidth=\"md\">\n        <Paper\n          elevation={10}\n          sx={{\n            borderRadius: 3,\n            overflow: 'hidden',\n          }}\n        >\n          {/* Header */}\n          <Box\n            sx={{\n              background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',\n              color: 'white',\n              p: 4,\n              textAlign: 'center',\n            }}\n          >\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Join MedPrep\n            </Typography>\n            <Typography variant=\"subtitle1\">\n              Start your medical preparation journey\n            </Typography>\n          </Box>\n\n          <CardContent sx={{ p: 4 }}>\n            <Typography variant=\"h5\" component=\"h2\" gutterBottom textAlign=\"center\" sx={{ mb: 3 }}>\n              Create Your Account\n            </Typography>\n\n            {error && (\n              <Alert severity=\"error\" sx={{ mb: 3 }}>\n                {error}\n              </Alert>\n            )}\n\n            <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n                <TextField\n                  fullWidth\n                  id=\"full_name\"\n                  name=\"full_name\"\n                  label=\"Full Name\"\n                  value={formData.full_name}\n                  onChange={handleChange}\n                  error={!!formErrors.full_name}\n                  helperText={formErrors.full_name}\n                  required\n                  autoComplete=\"name\"\n                />\n                <TextField\n                  fullWidth\n                  id=\"email\"\n                  name=\"email\"\n                  label=\"Email Address\"\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  error={!!formErrors.email}\n                  helperText={formErrors.email}\n                  required\n                  autoComplete=\"email\"\n                />\n              </Box>\n\n              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n                <TextField\n                  fullWidth\n                  id=\"password\"\n                  name=\"password\"\n                  label=\"Password\"\n                  type=\"password\"\n                  value={formData.password}\n                  onChange={handleChange}\n                  error={!!formErrors.password}\n                  helperText={formErrors.password}\n                  required\n                  autoComplete=\"new-password\"\n                />\n                <TextField\n                  fullWidth\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  label=\"Confirm Password\"\n                  type=\"password\"\n                  value={confirmPassword}\n                  onChange={handleConfirmPasswordChange}\n                  error={!!formErrors.confirmPassword}\n                  helperText={formErrors.confirmPassword}\n                  required\n                  autoComplete=\"new-password\"\n                />\n              </Box>\n\n              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n                <TextField\n                  fullWidth\n                  id=\"institution\"\n                  name=\"institution\"\n                  label=\"Institution (Optional)\"\n                  value={formData.institution}\n                  onChange={handleChange}\n                  autoComplete=\"organization\"\n                />\n                <TextField\n                  fullWidth\n                  id=\"specialization\"\n                  name=\"specialization\"\n                  label=\"Specialization (Optional)\"\n                  select\n                  value={formData.specialization}\n                  onChange={handleChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Select specialization</em>\n                  </MenuItem>\n                  {specializations.map((spec) => (\n                    <MenuItem key={spec} value={spec}>\n                      {spec}\n                    </MenuItem>\n                  ))}\n                </TextField>\n              </Box>\n\n              <TextField\n                fullWidth\n                id=\"year_of_study\"\n                name=\"year_of_study\"\n                label=\"Year of Study (Optional)\"\n                type=\"number\"\n                value={formData.year_of_study || ''}\n                onChange={handleChange}\n                inputProps={{ min: 1, max: 10 }}\n                sx={{ mb: 3 }}\n              />\n\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                size=\"large\"\n                disabled={isLoading}\n                sx={{ mt: 2, mb: 2, py: 1.5 }}\n              >\n                {isLoading ? (\n                  <CircularProgress size={24} color=\"inherit\" />\n                ) : (\n                  'Create Account'\n                )}\n              </Button>\n\n              <Box textAlign=\"center\">\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Already have an account?{' '}\n                  <Link component={RouterLink} to=\"/login\" underline=\"hover\">\n                    Sign in here\n                  </Link>\n                </Typography>\n              </Box>\n            </Box>\n          </CardContent>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default SignupPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,EAAEC,IAAI,IAAIC,UAAU,QAAQ,kBAAkB;AAC/E,SACEC,GAAG,EAEHC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVN,IAAI,EACJO,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACTC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,eAAe,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB,MAAM;IAAEC,eAAe;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC3E,MAAM;IAAEa;EAAY,CAAC,GAAGZ,eAAe,CAAC,CAAC;EAEzC,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAgB;IACtDgC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,aAAa,EAAEC;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAE3E,MAAM2C,eAAe,GAAG,CACtB,mBAAmB,EACnB,SAAS,EACT,YAAY,EACZ,yBAAyB,EACzB,YAAY,EACZ,oBAAoB,EACpB,WAAW,EACX,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,eAAe,EACf,aAAa,EACb,YAAY,EACZ,WAAW,EACX,OAAO,CACR;;EAED;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIwB,eAAe,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAAArB,QAAQ,CAACwB,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBG,QAAQ,KAAI,GAAG;MAClD1B,QAAQ,CAACwB,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACxB,eAAe,EAAEH,QAAQ,EAAEC,QAAQ,CAAC,CAAC;;EAEzC;EACAtB,SAAS,CAAC,MAAM;IACd2B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMsB,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,MAAiC,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACrB,QAAQ,CAACI,SAAS,CAACkB,IAAI,CAAC,CAAC,EAAE;MAC9BD,MAAM,CAACjB,SAAS,GAAG,uBAAuB;IAC5C;IAEA,IAAI,CAACJ,QAAQ,CAACE,KAAK,EAAE;MACnBmB,MAAM,CAACnB,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACqB,IAAI,CAACvB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC/CmB,MAAM,CAACnB,KAAK,GAAG,kBAAkB;IACnC;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBkB,MAAM,CAAClB,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACqB,MAAM,GAAG,CAAC,EAAE;MACvCH,MAAM,CAAClB,QAAQ,GAAG,wCAAwC;IAC5D;IAEA,IAAI,CAACM,eAAe,EAAE;MACpBY,MAAM,CAACZ,eAAe,GAAG,8BAA8B;IACzD,CAAC,MAAM,IAAIT,QAAQ,CAACG,QAAQ,KAAKM,eAAe,EAAE;MAChDY,MAAM,CAACZ,eAAe,GAAG,wBAAwB;IACnD;IAEAG,aAAa,CAACS,MAAM,CAAC;IACrB,OAAOI,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACT,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MAAA,IAAAU,gBAAA,EAAAC,qBAAA;MACF,MAAMrC,MAAM,CAACM,QAAQ,CAAC;MACtBD,WAAW,CAAC,mDAAmD,CAAC;MAChE,MAAMiB,IAAI,GAAG,EAAAc,gBAAA,GAAArC,QAAQ,CAACwB,KAAK,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBb,QAAQ,KAAI,GAAG;MAClD1B,QAAQ,CAACwB,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,MAAMmC,YAAY,GAAIJ,CAAsC,IAAK;IAC/D,MAAM;MAAEK,IAAI;MAAEC;IAAM,CAAC,GAAGN,CAAC,CAACO,MAAM;IAEhC,IAAIF,IAAI,KAAK,eAAe,EAAE;MAC5BhC,WAAW,CAACmC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAGC,KAAK,GAAGG,QAAQ,CAACH,KAAK,CAAC,GAAG1B;MAAU,CAAC,CAAC,CAAC;IACjF,CAAC,MAAM;MACLP,WAAW,CAACmC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAGC;MAAM,CAAC,CAAC,CAAC;IACnD;;IAEA;IACA,IAAIvB,UAAU,CAACsB,IAAI,CAAC,EAAE;MACpBrB,aAAa,CAACwB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACH,IAAI,GAAG;MAAG,CAAC,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMK,2BAA2B,GAAIV,CAAsC,IAAK;IAC9ElB,kBAAkB,CAACkB,CAAC,CAACO,MAAM,CAACD,KAAK,CAAC;IAClC,IAAIvB,UAAU,CAACF,eAAe,EAAE;MAC9BG,aAAa,CAACwB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3B,eAAe,EAAE;MAAG,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC;EAED,oBACEpB,OAAA,CAACb,GAAG;IACF+D,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,mDAAmD;MAC/DC,CAAC,EAAE;IACL,CAAE;IAAAC,QAAA,eAEFzD,OAAA,CAACN,SAAS;MAACgE,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtBzD,OAAA,CAACL,KAAK;QACJgE,SAAS,EAAE,EAAG;QACdT,EAAE,EAAE;UACFU,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,gBAGFzD,OAAA,CAACb,GAAG;UACF+D,EAAE,EAAE;YACFK,UAAU,EAAE,kDAAkD;YAC9DO,KAAK,EAAE,OAAO;YACdN,CAAC,EAAE,CAAC;YACJO,SAAS,EAAE;UACb,CAAE;UAAAN,QAAA,gBAEFzD,OAAA,CAACT,UAAU;YAACyE,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,YAAY;YAAChB,EAAE,EAAE;cAAEiB,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAEjF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvE,OAAA,CAACT,UAAU;YAACyE,OAAO,EAAC,WAAW;YAAAP,QAAA,EAAC;UAEhC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENvE,OAAA,CAACZ,WAAW;UAAC8D,EAAE,EAAE;YAAEM,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBACxBzD,OAAA,CAACT,UAAU;YAACyE,OAAO,EAAC,IAAI;YAACC,SAAS,EAAC,IAAI;YAACC,YAAY;YAACH,SAAS,EAAC,QAAQ;YAACb,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,EAAC;UAEvF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ/D,KAAK,iBACJR,OAAA,CAACR,KAAK;YAACiF,QAAQ,EAAC,OAAO;YAACvB,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAf,QAAA,EACnCjD;UAAK;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAEDvE,OAAA,CAACb,GAAG;YAAC8E,SAAS,EAAC,MAAM;YAACS,QAAQ,EAAEpC,YAAa;YAACqC,UAAU;YAAAlB,QAAA,gBACtDzD,OAAA,CAACb,GAAG;cAAC+D,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEwB,GAAG,EAAE,CAAC;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,gBAC1CzD,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,WAAW;gBACdlC,IAAI,EAAC,WAAW;gBAChBmC,KAAK,EAAC,WAAW;gBACjBlC,KAAK,EAAElC,QAAQ,CAACI,SAAU;gBAC1BiE,QAAQ,EAAErC,YAAa;gBACvBnC,KAAK,EAAE,CAAC,CAACc,UAAU,CAACP,SAAU;gBAC9BkE,UAAU,EAAE3D,UAAU,CAACP,SAAU;gBACjCmE,QAAQ;gBACRC,YAAY,EAAC;cAAM;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACFvE,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,OAAO;gBACVlC,IAAI,EAAC,OAAO;gBACZmC,KAAK,EAAC,eAAe;gBACrBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAElC,QAAQ,CAACE,KAAM;gBACtBmE,QAAQ,EAAErC,YAAa;gBACvBnC,KAAK,EAAE,CAAC,CAACc,UAAU,CAACT,KAAM;gBAC1BoE,UAAU,EAAE3D,UAAU,CAACT,KAAM;gBAC7BqE,QAAQ;gBACRC,YAAY,EAAC;cAAO;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA,CAACb,GAAG;cAAC+D,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEwB,GAAG,EAAE,CAAC;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,gBAC1CzD,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,UAAU;gBACblC,IAAI,EAAC,UAAU;gBACfmC,KAAK,EAAC,UAAU;gBAChBK,IAAI,EAAC,UAAU;gBACfvC,KAAK,EAAElC,QAAQ,CAACG,QAAS;gBACzBkE,QAAQ,EAAErC,YAAa;gBACvBnC,KAAK,EAAE,CAAC,CAACc,UAAU,CAACR,QAAS;gBAC7BmE,UAAU,EAAE3D,UAAU,CAACR,QAAS;gBAChCoE,QAAQ;gBACRC,YAAY,EAAC;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFvE,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,iBAAiB;gBACpBlC,IAAI,EAAC,iBAAiB;gBACtBmC,KAAK,EAAC,kBAAkB;gBACxBK,IAAI,EAAC,UAAU;gBACfvC,KAAK,EAAEzB,eAAgB;gBACvB4D,QAAQ,EAAE/B,2BAA4B;gBACtCzC,KAAK,EAAE,CAAC,CAACc,UAAU,CAACF,eAAgB;gBACpC6D,UAAU,EAAE3D,UAAU,CAACF,eAAgB;gBACvC8D,QAAQ;gBACRC,YAAY,EAAC;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvE,OAAA,CAACb,GAAG;cAAC+D,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEwB,GAAG,EAAE,CAAC;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,gBAC1CzD,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,aAAa;gBAChBlC,IAAI,EAAC,aAAa;gBAClBmC,KAAK,EAAC,wBAAwB;gBAC9BlC,KAAK,EAAElC,QAAQ,CAACK,WAAY;gBAC5BgE,QAAQ,EAAErC,YAAa;gBACvBwC,YAAY,EAAC;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFvE,OAAA,CAACX,SAAS;gBACRwF,SAAS;gBACTC,EAAE,EAAC,gBAAgB;gBACnBlC,IAAI,EAAC,gBAAgB;gBACrBmC,KAAK,EAAC,2BAA2B;gBACjCM,MAAM;gBACNxC,KAAK,EAAElC,QAAQ,CAACM,cAAe;gBAC/B+D,QAAQ,EAAErC,YAAa;gBAAAc,QAAA,gBAEvBzD,OAAA,CAACJ,QAAQ;kBAACiD,KAAK,EAAC,EAAE;kBAAAY,QAAA,eAChBzD,OAAA;oBAAAyD,QAAA,EAAI;kBAAqB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,EACV/C,eAAe,CAAC8D,GAAG,CAAEC,IAAI,iBACxBvF,OAAA,CAACJ,QAAQ;kBAAYiD,KAAK,EAAE0C,IAAK;kBAAA9B,QAAA,EAC9B8B;gBAAI,GADQA,IAAI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENvE,OAAA,CAACX,SAAS;cACRwF,SAAS;cACTC,EAAE,EAAC,eAAe;cAClBlC,IAAI,EAAC,eAAe;cACpBmC,KAAK,EAAC,0BAA0B;cAChCK,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAElC,QAAQ,CAACO,aAAa,IAAI,EAAG;cACpC8D,QAAQ,EAAErC,YAAa;cACvB6C,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE;cAAG,CAAE;cAChCxC,EAAE,EAAE;gBAAEsB,EAAE,EAAE;cAAE;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFvE,OAAA,CAACV,MAAM;cACL8F,IAAI,EAAC,QAAQ;cACbP,SAAS;cACTb,OAAO,EAAC,WAAW;cACnB2B,IAAI,EAAC,OAAO;cACZC,QAAQ,EAAErF,SAAU;cACpB2C,EAAE,EAAE;gBAAE2C,EAAE,EAAE,CAAC;gBAAErB,EAAE,EAAE,CAAC;gBAAEsB,EAAE,EAAE;cAAI,CAAE;cAAArC,QAAA,EAE7BlD,SAAS,gBACRP,OAAA,CAACP,gBAAgB;gBAACkG,IAAI,EAAE,EAAG;gBAAC7B,KAAK,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAE9C;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAETvE,OAAA,CAACb,GAAG;cAAC4E,SAAS,EAAC,QAAQ;cAAAN,QAAA,eACrBzD,OAAA,CAACT,UAAU;gBAACyE,OAAO,EAAC,OAAO;gBAACF,KAAK,EAAC,gBAAgB;gBAAAL,QAAA,GAAC,0BACzB,EAAC,GAAG,eAC5BzD,OAAA,CAACf,IAAI;kBAACgF,SAAS,EAAE/E,UAAW;kBAAC6G,EAAE,EAAC,QAAQ;kBAACC,SAAS,EAAC,OAAO;kBAAAvC,QAAA,EAAC;gBAE3D;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACrE,EAAA,CArSID,UAAoB;EAAA,QACPlB,WAAW,EACXC,WAAW,EACsCa,OAAO,EACjDC,eAAe;AAAA;AAAAmG,EAAA,GAJnChG,UAAoB;AAuS1B,eAAeA,UAAU;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}