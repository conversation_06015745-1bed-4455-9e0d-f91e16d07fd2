{"ast": null, "code": "export { default } from \"./requirePropFactory.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/requirePropFactory/index.js"], "sourcesContent": ["export { default } from \"./requirePropFactory.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}