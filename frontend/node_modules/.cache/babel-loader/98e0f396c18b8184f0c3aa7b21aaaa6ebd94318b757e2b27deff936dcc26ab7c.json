{"ast": null, "code": "'use client';\n\n// TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- process.env never changes, dependency arrays are intentionally ignored\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled(props) {\n  const {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = props;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      // Object.is() is not equivalent to the === operator.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is for more details.\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n\n  // TODO: provide overloads for the useControlled function to account for the case where either\n  // controlled or default is not undefiend.\n  // In that case the return type should be [T, React.Dispatch<React.SetStateAction<T>>]\n  // otherwise it should be [T | undefined, React.Dispatch<React.SetStateAction<T | undefined>>]\n  return [value, setValueIfUncontrolled];\n}", "map": {"version": 3, "names": ["React", "useControlled", "props", "controlled", "default", "defaultProp", "name", "state", "current", "isControlled", "useRef", "undefined", "valueState", "setValue", "useState", "value", "process", "env", "NODE_ENV", "useEffect", "console", "error", "join", "defaultValue", "Object", "is", "JSON", "stringify", "setValueIfUncontrolled", "useCallback", "newValue"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/useControlled/useControlled.js"], "sourcesContent": ["'use client';\n\n// TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- process.env never changes, dependency arrays are intentionally ignored\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled(props) {\n  const {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = props;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      // Object.is() is not equivalent to the === operator.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is for more details.\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n\n  // TODO: provide overloads for the useControlled function to account for the case where either\n  // controlled or default is not undefiend.\n  // In that case the return type should be [T, React.Dispatch<React.SetStateAction<T>>]\n  // otherwise it should be [T | undefined, React.Dispatch<React.SetStateAction<T | undefined>>]\n  return [value, setValueIfUncontrolled];\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAM;IACJC,UAAU;IACVC,OAAO,EAAEC,WAAW;IACpBC,IAAI;IACJC,KAAK,GAAG;EACV,CAAC,GAAGL,KAAK;EACT;EACA,MAAM;IACJM,OAAO,EAAEC;EACX,CAAC,GAAGT,KAAK,CAACU,MAAM,CAACP,UAAU,KAAKQ,SAAS,CAAC;EAC1C,MAAM,CAACC,UAAU,EAAEC,QAAQ,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAACT,WAAW,CAAC;EAC1D,MAAMU,KAAK,GAAGN,YAAY,GAAGN,UAAU,GAAGS,UAAU;EACpD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzClB,KAAK,CAACmB,SAAS,CAAC,MAAM;MACpB,IAAIV,YAAY,MAAMN,UAAU,KAAKQ,SAAS,CAAC,EAAE;QAC/CS,OAAO,CAACC,KAAK,CAAC,CAAC,oCAAoCZ,YAAY,GAAG,EAAE,GAAG,IAAI,cAAcF,KAAK,aAAaD,IAAI,UAAUG,YAAY,GAAG,IAAI,GAAG,EAAE,aAAa,EAAE,6EAA6E,EAAE,qDAAqDH,IAAI,GAAG,GAAG,4CAA4C,EAAE,4HAA4H,EAAE,sDAAsD,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/hB;IACF,CAAC,EAAE,CAACf,KAAK,EAAED,IAAI,EAAEH,UAAU,CAAC,CAAC;IAC7B,MAAM;MACJK,OAAO,EAAEe;IACX,CAAC,GAAGvB,KAAK,CAACU,MAAM,CAACL,WAAW,CAAC;IAC7BL,KAAK,CAACmB,SAAS,CAAC,MAAM;MACpB;MACA;MACA,IAAI,CAACV,YAAY,IAAI,CAACe,MAAM,CAACC,EAAE,CAACF,YAAY,EAAElB,WAAW,CAAC,EAAE;QAC1De,OAAO,CAACC,KAAK,CAAC,CAAC,4CAA4Cd,KAAK,6BAA6BD,IAAI,4BAA4B,GAAG,oDAAoDA,IAAI,GAAG,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1M;IACF,CAAC,EAAE,CAACI,IAAI,CAACC,SAAS,CAACtB,WAAW,CAAC,CAAC,CAAC;EACnC;EACA,MAAMuB,sBAAsB,GAAG5B,KAAK,CAAC6B,WAAW,CAACC,QAAQ,IAAI;IAC3D,IAAI,CAACrB,YAAY,EAAE;MACjBI,QAAQ,CAACiB,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA,OAAO,CAACf,KAAK,EAAEa,sBAAsB,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}