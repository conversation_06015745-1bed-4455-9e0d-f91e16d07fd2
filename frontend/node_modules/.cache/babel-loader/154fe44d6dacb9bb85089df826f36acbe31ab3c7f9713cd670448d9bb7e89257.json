{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx\",\n  _s = $RefreshSig$();\n/**\n * Search page component\n */\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Paper, TextField, Button, Card, CardContent, Chip, CircularProgress, Alert, FormControl, InputLabel, Select, MenuItem, Checkbox, Slider, IconButton, Tooltip } from '@mui/material';\nimport { Search as SearchIcon, FilterList as FilterIcon, Clear as ClearIcon, QuestionAnswer as QAIcon, MenuBook as BookIcon, Star as StarIcon } from '@mui/icons-material';\nimport { searchService } from '../services/searchService';\nimport { qaService } from '../services/qaService';\nimport { useNotification } from '../contexts/NotificationContext';\nimport { TopicCategory } from '../types';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchPage = () => {\n  _s();\n  var _qaResponse$confidenc;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const {\n    showError,\n    showSuccess\n  } = useNotification();\n\n  // Search state\n  const [query, setQuery] = useState(searchParams.get('q') || '');\n  const [searchResults, setSearchResults] = useState(null);\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchError, setSearchError] = useState(null);\n\n  // Q&A state\n  const [qaResponse, setQaResponse] = useState(null);\n  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false);\n  const [qaError, setQaError] = useState(null);\n\n  // Filter state\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    topicCategories: [],\n    scoreThreshold: 0.7,\n    limit: 20\n  });\n\n  // Search mode\n  const [searchMode, setSearchMode] = useState('search');\n\n  // Citation viewer state\n  const [selectedCitation, setSelectedCitation] = useState(null);\n  const [citationViewerOpen, setCitationViewerOpen] = useState(false);\n  useEffect(() => {\n    const initialQuery = searchParams.get('q');\n    if (initialQuery && initialQuery !== query) {\n      setQuery(initialQuery);\n      handleSearch(initialQuery);\n    }\n  }, [searchParams]);\n  const handleSearch = async searchQuery => {\n    const queryToSearch = searchQuery || query;\n    if (!queryToSearch.trim()) {\n      showError('Please enter a search query');\n      return;\n    }\n    setIsSearching(true);\n    setSearchError(null);\n    setSearchResults(null);\n    try {\n      const searchRequest = {\n        query: queryToSearch,\n        limit: filters.limit,\n        score_threshold: filters.scoreThreshold,\n        filters: {\n          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined\n        }\n      };\n      const results = await searchService.search(searchRequest);\n      setSearchResults(results);\n\n      // Update URL\n      setSearchParams({\n        q: queryToSearch\n      });\n      showSuccess(`Found ${results.total_results} results in ${results.search_time_ms}ms`);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message || 'Search failed';\n      setSearchError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n  const handleAskQuestion = async () => {\n    if (!query.trim()) {\n      showError('Please enter a question');\n      return;\n    }\n    setIsGeneratingAnswer(true);\n    setQaError(null);\n    setQaResponse(null);\n    try {\n      const qaRequest = {\n        question: query,\n        context_limit: 5,\n        include_citations: true,\n        filters: {\n          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined\n        }\n      };\n      const response = await qaService.askQuestion(qaRequest);\n      setQaResponse(response);\n      showSuccess(`Generated answer with ${response.citations.length} citations in ${response.response_time_ms}ms`);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message || 'Failed to generate answer';\n      setQaError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsGeneratingAnswer(false);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (searchMode === 'search') {\n      handleSearch();\n    } else {\n      handleAskQuestion();\n    }\n  };\n  const clearFilters = () => {\n    setFilters({\n      topicCategories: [],\n      scoreThreshold: 0.7,\n      limit: 20\n    });\n  };\n  const topicCategories = Object.values(TopicCategory);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      maxWidth: 1200,\n      mx: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"Medical Literature Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Search through medical textbooks or ask AI-powered questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        component: \"form\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: searchMode === 'search' ? 'contained' : 'outlined',\n            startIcon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 26\n            }, this),\n            onClick: () => setSearchMode('search'),\n            children: \"Search Literature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: searchMode === 'qa' ? 'contained' : 'outlined',\n            startIcon: /*#__PURE__*/_jsxDEV(QAIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 26\n            }, this),\n            onClick: () => setSearchMode('qa'),\n            children: \"Ask AI Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            value: query,\n            onChange: e => setQuery(e.target.value),\n            placeholder: searchMode === 'search' ? 'Search medical literature...' : 'Ask a medical question...',\n            variant: \"outlined\",\n            InputProps: {\n              endAdornment: query && /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setQuery(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            size: \"large\",\n            disabled: isSearching || isGeneratingAnswer,\n            sx: {\n              minWidth: 120\n            },\n            children: isSearching || isGeneratingAnswer ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this) : searchMode === 'search' ? 'Search' : 'Ask AI'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 26\n            }, this),\n            onClick: () => setShowFilters(!showFilters),\n            variant: \"text\",\n            children: showFilters ? 'Hide Filters' : 'Show Filters'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), (filters.topicCategories.length > 0 || filters.scoreThreshold !== 0.7 || filters.limit !== 20) && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: clearFilters,\n            size: \"small\",\n            color: \"secondary\",\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          pt: 3,\n          borderTop: 1,\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Search Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            sx: {\n              minWidth: 200\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Topic Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              multiple: true,\n              value: filters.topicCategories,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                topicCategories: e.target.value\n              })),\n              renderValue: selected => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 0.5\n                },\n                children: selected.map(value => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: value,\n                  size: \"small\"\n                }, value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this),\n              children: topicCategories.map(category => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: category,\n                children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                  checked: filters.topicCategories.indexOf(category) > -1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 23\n                }, this), category.charAt(0).toUpperCase() + category.slice(1)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              minWidth: 200\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              gutterBottom: true,\n              children: [\"Relevance Threshold: \", filters.scoreThreshold]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Slider, {\n              value: filters.scoreThreshold,\n              onChange: (_, value) => setFilters(prev => ({\n                ...prev,\n                scoreThreshold: value\n              })),\n              min: 0.1,\n              max: 1.0,\n              step: 0.1,\n              marks: true,\n              valueLabelDisplay: \"auto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            sx: {\n              minWidth: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Results Limit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.limit,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                limit: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 10,\n                children: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 20,\n                children: \"20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 50,\n                children: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 100,\n                children: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), (searchError || qaError) && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: searchError || qaError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this), qaResponse && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(QAIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), \"AI Answer\", /*#__PURE__*/_jsxDEV(Chip, {\n          label: `${((_qaResponse$confidenc = qaResponse.confidence_score) === null || _qaResponse$confidenc === void 0 ? void 0 : _qaResponse$confidenc.toFixed(2)) || 'N/A'} confidence`,\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 3,\n          lineHeight: 1.7\n        },\n        children: qaResponse.answer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this), qaResponse.citations.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Citations (\", qaResponse.citations.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 2\n          },\n          children: qaResponse.citations.map((citation, index) => /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'flex-start',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"primary\",\n                  children: [\"[\", citation.citation_order, \"] \", citation.book_title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${(citation.relevance_score * 100).toFixed(1)}%`,\n                  size: \"small\",\n                  color: \"secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mb: 1\n                },\n                children: [citation.book_authors.join(', '), \" \\u2022 Page \", citation.page_number, citation.chapter_title && ` • ${citation.chapter_title}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: citation.cited_text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          pt: 2,\n          borderTop: 1,\n          borderColor: 'divider',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"Generated in \", qaResponse.response_time_ms, \"ms using \", qaResponse.model_used]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [qaResponse.context_chunks_used, \" context chunks used\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 9\n    }, this), searchResults && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(BookIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), \"Search Results (\", searchResults.total_results, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"Found in \", searchResults.search_time_ms, \"ms\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this), searchResults.results.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          py: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: \"No results found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Try adjusting your search terms or filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: 2\n        },\n        children: searchResults.results.map((result, index) => /*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: result.book_title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: [result.book_authors.join(', '), result.book_publisher && ` • ${result.book_publisher}`, result.book_publication_year && ` • ${result.book_publication_year}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: [\"Page \", result.page_number, result.chapter_title && ` • ${result.chapter_title}`, result.topic_category && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: result.topic_category,\n                    size: \"small\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Relevance Score\",\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n                      color: \"primary\",\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"primary\",\n                      sx: {\n                        fontWeight: 'bold'\n                      },\n                      children: [(result.score * 100).toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                lineHeight: 1.6\n              },\n              children: result.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 21\n            }, this), result.medical_topics.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 0.5\n              },\n              children: [result.medical_topics.slice(0, 5).map((topic, topicIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: topic,\n                size: \"small\",\n                variant: \"outlined\"\n              }, topicIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 27\n              }, this)), result.medical_topics.length > 5 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `+${result.medical_topics.length - 5} more`,\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                pt: 2,\n                borderTop: 1,\n                borderColor: 'divider',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [result.word_count, \" words \\u2022 \", result.char_count, \" characters\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: \"outlined\",\n                children: \"View Context\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 19\n          }, this)\n        }, result.chunk_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this), !searchResults && !qaResponse && !isSearching && !isGeneratingAnswer && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 6,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        gutterBottom: true,\n        children: \"Start your medical research\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 3\n        },\n        children: \"Search through medical literature or ask AI-powered questions to get started\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 51\n          }, this),\n          children: \"Try searching for \\\"diabetes\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(QAIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 51\n          }, this),\n          children: \"Ask \\\"What causes hypertension?\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchPage, \"HEsIULUr63mDLjXo3ZqeqS2ZG5k=\", false, function () {\n  return [useSearchParams, useNotification];\n});\n_c = SearchPage;\nexport default SearchPage;\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "Paper", "TextField", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "CircularProgress", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Checkbox", "Slide<PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Search", "SearchIcon", "FilterList", "FilterIcon", "Clear", "ClearIcon", "QuestionAnswer", "QAIcon", "MenuBook", "BookIcon", "Star", "StarIcon", "searchService", "qaService", "useNotification", "TopicCategory", "jsxDEV", "_jsxDEV", "SearchPage", "_s", "_qaResponse$confidenc", "searchParams", "setSearchParams", "showError", "showSuccess", "query", "<PERSON><PERSON><PERSON><PERSON>", "get", "searchResults", "setSearchResults", "isSearching", "setIsSearching", "searchError", "setSearchError", "qaResponse", "setQaResponse", "isGeneratingAnswer", "setIsGeneratingAnswer", "qaError", "set<PERSON>aError", "showFilters", "setShowFilters", "filters", "setFilters", "topicCategories", "scoreThreshold", "limit", "searchMode", "setSearchMode", "selectedCitation", "setSelectedCitation", "citationViewerOpen", "setCitationViewerOpen", "initialQuery", "handleSearch", "searchQuery", "queryToSearch", "trim", "searchRequest", "score_threshold", "topic_categories", "length", "undefined", "results", "search", "q", "total_results", "search_time_ms", "error", "_error$response", "_error$response$data", "errorMessage", "response", "data", "detail", "message", "handleAskQuestion", "qaRequest", "question", "context_limit", "include_citations", "askQuestion", "citations", "response_time_ms", "_error$response2", "_error$response2$data", "handleSubmit", "e", "preventDefault", "clearFilters", "Object", "values", "sx", "max<PERSON><PERSON><PERSON>", "mx", "children", "mb", "variant", "component", "gutterBottom", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "p", "onSubmit", "display", "gap", "startIcon", "onClick", "fullWidth", "value", "onChange", "target", "placeholder", "InputProps", "endAdornment", "size", "type", "disabled", "min<PERSON><PERSON><PERSON>", "justifyContent", "alignItems", "mt", "pt", "borderTop", "borderColor", "flexWrap", "multiple", "prev", "renderValue", "selected", "map", "label", "category", "checked", "indexOf", "char<PERSON>t", "toUpperCase", "slice", "_", "min", "max", "step", "marks", "valueLabelDisplay", "severity", "confidence_score", "toFixed", "lineHeight", "answer", "flexDirection", "citation", "index", "citation_order", "book_title", "relevance_score", "book_authors", "join", "page_number", "chapter_title", "cited_text", "model_used", "context_chunks_used", "textAlign", "py", "result", "flex", "book_publisher", "book_publication_year", "topic_category", "ml", "title", "fontSize", "score", "content", "medical_topics", "topic", "topicIndex", "word_count", "char_count", "chunk_id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/SearchPage.tsx"], "sourcesContent": ["/**\n * Search page component\n */\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  Button,\n  Card,\n  CardContent,\n  Chip,\n  Divider,\n  CircularProgress,\n  Alert,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Checkbox,\n  FormControlLabel,\n  Slider,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  ExpandMore as ExpandMoreIcon,\n  FilterList as FilterIcon,\n  Clear as ClearIcon,\n  QuestionAnswer as QAIcon,\n  MenuBook as BookIcon,\n  Star as StarIcon,\n} from '@mui/icons-material';\nimport { searchService } from '../services/searchService';\nimport { qaService } from '../services/qaService';\nimport { useNotification } from '../contexts/NotificationContext';\nimport CitationViewer from '../components/Citations/CitationViewer';\nimport { SearchRequest, SearchResponse, QARequest, QAResponse, TopicCategory, Citation } from '../types';\n\nconst SearchPage: React.FC = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const { showError, showSuccess } = useNotification();\n\n  // Search state\n  const [query, setQuery] = useState(searchParams.get('q') || '');\n  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchError, setSearchError] = useState<string | null>(null);\n\n  // Q&A state\n  const [qaResponse, setQaResponse] = useState<QAResponse | null>(null);\n  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false);\n  const [qaError, setQaError] = useState<string | null>(null);\n\n  // Filter state\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    topicCategories: [] as TopicCategory[],\n    scoreThreshold: 0.7,\n    limit: 20,\n  });\n\n  // Search mode\n  const [searchMode, setSearchMode] = useState<'search' | 'qa'>('search');\n\n  // Citation viewer state\n  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);\n  const [citationViewerOpen, setCitationViewerOpen] = useState(false);\n\n  useEffect(() => {\n    const initialQuery = searchParams.get('q');\n    if (initialQuery && initialQuery !== query) {\n      setQuery(initialQuery);\n      handleSearch(initialQuery);\n    }\n  }, [searchParams]);\n\n  const handleSearch = async (searchQuery?: string) => {\n    const queryToSearch = searchQuery || query;\n    if (!queryToSearch.trim()) {\n      showError('Please enter a search query');\n      return;\n    }\n\n    setIsSearching(true);\n    setSearchError(null);\n    setSearchResults(null);\n\n    try {\n      const searchRequest: SearchRequest = {\n        query: queryToSearch,\n        limit: filters.limit,\n        score_threshold: filters.scoreThreshold,\n        filters: {\n          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined,\n        },\n      };\n\n      const results = await searchService.search(searchRequest);\n      setSearchResults(results);\n\n      // Update URL\n      setSearchParams({ q: queryToSearch });\n\n      showSuccess(`Found ${results.total_results} results in ${results.search_time_ms}ms`);\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Search failed';\n      setSearchError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  const handleAskQuestion = async () => {\n    if (!query.trim()) {\n      showError('Please enter a question');\n      return;\n    }\n\n    setIsGeneratingAnswer(true);\n    setQaError(null);\n    setQaResponse(null);\n\n    try {\n      const qaRequest: QARequest = {\n        question: query,\n        context_limit: 5,\n        include_citations: true,\n        filters: {\n          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined,\n        },\n      };\n\n      const response = await qaService.askQuestion(qaRequest);\n      setQaResponse(response);\n\n      showSuccess(`Generated answer with ${response.citations.length} citations in ${response.response_time_ms}ms`);\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Failed to generate answer';\n      setQaError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsGeneratingAnswer(false);\n    }\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchMode === 'search') {\n      handleSearch();\n    } else {\n      handleAskQuestion();\n    }\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      topicCategories: [],\n      scoreThreshold: 0.7,\n      limit: 20,\n    });\n  };\n\n  const topicCategories = Object.values(TopicCategory);\n\n  return (\n    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n          Medical Literature Search\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Search through medical textbooks or ask AI-powered questions\n        </Typography>\n      </Box>\n\n      {/* Search Form */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Box component=\"form\" onSubmit={handleSubmit}>\n          {/* Search Mode Toggle */}\n          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>\n            <Button\n              variant={searchMode === 'search' ? 'contained' : 'outlined'}\n              startIcon={<SearchIcon />}\n              onClick={() => setSearchMode('search')}\n            >\n              Search Literature\n            </Button>\n            <Button\n              variant={searchMode === 'qa' ? 'contained' : 'outlined'}\n              startIcon={<QAIcon />}\n              onClick={() => setSearchMode('qa')}\n            >\n              Ask AI Question\n            </Button>\n          </Box>\n\n          {/* Search Input */}\n          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n            <TextField\n              fullWidth\n              value={query}\n              onChange={(e) => setQuery(e.target.value)}\n              placeholder={\n                searchMode === 'search'\n                  ? 'Search medical literature...'\n                  : 'Ask a medical question...'\n              }\n              variant=\"outlined\"\n              InputProps={{\n                endAdornment: query && (\n                  <IconButton onClick={() => setQuery('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                ),\n              }}\n            />\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              size=\"large\"\n              disabled={isSearching || isGeneratingAnswer}\n              sx={{ minWidth: 120 }}\n            >\n              {isSearching || isGeneratingAnswer ? (\n                <CircularProgress size={24} color=\"inherit\" />\n              ) : searchMode === 'search' ? (\n                'Search'\n              ) : (\n                'Ask AI'\n              )}\n            </Button>\n          </Box>\n\n          {/* Filters Toggle */}\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Button\n              startIcon={<FilterIcon />}\n              onClick={() => setShowFilters(!showFilters)}\n              variant=\"text\"\n            >\n              {showFilters ? 'Hide Filters' : 'Show Filters'}\n            </Button>\n            {(filters.topicCategories.length > 0 || filters.scoreThreshold !== 0.7 || filters.limit !== 20) && (\n              <Button onClick={clearFilters} size=\"small\" color=\"secondary\">\n                Clear Filters\n              </Button>\n            )}\n          </Box>\n        </Box>\n\n        {/* Filters */}\n        {showFilters && (\n          <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Search Filters\n            </Typography>\n\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n              {/* Topic Categories */}\n              <FormControl sx={{ minWidth: 200 }}>\n                <InputLabel>Topic Categories</InputLabel>\n                <Select\n                  multiple\n                  value={filters.topicCategories}\n                  onChange={(e) => setFilters(prev => ({\n                    ...prev,\n                    topicCategories: e.target.value as TopicCategory[]\n                  }))}\n                  renderValue={(selected) => (\n                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                      {selected.map((value) => (\n                        <Chip key={value} label={value} size=\"small\" />\n                      ))}\n                    </Box>\n                  )}\n                >\n                  {topicCategories.map((category) => (\n                    <MenuItem key={category} value={category}>\n                      <Checkbox checked={filters.topicCategories.indexOf(category) > -1} />\n                      {category.charAt(0).toUpperCase() + category.slice(1)}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n\n              {/* Score Threshold */}\n              <Box sx={{ minWidth: 200 }}>\n                <Typography gutterBottom>\n                  Relevance Threshold: {filters.scoreThreshold}\n                </Typography>\n                <Slider\n                  value={filters.scoreThreshold}\n                  onChange={(_, value) => setFilters(prev => ({\n                    ...prev,\n                    scoreThreshold: value as number\n                  }))}\n                  min={0.1}\n                  max={1.0}\n                  step={0.1}\n                  marks\n                  valueLabelDisplay=\"auto\"\n                />\n              </Box>\n\n              {/* Result Limit */}\n              <FormControl sx={{ minWidth: 120 }}>\n                <InputLabel>Results Limit</InputLabel>\n                <Select\n                  value={filters.limit}\n                  onChange={(e) => setFilters(prev => ({\n                    ...prev,\n                    limit: e.target.value as number\n                  }))}\n                >\n                  <MenuItem value={10}>10</MenuItem>\n                  <MenuItem value={20}>20</MenuItem>\n                  <MenuItem value={50}>50</MenuItem>\n                  <MenuItem value={100}>100</MenuItem>\n                </Select>\n              </FormControl>\n            </Box>\n          </Box>\n        )}\n      </Paper>\n\n      {/* Error Display */}\n      {(searchError || qaError) && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {searchError || qaError}\n        </Alert>\n      )}\n\n      {/* Q&A Response */}\n      {qaResponse && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <QAIcon color=\"primary\" />\n            AI Answer\n            <Chip\n              label={`${qaResponse.confidence_score?.toFixed(2) || 'N/A'} confidence`}\n              size=\"small\"\n              color=\"primary\"\n              variant=\"outlined\"\n            />\n          </Typography>\n\n          <Typography variant=\"body1\" sx={{ mb: 3, lineHeight: 1.7 }}>\n            {qaResponse.answer}\n          </Typography>\n\n          {qaResponse.citations.length > 0 && (\n            <Box>\n              <Typography variant=\"h6\" gutterBottom>\n                Citations ({qaResponse.citations.length})\n              </Typography>\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                {qaResponse.citations.map((citation, index) => (\n                  <Card key={index} variant=\"outlined\">\n                    <CardContent sx={{ p: 2 }}>\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>\n                        <Typography variant=\"subtitle2\" color=\"primary\">\n                          [{citation.citation_order}] {citation.book_title}\n                        </Typography>\n                        <Chip\n                          label={`${(citation.relevance_score * 100).toFixed(1)}%`}\n                          size=\"small\"\n                          color=\"secondary\"\n                        />\n                      </Box>\n                      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                        {citation.book_authors.join(', ')} • Page {citation.page_number}\n                        {citation.chapter_title && ` • ${citation.chapter_title}`}\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        {citation.cited_text}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                ))}\n              </Box>\n            </Box>\n          )}\n\n          <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Generated in {qaResponse.response_time_ms}ms using {qaResponse.model_used}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {qaResponse.context_chunks_used} context chunks used\n            </Typography>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Search Results */}\n      {searchResults && (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              <BookIcon color=\"primary\" />\n              Search Results ({searchResults.total_results})\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Found in {searchResults.search_time_ms}ms\n            </Typography>\n          </Box>\n\n          {searchResults.results.length === 0 ? (\n            <Box sx={{ textAlign: 'center', py: 4 }}>\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                No results found\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Try adjusting your search terms or filters\n              </Typography>\n            </Box>\n          ) : (\n            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n              {searchResults.results.map((result, index) => (\n                <Card key={result.chunk_id} variant=\"outlined\">\n                  <CardContent>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n                      <Box sx={{ flex: 1 }}>\n                        <Typography variant=\"h6\" color=\"primary\" gutterBottom>\n                          {result.book_title}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                          {result.book_authors.join(', ')}\n                          {result.book_publisher && ` • ${result.book_publisher}`}\n                          {result.book_publication_year && ` • ${result.book_publication_year}`}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                          Page {result.page_number}\n                          {result.chapter_title && ` • ${result.chapter_title}`}\n                          {result.topic_category && (\n                            <Chip\n                              label={result.topic_category}\n                              size=\"small\"\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Typography>\n                      </Box>\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <Tooltip title=\"Relevance Score\">\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                            <StarIcon color=\"primary\" fontSize=\"small\" />\n                            <Typography variant=\"body2\" color=\"primary\" sx={{ fontWeight: 'bold' }}>\n                              {(result.score * 100).toFixed(1)}%\n                            </Typography>\n                          </Box>\n                        </Tooltip>\n                      </Box>\n                    </Box>\n\n                    <Typography variant=\"body1\" sx={{ lineHeight: 1.6 }}>\n                      {result.content}\n                    </Typography>\n\n                    {result.medical_topics.length > 0 && (\n                      <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                        {result.medical_topics.slice(0, 5).map((topic, topicIndex) => (\n                          <Chip\n                            key={topicIndex}\n                            label={topic}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                        ))}\n                        {result.medical_topics.length > 5 && (\n                          <Chip\n                            label={`+${result.medical_topics.length - 5} more`}\n                            size=\"small\"\n                            variant=\"outlined\"\n                            color=\"secondary\"\n                          />\n                        )}\n                      </Box>\n                    )}\n\n                    <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {result.word_count} words • {result.char_count} characters\n                      </Typography>\n                      <Button size=\"small\" variant=\"outlined\">\n                        View Context\n                      </Button>\n                    </Box>\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n          )}\n        </Paper>\n      )}\n\n      {/* Empty State */}\n      {!searchResults && !qaResponse && !isSearching && !isGeneratingAnswer && (\n        <Paper sx={{ p: 6, textAlign: 'center' }}>\n          <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n            Start your medical research\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Search through medical literature or ask AI-powered questions to get started\n          </Typography>\n          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>\n            <Button variant=\"outlined\" startIcon={<SearchIcon />}>\n              Try searching for \"diabetes\"\n            </Button>\n            <Button variant=\"outlined\" startIcon={<QAIcon />}>\n              Ask \"What causes hypertension?\"\n            </Button>\n          </Box>\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default SearchPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,IAAI,EAEJC,gBAAgB,EAChBC,KAAK,EAILC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EAERC,MAAM,EACNC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EAEpBC,UAAU,IAAIC,UAAU,EACxBC,KAAK,IAAIC,SAAS,EAClBC,cAAc,IAAIC,MAAM,EACxBC,QAAQ,IAAIC,QAAQ,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,eAAe,QAAQ,iCAAiC;AAEjE,SAA+DC,aAAa,QAAkB,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzG,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACjC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,eAAe,CAAC,CAAC;EACzD,MAAM;IAAE0C,SAAS;IAAEC;EAAY,CAAC,GAAGV,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC0C,YAAY,CAACM,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;EAC/D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAwB,IAAI,CAAC;EAC/E,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;;EAEnE;EACA,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAoB,IAAI,CAAC;EACrE,MAAM,CAACyD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC6D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC;IACrCiE,eAAe,EAAE,EAAqB;IACtCC,cAAc,EAAE,GAAG;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAkB,QAAQ,CAAC;;EAEvE;EACA,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAkB,IAAI,CAAC;EAC/E,MAAM,CAACwE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd,MAAMyE,YAAY,GAAGhC,YAAY,CAACM,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI0B,YAAY,IAAIA,YAAY,KAAK5B,KAAK,EAAE;MAC1CC,QAAQ,CAAC2B,YAAY,CAAC;MACtBC,YAAY,CAACD,YAAY,CAAC;IAC5B;EACF,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;EAElB,MAAMiC,YAAY,GAAG,MAAOC,WAAoB,IAAK;IACnD,MAAMC,aAAa,GAAGD,WAAW,IAAI9B,KAAK;IAC1C,IAAI,CAAC+B,aAAa,CAACC,IAAI,CAAC,CAAC,EAAE;MACzBlC,SAAS,CAAC,6BAA6B,CAAC;MACxC;IACF;IAEAQ,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IACpBJ,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF,MAAM6B,aAA4B,GAAG;QACnCjC,KAAK,EAAE+B,aAAa;QACpBV,KAAK,EAAEJ,OAAO,CAACI,KAAK;QACpBa,eAAe,EAAEjB,OAAO,CAACG,cAAc;QACvCH,OAAO,EAAE;UACPkB,gBAAgB,EAAElB,OAAO,CAACE,eAAe,CAACiB,MAAM,GAAG,CAAC,GAAGnB,OAAO,CAACE,eAAe,GAAGkB;QACnF;MACF,CAAC;MAED,MAAMC,OAAO,GAAG,MAAMnD,aAAa,CAACoD,MAAM,CAACN,aAAa,CAAC;MACzD7B,gBAAgB,CAACkC,OAAO,CAAC;;MAEzB;MACAzC,eAAe,CAAC;QAAE2C,CAAC,EAAET;MAAc,CAAC,CAAC;MAErChC,WAAW,CAAC,SAASuC,OAAO,CAACG,aAAa,eAAeH,OAAO,CAACI,cAAc,IAAI,CAAC;IACtF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAD,KAAK,CAACI,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAIN,KAAK,CAACO,OAAO,IAAI,eAAe;MACrF1C,cAAc,CAACsC,YAAY,CAAC;MAC5BhD,SAAS,CAACgD,YAAY,CAAC;IACzB,CAAC,SAAS;MACRxC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM6C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACnD,KAAK,CAACgC,IAAI,CAAC,CAAC,EAAE;MACjBlC,SAAS,CAAC,yBAAyB,CAAC;MACpC;IACF;IAEAc,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,UAAU,CAAC,IAAI,CAAC;IAChBJ,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF,MAAM0C,SAAoB,GAAG;QAC3BC,QAAQ,EAAErD,KAAK;QACfsD,aAAa,EAAE,CAAC;QAChBC,iBAAiB,EAAE,IAAI;QACvBtC,OAAO,EAAE;UACPkB,gBAAgB,EAAElB,OAAO,CAACE,eAAe,CAACiB,MAAM,GAAG,CAAC,GAAGnB,OAAO,CAACE,eAAe,GAAGkB;QACnF;MACF,CAAC;MAED,MAAMU,QAAQ,GAAG,MAAM3D,SAAS,CAACoE,WAAW,CAACJ,SAAS,CAAC;MACvD1C,aAAa,CAACqC,QAAQ,CAAC;MAEvBhD,WAAW,CAAC,yBAAyBgD,QAAQ,CAACU,SAAS,CAACrB,MAAM,iBAAiBW,QAAQ,CAACW,gBAAgB,IAAI,CAAC;IAC/G,CAAC,CAAC,OAAOf,KAAU,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACnB,MAAMd,YAAY,GAAG,EAAAa,gBAAA,GAAAhB,KAAK,CAACI,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBX,MAAM,KAAIN,KAAK,CAACO,OAAO,IAAI,2BAA2B;MACjGpC,UAAU,CAACgC,YAAY,CAAC;MACxBhD,SAAS,CAACgD,YAAY,CAAC;IACzB,CAAC,SAAS;MACRlC,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMiD,YAAY,GAAIC,CAAkB,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIzC,UAAU,KAAK,QAAQ,EAAE;MAC3BO,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLsB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB9C,UAAU,CAAC;MACTC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,GAAG;MACnBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAMF,eAAe,GAAG8C,MAAM,CAACC,MAAM,CAAC5E,aAAa,CAAC;EAEpD,oBACEE,OAAA,CAACnC,GAAG;IAAC8G,EAAE,EAAE;MAAEC,QAAQ,EAAE,IAAI;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEtC9E,OAAA,CAACnC,GAAG;MAAC8G,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjB9E,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACP,EAAE,EAAE;UAAEQ,UAAU,EAAE;QAAO,CAAE;QAAAL,QAAA,EAAC;MAEjF;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,IAAI;QAACQ,KAAK,EAAC,gBAAgB;QAAAV,QAAA,EAAC;MAEhD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNvF,OAAA,CAACjC,KAAK;MAAC4G,EAAE,EAAE;QAAEc,CAAC,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzB9E,OAAA,CAACnC,GAAG;QAACoH,SAAS,EAAC,MAAM;QAACS,QAAQ,EAAErB,YAAa;QAAAS,QAAA,gBAE3C9E,OAAA,CAACnC,GAAG;UAAC8G,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC1C9E,OAAA,CAAC/B,MAAM;YACL+G,OAAO,EAAElD,UAAU,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;YAC5D+D,SAAS,eAAE7F,OAAA,CAAChB,UAAU;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BO,OAAO,EAAEA,CAAA,KAAM/D,aAAa,CAAC,QAAQ,CAAE;YAAA+C,QAAA,EACxC;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvF,OAAA,CAAC/B,MAAM;YACL+G,OAAO,EAAElD,UAAU,KAAK,IAAI,GAAG,WAAW,GAAG,UAAW;YACxD+D,SAAS,eAAE7F,OAAA,CAACV,MAAM;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtBO,OAAO,EAAEA,CAAA,KAAM/D,aAAa,CAAC,IAAI,CAAE;YAAA+C,QAAA,EACpC;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNvF,OAAA,CAACnC,GAAG;UAAC8G,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC1C9E,OAAA,CAAChC,SAAS;YACR+H,SAAS;YACTC,KAAK,EAAExF,KAAM;YACbyF,QAAQ,EAAG3B,CAAC,IAAK7D,QAAQ,CAAC6D,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;YAC1CG,WAAW,EACTrE,UAAU,KAAK,QAAQ,GACnB,8BAA8B,GAC9B,2BACL;YACDkD,OAAO,EAAC,UAAU;YAClBoB,UAAU,EAAE;cACVC,YAAY,EAAE7F,KAAK,iBACjBR,OAAA,CAACnB,UAAU;gBAACiH,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,EAAE,CAAE;gBAAC6F,IAAI,EAAC,OAAO;gBAAAxB,QAAA,eACnD9E,OAAA,CAACZ,SAAS;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAEhB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFvF,OAAA,CAAC/B,MAAM;YACLsI,IAAI,EAAC,QAAQ;YACbvB,OAAO,EAAC,WAAW;YACnBsB,IAAI,EAAC,OAAO;YACZE,QAAQ,EAAE3F,WAAW,IAAIM,kBAAmB;YAC5CwD,EAAE,EAAE;cAAE8B,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,EAErBjE,WAAW,IAAIM,kBAAkB,gBAChCnB,OAAA,CAAC3B,gBAAgB;cAACiI,IAAI,EAAE,EAAG;cAACd,KAAK,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAC5CzD,UAAU,KAAK,QAAQ,GACzB,QAAQ,GAER;UACD;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNvF,OAAA,CAACnC,GAAG;UAAC8G,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEe,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBAClF9E,OAAA,CAAC/B,MAAM;YACL4H,SAAS,eAAE7F,OAAA,CAACd,UAAU;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BO,OAAO,EAAEA,CAAA,KAAMtE,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CyD,OAAO,EAAC,MAAM;YAAAF,QAAA,EAEbvD,WAAW,GAAG,cAAc,GAAG;UAAc;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACR,CAAC9D,OAAO,CAACE,eAAe,CAACiB,MAAM,GAAG,CAAC,IAAInB,OAAO,CAACG,cAAc,KAAK,GAAG,IAAIH,OAAO,CAACI,KAAK,KAAK,EAAE,kBAC5F7B,OAAA,CAAC/B,MAAM;YAAC6H,OAAO,EAAEtB,YAAa;YAAC8B,IAAI,EAAC,OAAO;YAACd,KAAK,EAAC,WAAW;YAAAV,QAAA,EAAC;UAE9D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhE,WAAW,iBACVvB,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAEiC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAjC,QAAA,gBAC9D9E,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAJ,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbvF,OAAA,CAACnC,GAAG;UAAC8G,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEqB,QAAQ,EAAE,MAAM;YAAEpB,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAErD9E,OAAA,CAACzB,WAAW;YAACoG,EAAE,EAAE;cAAE8B,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,gBACjC9E,OAAA,CAACxB,UAAU;cAAAsG,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzCvF,OAAA,CAACvB,MAAM;cACLwI,QAAQ;cACRjB,KAAK,EAAEvE,OAAO,CAACE,eAAgB;cAC/BsE,QAAQ,EAAG3B,CAAC,IAAK5C,UAAU,CAACwF,IAAI,KAAK;gBACnC,GAAGA,IAAI;gBACPvF,eAAe,EAAE2C,CAAC,CAAC4B,MAAM,CAACF;cAC5B,CAAC,CAAC,CAAE;cACJmB,WAAW,EAAGC,QAAQ,iBACpBpH,OAAA,CAACnC,GAAG;gBAAC8G,EAAE,EAAE;kBAAEgB,OAAO,EAAE,MAAM;kBAAEqB,QAAQ,EAAE,MAAM;kBAAEpB,GAAG,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EACtDsC,QAAQ,CAACC,GAAG,CAAErB,KAAK,iBAClBhG,OAAA,CAAC5B,IAAI;kBAAakJ,KAAK,EAAEtB,KAAM;kBAACM,IAAI,EAAC;gBAAO,GAAjCN,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA8B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACL;cAAAT,QAAA,EAEDnD,eAAe,CAAC0F,GAAG,CAAEE,QAAQ,iBAC5BvH,OAAA,CAACtB,QAAQ;gBAAgBsH,KAAK,EAAEuB,QAAS;gBAAAzC,QAAA,gBACvC9E,OAAA,CAACrB,QAAQ;kBAAC6I,OAAO,EAAE/F,OAAO,CAACE,eAAe,CAAC8F,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpEgC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC;cAAA,GAFxCL,QAAQ;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGb,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGdvF,OAAA,CAACnC,GAAG;YAAC8G,EAAE,EAAE;cAAE8B,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,gBACzB9E,OAAA,CAAClC,UAAU;cAACoH,YAAY;cAAAJ,QAAA,GAAC,uBACF,EAACrD,OAAO,CAACG,cAAc;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbvF,OAAA,CAACpB,MAAM;cACLoH,KAAK,EAAEvE,OAAO,CAACG,cAAe;cAC9BqE,QAAQ,EAAEA,CAAC4B,CAAC,EAAE7B,KAAK,KAAKtE,UAAU,CAACwF,IAAI,KAAK;gBAC1C,GAAGA,IAAI;gBACPtF,cAAc,EAAEoE;cAClB,CAAC,CAAC,CAAE;cACJ8B,GAAG,EAAE,GAAI;cACTC,GAAG,EAAE,GAAI;cACTC,IAAI,EAAE,GAAI;cACVC,KAAK;cACLC,iBAAiB,EAAC;YAAM;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNvF,OAAA,CAACzB,WAAW;YAACoG,EAAE,EAAE;cAAE8B,QAAQ,EAAE;YAAI,CAAE;YAAA3B,QAAA,gBACjC9E,OAAA,CAACxB,UAAU;cAAAsG,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtCvF,OAAA,CAACvB,MAAM;cACLuH,KAAK,EAAEvE,OAAO,CAACI,KAAM;cACrBoE,QAAQ,EAAG3B,CAAC,IAAK5C,UAAU,CAACwF,IAAI,KAAK;gBACnC,GAAGA,IAAI;gBACPrF,KAAK,EAAEyC,CAAC,CAAC4B,MAAM,CAACF;cAClB,CAAC,CAAC,CAAE;cAAAlB,QAAA,gBAEJ9E,OAAA,CAACtB,QAAQ;gBAACsH,KAAK,EAAE,EAAG;gBAAAlB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCvF,OAAA,CAACtB,QAAQ;gBAACsH,KAAK,EAAE,EAAG;gBAAAlB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCvF,OAAA,CAACtB,QAAQ;gBAACsH,KAAK,EAAE,EAAG;gBAAAlB,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCvF,OAAA,CAACtB,QAAQ;gBAACsH,KAAK,EAAE,GAAI;gBAAAlB,QAAA,EAAC;cAAG;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGP,CAACxE,WAAW,IAAIM,OAAO,kBACtBrB,OAAA,CAAC1B,KAAK;MAAC6J,QAAQ,EAAC,OAAO;MAACxD,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACnC/D,WAAW,IAAIM;IAAO;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACR,EAGAtE,UAAU,iBACTjB,OAAA,CAACjC,KAAK;MAAC4G,EAAE,EAAE;QAAEc,CAAC,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzB9E,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,IAAI;QAACE,YAAY;QAACP,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEgB,UAAU,EAAE,QAAQ;UAAEf,GAAG,EAAE;QAAE,CAAE;QAAAd,QAAA,gBAC1F9E,OAAA,CAACV,MAAM;UAACkG,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAE1B,eAAAvF,OAAA,CAAC5B,IAAI;UACHkJ,KAAK,EAAE,GAAG,EAAAnH,qBAAA,GAAAc,UAAU,CAACmH,gBAAgB,cAAAjI,qBAAA,uBAA3BA,qBAAA,CAA6BkI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,aAAc;UACxE/B,IAAI,EAAC,OAAO;UACZd,KAAK,EAAC,SAAS;UACfR,OAAO,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAEbvF,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,OAAO;QAACL,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEuD,UAAU,EAAE;QAAI,CAAE;QAAAxD,QAAA,EACxD7D,UAAU,CAACsH;MAAM;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAEZtE,UAAU,CAACgD,SAAS,CAACrB,MAAM,GAAG,CAAC,iBAC9B5C,OAAA,CAACnC,GAAG;QAAAiH,QAAA,gBACF9E,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAJ,QAAA,GAAC,aACzB,EAAC7D,UAAU,CAACgD,SAAS,CAACrB,MAAM,EAAC,GAC1C;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAACnC,GAAG;UAAC8G,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAE6C,aAAa,EAAE,QAAQ;YAAE5C,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,EAC3D7D,UAAU,CAACgD,SAAS,CAACoD,GAAG,CAAC,CAACoB,QAAQ,EAAEC,KAAK,kBACxC1I,OAAA,CAAC9B,IAAI;YAAa8G,OAAO,EAAC,UAAU;YAAAF,QAAA,eAClC9E,OAAA,CAAC7B,WAAW;cAACwG,EAAE,EAAE;gBAAEc,CAAC,EAAE;cAAE,CAAE;cAAAX,QAAA,gBACxB9E,OAAA,CAACnC,GAAG;gBAAC8G,EAAE,EAAE;kBAAEgB,OAAO,EAAE,MAAM;kBAAEe,cAAc,EAAE,eAAe;kBAAEC,UAAU,EAAE,YAAY;kBAAE5B,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,gBAC7F9E,OAAA,CAAClC,UAAU;kBAACkH,OAAO,EAAC,WAAW;kBAACQ,KAAK,EAAC,SAAS;kBAAAV,QAAA,GAAC,GAC7C,EAAC2D,QAAQ,CAACE,cAAc,EAAC,IAAE,EAACF,QAAQ,CAACG,UAAU;gBAAA;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbvF,OAAA,CAAC5B,IAAI;kBACHkJ,KAAK,EAAE,GAAG,CAACmB,QAAQ,CAACI,eAAe,GAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,GAAI;kBACzD/B,IAAI,EAAC,OAAO;kBACZd,KAAK,EAAC;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvF,OAAA,CAAClC,UAAU;gBAACkH,OAAO,EAAC,OAAO;gBAACQ,KAAK,EAAC,gBAAgB;gBAACb,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,GAC9D2D,QAAQ,CAACK,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,eAAQ,EAACN,QAAQ,CAACO,WAAW,EAC9DP,QAAQ,CAACQ,aAAa,IAAI,MAAMR,QAAQ,CAACQ,aAAa,EAAE;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACbvF,OAAA,CAAClC,UAAU;gBAACkH,OAAO,EAAC,OAAO;gBAAAF,QAAA,EACxB2D,QAAQ,CAACS;cAAU;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAnBLmD,KAAK;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDvF,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAEiC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEpB,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA7B,QAAA,gBACtI9E,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,SAAS;UAACQ,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,eACtC,EAAC7D,UAAU,CAACiD,gBAAgB,EAAC,WAAS,EAACjD,UAAU,CAACkI,UAAU;QAAA;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACbvF,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,SAAS;UAACQ,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GACjD7D,UAAU,CAACmI,mBAAmB,EAAC,sBAClC;QAAA;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA5E,aAAa,iBACZX,OAAA,CAACjC,KAAK;MAAC4G,EAAE,EAAE;QAAEc,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,gBAClB9E,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAE5B,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,gBACzF9E,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACL,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEgB,UAAU,EAAE,QAAQ;YAAEf,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAC7E9E,OAAA,CAACR,QAAQ;YAACgG,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBACZ,EAAC5E,aAAa,CAACsC,aAAa,EAAC,GAC/C;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,SAAS;UAACQ,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,WAC1C,EAACnE,aAAa,CAACuC,cAAc,EAAC,IACzC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAEL5E,aAAa,CAACmC,OAAO,CAACF,MAAM,KAAK,CAAC,gBACjC5C,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAE0E,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxE,QAAA,gBACtC9E,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,IAAI;UAACQ,KAAK,EAAC,gBAAgB;UAACN,YAAY;UAAAJ,QAAA,EAAC;QAE7D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAAClC,UAAU;UAACkH,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAAAV,QAAA,EAAC;QAEnD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENvF,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAE6C,aAAa,EAAE,QAAQ;UAAE5C,GAAG,EAAE;QAAE,CAAE;QAAAd,QAAA,EAC3DnE,aAAa,CAACmC,OAAO,CAACuE,GAAG,CAAC,CAACkC,MAAM,EAAEb,KAAK,kBACvC1I,OAAA,CAAC9B,IAAI;UAAuB8G,OAAO,EAAC,UAAU;UAAAF,QAAA,eAC5C9E,OAAA,CAAC7B,WAAW;YAAA2G,QAAA,gBACV9E,OAAA,CAACnC,GAAG;cAAC8G,EAAE,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,YAAY;gBAAE5B,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,gBAC7F9E,OAAA,CAACnC,GAAG;gBAAC8G,EAAE,EAAE;kBAAE6E,IAAI,EAAE;gBAAE,CAAE;gBAAA1E,QAAA,gBACnB9E,OAAA,CAAClC,UAAU;kBAACkH,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,SAAS;kBAACN,YAAY;kBAAAJ,QAAA,EAClDyE,MAAM,CAACX;gBAAU;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACbvF,OAAA,CAAClC,UAAU;kBAACkH,OAAO,EAAC,OAAO;kBAACQ,KAAK,EAAC,gBAAgB;kBAACb,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,GAC9DyE,MAAM,CAACT,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,EAC9BQ,MAAM,CAACE,cAAc,IAAI,MAAMF,MAAM,CAACE,cAAc,EAAE,EACtDF,MAAM,CAACG,qBAAqB,IAAI,MAAMH,MAAM,CAACG,qBAAqB,EAAE;gBAAA;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACbvF,OAAA,CAAClC,UAAU;kBAACkH,OAAO,EAAC,OAAO;kBAACQ,KAAK,EAAC,gBAAgB;kBAACb,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE,CAAE;kBAAAD,QAAA,GAAC,OAC3D,EAACyE,MAAM,CAACP,WAAW,EACvBO,MAAM,CAACN,aAAa,IAAI,MAAMM,MAAM,CAACN,aAAa,EAAE,EACpDM,MAAM,CAACI,cAAc,iBACpB3J,OAAA,CAAC5B,IAAI;oBACHkJ,KAAK,EAAEiC,MAAM,CAACI,cAAe;oBAC7BrD,IAAI,EAAC,OAAO;oBACZ3B,EAAE,EAAE;sBAAEiF,EAAE,EAAE;oBAAE;kBAAE;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACnC,GAAG;gBAAC8G,EAAE,EAAE;kBAAEgB,OAAO,EAAE,MAAM;kBAAEgB,UAAU,EAAE,QAAQ;kBAAEf,GAAG,EAAE;gBAAE,CAAE;gBAAAd,QAAA,eACzD9E,OAAA,CAAClB,OAAO;kBAAC+K,KAAK,EAAC,iBAAiB;kBAAA/E,QAAA,eAC9B9E,OAAA,CAACnC,GAAG;oBAAC8G,EAAE,EAAE;sBAAEgB,OAAO,EAAE,MAAM;sBAAEgB,UAAU,EAAE,QAAQ;sBAAEf,GAAG,EAAE;oBAAI,CAAE;oBAAAd,QAAA,gBAC3D9E,OAAA,CAACN,QAAQ;sBAAC8F,KAAK,EAAC,SAAS;sBAACsE,QAAQ,EAAC;oBAAO;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7CvF,OAAA,CAAClC,UAAU;sBAACkH,OAAO,EAAC,OAAO;sBAACQ,KAAK,EAAC,SAAS;sBAACb,EAAE,EAAE;wBAAEQ,UAAU,EAAE;sBAAO,CAAE;sBAAAL,QAAA,GACpE,CAACyE,MAAM,CAACQ,KAAK,GAAG,GAAG,EAAE1B,OAAO,CAAC,CAAC,CAAC,EAAC,GACnC;oBAAA;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvF,OAAA,CAAClC,UAAU;cAACkH,OAAO,EAAC,OAAO;cAACL,EAAE,EAAE;gBAAE2D,UAAU,EAAE;cAAI,CAAE;cAAAxD,QAAA,EACjDyE,MAAM,CAACS;YAAO;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAEZgE,MAAM,CAACU,cAAc,CAACrH,MAAM,GAAG,CAAC,iBAC/B5C,OAAA,CAACnC,GAAG;cAAC8G,EAAE,EAAE;gBAAEiC,EAAE,EAAE,CAAC;gBAAEjB,OAAO,EAAE,MAAM;gBAAEqB,QAAQ,EAAE,MAAM;gBAAEpB,GAAG,EAAE;cAAI,CAAE;cAAAd,QAAA,GAC7DyE,MAAM,CAACU,cAAc,CAACrC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACP,GAAG,CAAC,CAAC6C,KAAK,EAAEC,UAAU,kBACvDnK,OAAA,CAAC5B,IAAI;gBAEHkJ,KAAK,EAAE4C,KAAM;gBACb5D,IAAI,EAAC,OAAO;gBACZtB,OAAO,EAAC;cAAU,GAHbmF,UAAU;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIhB,CACF,CAAC,EACDgE,MAAM,CAACU,cAAc,CAACrH,MAAM,GAAG,CAAC,iBAC/B5C,OAAA,CAAC5B,IAAI;gBACHkJ,KAAK,EAAE,IAAIiC,MAAM,CAACU,cAAc,CAACrH,MAAM,GAAG,CAAC,OAAQ;gBACnD0D,IAAI,EAAC,OAAO;gBACZtB,OAAO,EAAC,UAAU;gBAClBQ,KAAK,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAEDvF,OAAA,CAACnC,GAAG;cAAC8G,EAAE,EAAE;gBAAEiC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,SAAS,EAAE,CAAC;gBAAEC,WAAW,EAAE,SAAS;gBAAEpB,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAA7B,QAAA,gBACtI9E,OAAA,CAAClC,UAAU;gBAACkH,OAAO,EAAC,SAAS;gBAACQ,KAAK,EAAC,gBAAgB;gBAAAV,QAAA,GACjDyE,MAAM,CAACa,UAAU,EAAC,gBAAS,EAACb,MAAM,CAACc,UAAU,EAAC,aACjD;cAAA;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvF,OAAA,CAAC/B,MAAM;gBAACqI,IAAI,EAAC,OAAO;gBAACtB,OAAO,EAAC,UAAU;gBAAAF,QAAA,EAAC;cAExC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GArELgE,MAAM,CAACe,QAAQ;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEpB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR,EAGA,CAAC5E,aAAa,IAAI,CAACM,UAAU,IAAI,CAACJ,WAAW,IAAI,CAACM,kBAAkB,iBACnEnB,OAAA,CAACjC,KAAK;MAAC4G,EAAE,EAAE;QAAEc,CAAC,EAAE,CAAC;QAAE4D,SAAS,EAAE;MAAS,CAAE;MAAAvE,QAAA,gBACvC9E,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,IAAI;QAACQ,KAAK,EAAC,gBAAgB;QAACN,YAAY;QAAAJ,QAAA,EAAC;MAE7D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAAClC,UAAU;QAACkH,OAAO,EAAC,OAAO;QAACQ,KAAK,EAAC,gBAAgB;QAACb,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAD,QAAA,EAAC;MAElE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvF,OAAA,CAACnC,GAAG;QAAC8G,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,QAAQ;UAAEd,GAAG,EAAE;QAAE,CAAE;QAAAd,QAAA,gBAC7D9E,OAAA,CAAC/B,MAAM;UAAC+G,OAAO,EAAC,UAAU;UAACa,SAAS,eAAE7F,OAAA,CAAChB,UAAU;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,EAAC;QAEtD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvF,OAAA,CAAC/B,MAAM;UAAC+G,OAAO,EAAC,UAAU;UAACa,SAAS,eAAE7F,OAAA,CAACV,MAAM;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAT,QAAA,EAAC;QAElD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrF,EAAA,CAjeID,UAAoB;EAAA,QACgBrC,eAAe,EACpBiC,eAAe;AAAA;AAAA0K,EAAA,GAF9CtK,UAAoB;AAme1B,eAAeA,UAAU;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}