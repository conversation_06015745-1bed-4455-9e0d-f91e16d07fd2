{"ast": null, "code": "/**\n * Q&A service for API calls\n */\nimport axios from 'axios';\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nclass QAService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: `${API_URL}/api/v1`,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Add request interceptor to include auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n  }\n  async askQuestion(qaRequest) {\n    const response = await this.api.post('/qa/ask', qaRequest);\n    return response.data;\n  }\n  async getCitationDetails(citationId) {\n    const response = await this.api.get(`/qa/citation/${citationId}`);\n    return response.data;\n  }\n  async getQAHistory(limit = 50) {\n    const response = await this.api.get('/qa/history', {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  }\n  async submitFeedback(answerId, rating, feedbackText, helpfulCitations) {\n    const response = await this.api.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations\n    });\n    return response.data;\n  }\n  async getQAHealth() {\n    const response = await this.api.get('/qa/health');\n    return response.data;\n  }\n}\nexport const qaService = new QAService();", "map": {"version": 3, "names": ["axios", "API_URL", "process", "env", "REACT_APP_API_URL", "QAService", "constructor", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "askQuestion", "qaRequest", "response", "post", "data", "getCitationDetails", "citationId", "get", "getQAHistory", "limit", "params", "submitFeedback", "answerId", "rating", "feedbackText", "helpfulCitations", "answer_id", "feedback_text", "helpful_citations", "getQAHealth", "qaService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts"], "sourcesContent": ["/**\n * Q&A service for API calls\n */\nimport axios, { AxiosInstance } from 'axios';\nimport { \n  QARequest, \n  QAResponse, \n  Citation,\n  QAHistoryItem,\n  PaginatedResponse \n} from '../types';\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nclass QAService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: `${API_URL}/api/v1`,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Add request interceptor to include auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  async askQuestion(qaRequest: QARequest): Promise<QAResponse> {\n    const response = await this.api.post('/qa/ask', qaRequest);\n    return response.data;\n  }\n\n  async getCitationDetails(citationId: string): Promise<any> {\n    const response = await this.api.get(`/qa/citation/${citationId}`);\n    return response.data;\n  }\n\n  async getQAHistory(limit: number = 50): Promise<PaginatedResponse<QAHistoryItem>> {\n    const response = await this.api.get('/qa/history', {\n      params: { limit },\n    });\n    return response.data;\n  }\n\n  async submitFeedback(\n    answerId: string,\n    rating: number,\n    feedbackText?: string,\n    helpfulCitations?: string[]\n  ): Promise<any> {\n    const response = await this.api.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations,\n    });\n    return response.data;\n  }\n\n  async getQAHealth(): Promise<any> {\n    const response = await this.api.get('/qa/health');\n    return response.data;\n  }\n}\n\nexport const qaService = new QAService();\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAyB,OAAO;AAS5C,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAExE,MAAMC,SAAS,CAAC;EAGdC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;MACtBC,OAAO,EAAE,GAAGR,OAAO,SAAS;MAC5BS,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEA,MAAMG,WAAWA,CAACC,SAAoB,EAAuB;IAC3D,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkB,IAAI,CAAC,SAAS,EAAEF,SAAS,CAAC;IAC1D,OAAOC,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMC,kBAAkBA,CAACC,UAAkB,EAAgB;IACzD,MAAMJ,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACsB,GAAG,CAAC,gBAAgBD,UAAU,EAAE,CAAC;IACjE,OAAOJ,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMI,YAAYA,CAACC,KAAa,GAAG,EAAE,EAA6C;IAChF,MAAMP,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACsB,GAAG,CAAC,aAAa,EAAE;MACjDG,MAAM,EAAE;QAAED;MAAM;IAClB,CAAC,CAAC;IACF,OAAOP,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMO,cAAcA,CAClBC,QAAgB,EAChBC,MAAc,EACdC,YAAqB,EACrBC,gBAA2B,EACb;IACd,MAAMb,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACkB,IAAI,CAAC,cAAc,EAAE;MACnDa,SAAS,EAAEJ,QAAQ;MACnBC,MAAM;MACNI,aAAa,EAAEH,YAAY;MAC3BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMe,WAAWA,CAAA,EAAiB;IAChC,MAAMjB,QAAQ,GAAG,MAAM,IAAI,CAACjB,GAAG,CAACsB,GAAG,CAAC,YAAY,CAAC;IACjD,OAAOL,QAAQ,CAACE,IAAI;EACtB;AACF;AAEA,OAAO,MAAMgB,SAAS,GAAG,IAAIrC,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}