{"ast": null, "code": "/**\n * Search service for API calls\n */\n\nclass SearchService {\n  async search(searchRequest) {\n    const response = await this.api.post('/search', searchRequest);\n    return response.data;\n  }\n  async getSuggestions(query, limit = 10) {\n    const response = await this.api.get('/search/suggestions', {\n      params: {\n        q: query,\n        limit\n      }\n    });\n    return response.data.suggestions;\n  }\n  async findSimilarChunks(chunkId, limit = 10, scoreThreshold = 0.8, excludeSameBook = false) {\n    const response = await this.api.post('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook\n    });\n    return response.data;\n  }\n  async getSearchHistory(limit = 50) {\n    const response = await this.api.get('/search/history', {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  }\n  async getSearchAnalytics() {\n    const response = await this.api.get('/search/analytics');\n    return response.data;\n  }\n}\nexport const searchService = new SearchService();", "map": {"version": 3, "names": ["SearchService", "search", "searchRequest", "response", "api", "post", "data", "getSuggestions", "query", "limit", "get", "params", "q", "suggestions", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunkId", "scoreThreshold", "excludeSameBook", "chunk_id", "score_threshold", "exclude_same_book", "getSearchHistory", "getSearchAnalytics", "searchService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts"], "sourcesContent": ["/**\n * Search service for API calls\n */\nimport { apiClient } from './apiClient';\nimport {\n  SearchRequest,\n  SearchResponse,\n  SearchResult,\n  SearchHistoryItem,\n  PaginatedResponse\n} from '../types';\n\nclass SearchService {\n\n  async search(searchRequest: SearchRequest): Promise<SearchResponse> {\n    const response = await this.api.post('/search', searchRequest);\n    return response.data;\n  }\n\n  async getSuggestions(query: string, limit: number = 10): Promise<string[]> {\n    const response = await this.api.get('/search/suggestions', {\n      params: { q: query, limit },\n    });\n    return response.data.suggestions;\n  }\n\n  async findSimilarChunks(\n    chunkId: string,\n    limit: number = 10,\n    scoreThreshold: number = 0.8,\n    excludeSameBook: boolean = false\n  ): Promise<SearchResult[]> {\n    const response = await this.api.post('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook,\n    });\n    return response.data;\n  }\n\n  async getSearchHistory(limit: number = 50): Promise<PaginatedResponse<SearchHistoryItem>> {\n    const response = await this.api.get('/search/history', {\n      params: { limit },\n    });\n    return response.data;\n  }\n\n  async getSearchAnalytics(): Promise<any> {\n    const response = await this.api.get('/search/analytics');\n    return response.data;\n  }\n}\n\nexport const searchService = new SearchService();\n"], "mappings": "AAAA;AACA;AACA;;AAUA,MAAMA,aAAa,CAAC;EAElB,MAAMC,MAAMA,CAACC,aAA4B,EAA2B;IAClE,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,IAAI,CAAC,SAAS,EAAEH,aAAa,CAAC;IAC9D,OAAOC,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMC,cAAcA,CAACC,KAAa,EAAEC,KAAa,GAAG,EAAE,EAAqB;IACzE,MAAMN,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC,qBAAqB,EAAE;MACzDC,MAAM,EAAE;QAAEC,CAAC,EAAEJ,KAAK;QAAEC;MAAM;IAC5B,CAAC,CAAC;IACF,OAAON,QAAQ,CAACG,IAAI,CAACO,WAAW;EAClC;EAEA,MAAMC,iBAAiBA,CACrBC,OAAe,EACfN,KAAa,GAAG,EAAE,EAClBO,cAAsB,GAAG,GAAG,EAC5BC,eAAwB,GAAG,KAAK,EACP;IACzB,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,IAAI,CAAC,iBAAiB,EAAE;MACtDa,QAAQ,EAAEH,OAAO;MACjBN,KAAK;MACLU,eAAe,EAAEH,cAAc;MAC/BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMe,gBAAgBA,CAACZ,KAAa,GAAG,EAAE,EAAiD;IACxF,MAAMN,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC,iBAAiB,EAAE;MACrDC,MAAM,EAAE;QAAEF;MAAM;IAClB,CAAC,CAAC;IACF,OAAON,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMgB,kBAAkBA,CAAA,EAAiB;IACvC,MAAMnB,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACM,GAAG,CAAC,mBAAmB,CAAC;IACxD,OAAOP,QAAQ,CAACG,IAAI;EACtB;AACF;AAEA,OAAO,MAAMiB,aAAa,GAAG,IAAIvB,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}