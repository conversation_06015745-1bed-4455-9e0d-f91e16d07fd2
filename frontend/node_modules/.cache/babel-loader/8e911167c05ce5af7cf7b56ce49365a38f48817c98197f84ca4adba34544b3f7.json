{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Authentication context for managing user authentication state\n */\nimport React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null\n};\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.access_token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...initialState\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    case 'SET_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    default:\n      return state;\n  }\n};\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          const user = JSON.parse(userStr);\n          authService.setToken(token);\n\n          // For development: Skip API validation if backend is not available\n          // In production, you should always validate the token\n          if (process.env.NODE_ENV === 'development') {\n            dispatch({\n              type: 'AUTH_SUCCESS',\n              payload: {\n                access_token: token,\n                token_type: 'bearer',\n                expires_in: 0,\n                user: user\n              }\n            });\n          } else {\n            // Verify token is still valid by fetching user profile\n            const currentUser = await authService.getCurrentUser();\n            dispatch({\n              type: 'AUTH_SUCCESS',\n              payload: {\n                access_token: token,\n                token_type: 'bearer',\n                expires_in: 0,\n                // We don't track expiry on frontend\n                user: currentUser\n              }\n            });\n          }\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          authService.clearToken();\n          dispatch({\n            type: 'AUTH_FAILURE',\n            payload: 'Session expired'\n          });\n        }\n      } else {\n        // No token found, set loading to false\n        dispatch({\n          type: 'AUTH_FAILURE',\n          payload: 'No authentication token found'\n        });\n      }\n    };\n    initializeAuth();\n  }, []);\n  const login = async credentials => {\n    dispatch({\n      type: 'AUTH_START'\n    });\n    try {\n      const response = await authService.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n\n      // Set token in service\n      authService.setToken(response.access_token);\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: response\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      let errorMessage = 'Login failed';\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your connection.';\n      } else if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n  const signup = async userData => {\n    dispatch({\n      type: 'AUTH_START'\n    });\n    try {\n      await authService.signup(userData);\n      // After successful signup, automatically log in\n      await login({\n        email: userData.email,\n        password: userData.password\n      });\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      let errorMessage = 'Signup failed';\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your connection.';\n      } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Clear token in service\n    authService.clearToken();\n    dispatch({\n      type: 'AUTH_LOGOUT'\n    });\n  };\n  const clearError = useCallback(() => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  }, []);\n  const refreshUser = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      dispatch({\n        type: 'SET_USER',\n        payload: user\n      });\n      localStorage.setItem('user', JSON.stringify(user));\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n    }\n  };\n  const value = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    refreshUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 10\n  }, this);\n};\n_s(AuthProvider, \"niHzHzaXBzPJarxOGfR8+jkIn7U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "useCallback", "authService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "access_token", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "localStorage", "getItem", "userStr", "JSON", "parse", "setToken", "process", "env", "NODE_ENV", "token_type", "expires_in", "currentUser", "getCurrentUser", "removeItem", "clearToken", "login", "credentials", "response", "setItem", "stringify", "_error$response", "_error$response$data", "errorMessage", "status", "data", "detail", "message", "signup", "userData", "email", "password", "_error$response2", "_error$response2$data", "logout", "clearError", "refreshUser", "console", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["/**\n * Authentication context for managing user authentication state\n */\nimport React, { create<PERSON>ontext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';\nimport { User, AuthResponse, LoginRequest, SignupRequest } from '../types';\nimport { authService } from '../services/authService';\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginRequest) => Promise<void>;\n  signup: (userData: SignupRequest) => Promise<void>;\n  logout: () => void;\n  clearError: () => void;\n  refreshUser: () => Promise<void>;\n}\n\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: AuthResponse }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'CLEAR_ERROR' }\n  | { type: 'SET_USER'; payload: User };\n\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.access_token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...initialState,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    case 'SET_USER':\n      return {\n        ...state,\n        user: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n\n      if (token && userStr) {\n        try {\n          const user = JSON.parse(userStr);\n          authService.setToken(token);\n\n          // For development: Skip API validation if backend is not available\n          // In production, you should always validate the token\n          if (process.env.NODE_ENV === 'development') {\n            dispatch({\n              type: 'AUTH_SUCCESS',\n              payload: {\n                access_token: token,\n                token_type: 'bearer',\n                expires_in: 0,\n                user: user,\n              },\n            });\n          } else {\n            // Verify token is still valid by fetching user profile\n            const currentUser = await authService.getCurrentUser();\n            dispatch({\n              type: 'AUTH_SUCCESS',\n              payload: {\n                access_token: token,\n                token_type: 'bearer',\n                expires_in: 0, // We don't track expiry on frontend\n                user: currentUser,\n              },\n            });\n          }\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          authService.clearToken();\n          dispatch({ type: 'AUTH_FAILURE', payload: 'Session expired' });\n        }\n      } else {\n        // No token found, set loading to false\n        dispatch({ type: 'AUTH_FAILURE', payload: 'No authentication token found' });\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  const login = async (credentials: LoginRequest) => {\n    dispatch({ type: 'AUTH_START' });\n    try {\n      const response = await authService.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n\n      // Set token in service\n      authService.setToken(response.access_token);\n\n      dispatch({ type: 'AUTH_SUCCESS', payload: response });\n    } catch (error: any) {\n      let errorMessage = 'Login failed';\n\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your connection.';\n      } else if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  const signup = async (userData: SignupRequest) => {\n    dispatch({ type: 'AUTH_START' });\n    try {\n      await authService.signup(userData);\n      // After successful signup, automatically log in\n      await login({ email: userData.email, password: userData.password });\n    } catch (error: any) {\n      let errorMessage = 'Signup failed';\n\n      if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your connection.';\n      } else if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    \n    // Clear token in service\n    authService.clearToken();\n    \n    dispatch({ type: 'AUTH_LOGOUT' });\n  };\n\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  const refreshUser = async () => {\n    try {\n      const user = await authService.getCurrentUser();\n      dispatch({ type: 'SET_USER', payload: user });\n      localStorage.setItem('user', JSON.stringify(user));\n    } catch (error) {\n      console.error('Failed to refresh user:', error);\n    }\n  };\n\n  const value: AuthContextType = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    refreshUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAmB,OAAO;AAEvG,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BtD,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE,OAAO,CAACT,IAAI;QACzBC,KAAK,EAAEM,MAAM,CAACE,OAAO,CAACC,YAAY;QAClCR,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGV;MACL,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGO,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH,KAAK,UAAU;MACb,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE;MACf,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;AAED,MAAMK,WAAW,gBAAGpB,aAAa,CAA8BqB,SAAS,CAAC;AAMzE,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACT,KAAK,EAAEU,QAAQ,CAAC,GAAGvB,UAAU,CAACY,WAAW,EAAEN,YAAY,CAAC;;EAE/D;EACAL,SAAS,CAAC,MAAM;IACd,MAAMuB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMhB,KAAK,GAAGiB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIlB,KAAK,IAAImB,OAAO,EAAE;QACpB,IAAI;UACF,MAAMpB,IAAI,GAAGqB,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;UAChCxB,WAAW,CAAC2B,QAAQ,CAACtB,KAAK,CAAC;;UAE3B;UACA;UACA,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;YAC1CV,QAAQ,CAAC;cACPR,IAAI,EAAE,cAAc;cACpBC,OAAO,EAAE;gBACPC,YAAY,EAAET,KAAK;gBACnB0B,UAAU,EAAE,QAAQ;gBACpBC,UAAU,EAAE,CAAC;gBACb5B,IAAI,EAAEA;cACR;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA,MAAM6B,WAAW,GAAG,MAAMjC,WAAW,CAACkC,cAAc,CAAC,CAAC;YACtDd,QAAQ,CAAC;cACPR,IAAI,EAAE,cAAc;cACpBC,OAAO,EAAE;gBACPC,YAAY,EAAET,KAAK;gBACnB0B,UAAU,EAAE,QAAQ;gBACpBC,UAAU,EAAE,CAAC;gBAAE;gBACf5B,IAAI,EAAE6B;cACR;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;UACd;UACAc,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;UAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;UAC/BnC,WAAW,CAACoC,UAAU,CAAC,CAAC;UACxBhB,QAAQ,CAAC;YAAER,IAAI,EAAE,cAAc;YAAEC,OAAO,EAAE;UAAkB,CAAC,CAAC;QAChE;MACF,CAAC,MAAM;QACL;QACAO,QAAQ,CAAC;UAAER,IAAI,EAAE,cAAc;UAAEC,OAAO,EAAE;QAAgC,CAAC,CAAC;MAC9E;IACF,CAAC;IAEDQ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgB,KAAK,GAAG,MAAOC,WAAyB,IAAK;IACjDlB,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAa,CAAC,CAAC;IAChC,IAAI;MACF,MAAM2B,QAAQ,GAAG,MAAMvC,WAAW,CAACqC,KAAK,CAACC,WAAW,CAAC;;MAErD;MACAhB,YAAY,CAACkB,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACzB,YAAY,CAAC;MACpDQ,YAAY,CAACkB,OAAO,CAAC,MAAM,EAAEf,IAAI,CAACgB,SAAS,CAACF,QAAQ,CAACnC,IAAI,CAAC,CAAC;;MAE3D;MACAJ,WAAW,CAAC2B,QAAQ,CAACY,QAAQ,CAACzB,YAAY,CAAC;MAE3CM,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAE0B;MAAS,CAAC,CAAC;IACvD,CAAC,CAAC,OAAO/B,KAAU,EAAE;MAAA,IAAAkC,eAAA,EAAAC,oBAAA;MACnB,IAAIC,YAAY,GAAG,cAAc;MAEjC,IAAIpC,KAAK,CAACqC,MAAM,KAAK,CAAC,EAAE;QACtBD,YAAY,GAAG,4DAA4D;MAC7E,CAAC,MAAM,KAAAF,eAAA,GAAIlC,KAAK,CAAC+B,QAAQ,cAAAG,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,eAApBA,oBAAA,CAAsBI,MAAM,EAAE;QACvCH,YAAY,GAAGpC,KAAK,CAAC+B,QAAQ,CAACO,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAIvC,KAAK,CAACwC,OAAO,EAAE;QACxBJ,YAAY,GAAGpC,KAAK,CAACwC,OAAO;MAC9B;MAEA5B,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAE+B;MAAa,CAAC,CAAC;MACzD,MAAMpC,KAAK;IACb;EACF,CAAC;EAED,MAAMyC,MAAM,GAAG,MAAOC,QAAuB,IAAK;IAChD9B,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAa,CAAC,CAAC;IAChC,IAAI;MACF,MAAMZ,WAAW,CAACiD,MAAM,CAACC,QAAQ,CAAC;MAClC;MACA,MAAMb,KAAK,CAAC;QAAEc,KAAK,EAAED,QAAQ,CAACC,KAAK;QAAEC,QAAQ,EAAEF,QAAQ,CAACE;MAAS,CAAC,CAAC;IACrE,CAAC,CAAC,OAAO5C,KAAU,EAAE;MAAA,IAAA6C,gBAAA,EAAAC,qBAAA;MACnB,IAAIV,YAAY,GAAG,eAAe;MAElC,IAAIpC,KAAK,CAACqC,MAAM,KAAK,CAAC,EAAE;QACtBD,YAAY,GAAG,4DAA4D;MAC7E,CAAC,MAAM,KAAAS,gBAAA,GAAI7C,KAAK,CAAC+B,QAAQ,cAAAc,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,eAApBA,qBAAA,CAAsBP,MAAM,EAAE;QACvCH,YAAY,GAAGpC,KAAK,CAAC+B,QAAQ,CAACO,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAIvC,KAAK,CAACwC,OAAO,EAAE;QACxBJ,YAAY,GAAGpC,KAAK,CAACwC,OAAO;MAC9B;MAEA5B,QAAQ,CAAC;QAAER,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAE+B;MAAa,CAAC,CAAC;MACzD,MAAMpC,KAAK;IACb;EACF,CAAC;EAED,MAAM+C,MAAM,GAAGA,CAAA,KAAM;IACnB;IACAjC,YAAY,CAACa,UAAU,CAAC,OAAO,CAAC;IAChCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAnC,WAAW,CAACoC,UAAU,CAAC,CAAC;IAExBhB,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;EAED,MAAM4C,UAAU,GAAGzD,WAAW,CAAC,MAAM;IACnCqB,QAAQ,CAAC;MAAER,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMrD,IAAI,GAAG,MAAMJ,WAAW,CAACkC,cAAc,CAAC,CAAC;MAC/Cd,QAAQ,CAAC;QAAER,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAET;MAAK,CAAC,CAAC;MAC7CkB,YAAY,CAACkB,OAAO,CAAC,MAAM,EAAEf,IAAI,CAACgB,SAAS,CAACrC,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdkD,OAAO,CAAClD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMmD,KAAsB,GAAG;IAC7B,GAAGjD,KAAK;IACR2B,KAAK;IACLY,MAAM;IACNM,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EAED,oBAAOvD,OAAA,CAACa,WAAW,CAAC6C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzC,QAAA,EAAEA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAC7C,EAAA,CA7IWF,YAAyC;AAAAgD,EAAA,GAAzChD,YAAyC;AA+ItD,OAAO,MAAMiD,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGxE,UAAU,CAACmB,WAAW,CAAC;EACvC,IAAIqD,OAAO,KAAKpD,SAAS,EAAE;IACzB,MAAM,IAAIqD,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}