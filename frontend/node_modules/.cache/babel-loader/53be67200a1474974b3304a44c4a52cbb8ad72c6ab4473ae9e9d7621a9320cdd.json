{"ast": null, "code": "/**\n * Q&A service for API calls\n */\n\nclass QAService {\n  async askQuestion(qaRequest) {\n    const response = await this.api.post('/qa/ask', qaRequest);\n    return response.data;\n  }\n  async getCitationDetails(citationId) {\n    const response = await this.api.get(`/qa/citation/${citationId}`);\n    return response.data;\n  }\n  async getQAHistory(limit = 50) {\n    const response = await this.api.get('/qa/history', {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  }\n  async submitFeedback(answerId, rating, feedbackText, helpfulCitations) {\n    const response = await this.api.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations\n    });\n    return response.data;\n  }\n  async getQAHealth() {\n    const response = await this.api.get('/qa/health');\n    return response.data;\n  }\n}\nexport const qaService = new QAService();", "map": {"version": 3, "names": ["QAService", "askQuestion", "qaRequest", "response", "api", "post", "data", "getCitationDetails", "citationId", "get", "getQAHistory", "limit", "params", "submitFeedback", "answerId", "rating", "feedbackText", "helpfulCitations", "answer_id", "feedback_text", "helpful_citations", "getQAHealth", "qaService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/qaService.ts"], "sourcesContent": ["/**\n * Q&A service for API calls\n */\nimport { apiClient } from './apiClient';\nimport {\n  QARequest,\n  QAResponse,\n  Citation,\n  QAHistoryItem,\n  PaginatedResponse\n} from '../types';\n\nclass QAService {\n\n  async askQuestion(qaRequest: QARequest): Promise<QAResponse> {\n    const response = await this.api.post('/qa/ask', qaRequest);\n    return response.data;\n  }\n\n  async getCitationDetails(citationId: string): Promise<any> {\n    const response = await this.api.get(`/qa/citation/${citationId}`);\n    return response.data;\n  }\n\n  async getQAHistory(limit: number = 50): Promise<PaginatedResponse<QAHistoryItem>> {\n    const response = await this.api.get('/qa/history', {\n      params: { limit },\n    });\n    return response.data;\n  }\n\n  async submitFeedback(\n    answerId: string,\n    rating: number,\n    feedbackText?: string,\n    helpfulCitations?: string[]\n  ): Promise<any> {\n    const response = await this.api.post('/qa/feedback', {\n      answer_id: answerId,\n      rating,\n      feedback_text: feedbackText,\n      helpful_citations: helpfulCitations,\n    });\n    return response.data;\n  }\n\n  async getQAHealth(): Promise<any> {\n    const response = await this.api.get('/qa/health');\n    return response.data;\n  }\n}\n\nexport const qaService = new QAService();\n"], "mappings": "AAAA;AACA;AACA;;AAUA,MAAMA,SAAS,CAAC;EAEd,MAAMC,WAAWA,CAACC,SAAoB,EAAuB;IAC3D,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,IAAI,CAAC,SAAS,EAAEH,SAAS,CAAC;IAC1D,OAAOC,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMC,kBAAkBA,CAACC,UAAkB,EAAgB;IACzD,MAAML,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACK,GAAG,CAAC,gBAAgBD,UAAU,EAAE,CAAC;IACjE,OAAOL,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMI,YAAYA,CAACC,KAAa,GAAG,EAAE,EAA6C;IAChF,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACK,GAAG,CAAC,aAAa,EAAE;MACjDG,MAAM,EAAE;QAAED;MAAM;IAClB,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMO,cAAcA,CAClBC,QAAgB,EAChBC,MAAc,EACdC,YAAqB,EACrBC,gBAA2B,EACb;IACd,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACC,IAAI,CAAC,cAAc,EAAE;MACnDa,SAAS,EAAEJ,QAAQ;MACnBC,MAAM;MACNI,aAAa,EAAEH,YAAY;MAC3BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMe,WAAWA,CAAA,EAAiB;IAChC,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACK,GAAG,CAAC,YAAY,CAAC;IACjD,OAAON,QAAQ,CAACG,IAAI;EACtB;AACF;AAEA,OAAO,MAAMgB,SAAS,GAAG,IAAItB,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}