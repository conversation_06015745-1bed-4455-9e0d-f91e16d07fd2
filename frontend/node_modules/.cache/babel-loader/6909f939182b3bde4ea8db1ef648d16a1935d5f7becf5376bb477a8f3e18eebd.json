{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx\",\n  _s = $RefreshSig$();\n/**\n * Main layout component with navigation and sidebar\n */\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Box, CssBaseline, Drawer, IconButton, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Toolbar, Typography, Avatar, Menu, MenuItem, Divider, useTheme, useMediaQuery } from '@mui/material';\nimport { Menu as MenuIcon, Home as HomeIcon, Search as SearchIcon, QuestionAnswer as QAIcon, Person as PersonIcon, AdminPanelSettings as AdminIcon, Logout as LogoutIcon, Settings as SettingsIcon, LocalHospital as TopicSearchIcon } from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNotification } from '../../contexts/NotificationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 240;\nconst navigationItems = [{\n  text: 'Home',\n  icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 25\n  }, this),\n  path: '/'\n}, {\n  text: 'Search',\n  icon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 27\n  }, this),\n  path: '/search'\n}, {\n  text: 'Topic Search',\n  icon: /*#__PURE__*/_jsxDEV(TopicSearchIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 33\n  }, this),\n  path: '/topic-search'\n}, {\n  text: 'Q&A',\n  icon: /*#__PURE__*/_jsxDEV(QAIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 24\n  }, this),\n  path: '/qa'\n}, {\n  text: 'Profile',\n  icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 28\n  }, this),\n  path: '/profile'\n}, {\n  text: 'Admin',\n  icon: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 26\n  }, this),\n  path: '/admin',\n  adminOnly: true\n}];\nconst Layout = () => {\n  _s();\n  var _user$full_name;\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    showSuccess\n  } = useNotification();\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n  const handleProfileMenuOpen = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleProfileMenuClose = () => {\n    setAnchorEl(null);\n  };\n  const handleLogout = () => {\n    logout();\n    showSuccess('Logged out successfully');\n    navigate('/login');\n    handleProfileMenuClose();\n  };\n  const handleNavigation = path => {\n    navigate(path);\n    if (isMobile) {\n      setMobileOpen(false);\n    }\n  };\n  const drawer = /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        noWrap: true,\n        component: \"div\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"MedPrep\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      children: navigationItems.filter(item => !item.adminOnly || (user === null || user === void 0 ? void 0 : user.role) === 'admin').map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n        disablePadding: true,\n        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n          selected: location.pathname === item.path,\n          onClick: () => handleNavigation(item.path),\n          sx: {\n            '&.Mui-selected': {\n              backgroundColor: theme.palette.primary.main + '20',\n              '&:hover': {\n                backgroundColor: theme.palette.primary.main + '30'\n              }\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              color: location.pathname === item.path ? theme.palette.primary.main : 'inherit'\n            },\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.text,\n            sx: {\n              '& .MuiListItemText-primary': {\n                color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',\n                fontWeight: location.pathname === item.path ? 600 : 400\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 15\n        }, this)\n      }, item.text, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        width: {\n          md: `calc(100% - ${drawerWidth}px)`\n        },\n        ml: {\n          md: `${drawerWidth}px`\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          onClick: handleDrawerToggle,\n          sx: {\n            mr: 2,\n            display: {\n              md: 'none'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          noWrap: true,\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"Medical Preparation Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              display: {\n                xs: 'none',\n                sm: 'block'\n              }\n            },\n            children: user === null || user === void 0 ? void 0 : user.full_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"large\",\n            edge: \"end\",\n            \"aria-label\": \"account of current user\",\n            \"aria-controls\": \"profile-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: handleProfileMenuOpen,\n            color: \"inherit\",\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                bgcolor: 'secondary.main'\n              },\n              children: user === null || user === void 0 ? void 0 : (_user$full_name = user.full_name) === null || _user$full_name === void 0 ? void 0 : _user$full_name.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"nav\",\n      sx: {\n        width: {\n          md: drawerWidth\n        },\n        flexShrink: {\n          md: 0\n        }\n      },\n      \"aria-label\": \"navigation\",\n      children: [/*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"temporary\",\n        open: mobileOpen,\n        onClose: handleDrawerToggle,\n        ModalProps: {\n          keepMounted: true // Better open performance on mobile.\n        },\n        sx: {\n          display: {\n            xs: 'block',\n            md: 'none'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth\n          }\n        },\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"permanent\",\n        sx: {\n          display: {\n            xs: 'none',\n            md: 'block'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth\n          }\n        },\n        open: true,\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: {\n          md: `calc(100% - ${drawerWidth}px)`\n        },\n        minHeight: '100vh',\n        backgroundColor: theme.palette.background.default\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      id: \"profile-menu\",\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleProfileMenuClose,\n      onClick: handleProfileMenuClose,\n      PaperProps: {\n        elevation: 0,\n        sx: {\n          overflow: 'visible',\n          filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\n          mt: 1.5,\n          '& .MuiAvatar-root': {\n            width: 32,\n            height: 32,\n            ml: -0.5,\n            mr: 1\n          },\n          '&:before': {\n            content: '\"\"',\n            display: 'block',\n            position: 'absolute',\n            top: 0,\n            right: 14,\n            width: 10,\n            height: 10,\n            bgcolor: 'background.paper',\n            transform: 'translateY(-50%) rotate(45deg)',\n            zIndex: 0\n          }\n        }\n      },\n      transformOrigin: {\n        horizontal: 'right',\n        vertical: 'top'\n      },\n      anchorOrigin: {\n        horizontal: 'right',\n        vertical: 'bottom'\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => navigate('/profile'),\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), \"Profile Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleLogout,\n        children: [/*#__PURE__*/_jsxDEV(LogoutIcon, {\n          sx: {\n            mr: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), \"Logout\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"AKCiIsXy+Fv7g/mMdkv0uWk0D5U=\", false, function () {\n  return [useTheme, useMediaQuery, useNavigate, useLocation, useAuth, useNotification];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "AppBar", "Box", "CssBaseline", "Drawer", "IconButton", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "Typography", "Avatar", "<PERSON><PERSON>", "MenuItem", "Divider", "useTheme", "useMediaQuery", "MenuIcon", "Home", "HomeIcon", "Search", "SearchIcon", "QuestionAnswer", "QAIcon", "Person", "PersonIcon", "AdminPanelSettings", "AdminIcon", "Logout", "LogoutIcon", "Settings", "SettingsIcon", "LocalHospital", "TopicSearchIcon", "useAuth", "useNotification", "jsxDEV", "_jsxDEV", "drawerWidth", "navigationItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "adminOnly", "Layout", "_s", "_user$full_name", "theme", "isMobile", "breakpoints", "down", "navigate", "location", "user", "logout", "showSuccess", "mobileOpen", "setMobileOpen", "anchorEl", "setAnchorEl", "handleDrawerToggle", "handleProfileMenuOpen", "event", "currentTarget", "handleProfileMenuClose", "handleLogout", "handleNavigation", "drawer", "children", "variant", "noWrap", "component", "sx", "fontWeight", "filter", "item", "role", "map", "disablePadding", "selected", "pathname", "onClick", "backgroundColor", "palette", "primary", "main", "color", "display", "position", "width", "md", "ml", "edge", "mr", "flexGrow", "alignItems", "gap", "xs", "sm", "full_name", "size", "height", "bgcolor", "char<PERSON>t", "toUpperCase", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "boxSizing", "p", "minHeight", "background", "default", "id", "Boolean", "PaperProps", "elevation", "overflow", "mt", "content", "top", "right", "transform", "zIndex", "transform<PERSON><PERSON>in", "horizontal", "vertical", "anchor<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["/**\n * Main layout component with navigation and sidebar\n */\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Box,\n  CssBaseline,\n  Drawer,\n  IconButton,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Toolbar,\n  Typography,\n  Avatar,\n  Menu,\n  MenuItem,\n  Divider,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Home as HomeIcon,\n  Search as SearchIcon,\n  QuestionAnswer as QAIcon,\n  Person as PersonIcon,\n  AdminPanelSettings as AdminIcon,\n  Logout as LogoutIcon,\n  Settings as SettingsIcon,\n  LocalHospital as TopicSearchIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useNotification } from '../../contexts/NotificationContext';\n\nconst drawerWidth = 240;\n\ninterface NavigationItem {\n  text: string;\n  icon: React.ReactElement;\n  path: string;\n  adminOnly?: boolean;\n}\n\nconst navigationItems: NavigationItem[] = [\n  { text: 'Home', icon: <HomeIcon />, path: '/' },\n  { text: 'Search', icon: <SearchIcon />, path: '/search' },\n  { text: 'Topic Search', icon: <TopicSearchIcon />, path: '/topic-search' },\n  { text: 'Q&A', icon: <QAIcon />, path: '/qa' },\n  { text: 'Profile', icon: <PersonIcon />, path: '/profile' },\n  { text: 'Admin', icon: <AdminIcon />, path: '/admin', adminOnly: true },\n];\n\nconst Layout: React.FC = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout } = useAuth();\n  const { showSuccess } = useNotification();\n\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleProfileMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleLogout = () => {\n    logout();\n    showSuccess('Logged out successfully');\n    navigate('/login');\n    handleProfileMenuClose();\n  };\n\n  const handleNavigation = (path: string) => {\n    navigate(path);\n    if (isMobile) {\n      setMobileOpen(false);\n    }\n  };\n\n  const drawer = (\n    <div>\n      <Toolbar>\n        <Typography variant=\"h6\" noWrap component=\"div\" sx={{ fontWeight: 'bold' }}>\n          MedPrep\n        </Typography>\n      </Toolbar>\n      <Divider />\n      <List>\n        {navigationItems\n          .filter(item => !item.adminOnly || user?.role === 'admin')\n          .map((item) => (\n            <ListItem key={item.text} disablePadding>\n              <ListItemButton\n                selected={location.pathname === item.path}\n                onClick={() => handleNavigation(item.path)}\n                sx={{\n                  '&.Mui-selected': {\n                    backgroundColor: theme.palette.primary.main + '20',\n                    '&:hover': {\n                      backgroundColor: theme.palette.primary.main + '30',\n                    },\n                  },\n                }}\n              >\n                <ListItemIcon\n                  sx={{\n                    color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',\n                  }}\n                >\n                  {item.icon}\n                </ListItemIcon>\n                <ListItemText \n                  primary={item.text}\n                  sx={{\n                    '& .MuiListItemText-primary': {\n                      color: location.pathname === item.path ? theme.palette.primary.main : 'inherit',\n                      fontWeight: location.pathname === item.path ? 600 : 400,\n                    },\n                  }}\n                />\n              </ListItemButton>\n            </ListItem>\n          ))}\n      </List>\n    </div>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      <CssBaseline />\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { md: `calc(100% - ${drawerWidth}px)` },\n          ml: { md: `${drawerWidth}px` },\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { md: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            Medical Preparation Platform\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <Typography variant=\"body2\" sx={{ display: { xs: 'none', sm: 'block' } }}>\n              {user?.full_name}\n            </Typography>\n            <IconButton\n              size=\"large\"\n              edge=\"end\"\n              aria-label=\"account of current user\"\n              aria-controls=\"profile-menu\"\n              aria-haspopup=\"true\"\n              onClick={handleProfileMenuOpen}\n              color=\"inherit\"\n            >\n              <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>\n                {user?.full_name?.charAt(0).toUpperCase()}\n              </Avatar>\n            </IconButton>\n          </Box>\n        </Toolbar>\n      </AppBar>\n\n      <Box\n        component=\"nav\"\n        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}\n        aria-label=\"navigation\"\n      >\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true, // Better open performance on mobile.\n          }}\n          sx={{\n            display: { xs: 'block', md: 'none' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', md: 'block' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { md: `calc(100% - ${drawerWidth}px)` },\n          minHeight: '100vh',\n          backgroundColor: theme.palette.background.default,\n        }}\n      >\n        <Toolbar />\n        <Outlet />\n      </Box>\n\n      <Menu\n        id=\"profile-menu\"\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleProfileMenuClose}\n        onClick={handleProfileMenuClose}\n        PaperProps={{\n          elevation: 0,\n          sx: {\n            overflow: 'visible',\n            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\n            mt: 1.5,\n            '& .MuiAvatar-root': {\n              width: 32,\n              height: 32,\n              ml: -0.5,\n              mr: 1,\n            },\n            '&:before': {\n              content: '\"\"',\n              display: 'block',\n              position: 'absolute',\n              top: 0,\n              right: 14,\n              width: 10,\n              height: 10,\n              bgcolor: 'background.paper',\n              transform: 'translateY(-50%) rotate(45deg)',\n              zIndex: 0,\n            },\n          },\n        }}\n        transformOrigin={{ horizontal: 'right', vertical: 'top' }}\n        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\n      >\n        <MenuItem onClick={() => navigate('/profile')}>\n          <SettingsIcon sx={{ mr: 2 }} />\n          Profile Settings\n        </MenuItem>\n        <Divider />\n        <MenuItem onClick={handleLogout}>\n          <LogoutIcon sx={{ mr: 2 }} />\n          Logout\n        </MenuItem>\n      </Menu>\n    </Box>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SACEC,MAAM,EACNC,GAAG,EACHC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,SACEJ,IAAI,IAAIK,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,cAAc,IAAIC,MAAM,EACxBC,MAAM,IAAIC,UAAU,EACpBC,kBAAkB,IAAIC,SAAS,EAC/BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,eAAe,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,eAAe,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,WAAW,GAAG,GAAG;AASvB,MAAMC,eAAiC,GAAG,CACxC;EAAEC,IAAI,EAAE,MAAM;EAAEC,IAAI,eAAEJ,OAAA,CAAClB,QAAQ;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAI,CAAC,EAC/C;EAAEN,IAAI,EAAE,QAAQ;EAAEC,IAAI,eAAEJ,OAAA,CAAChB,UAAU;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAU,CAAC,EACzD;EAAEN,IAAI,EAAE,cAAc;EAAEC,IAAI,eAAEJ,OAAA,CAACJ,eAAe;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAgB,CAAC,EAC1E;EAAEN,IAAI,EAAE,KAAK;EAAEC,IAAI,eAAEJ,OAAA,CAACd,MAAM;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAM,CAAC,EAC9C;EAAEN,IAAI,EAAE,SAAS;EAAEC,IAAI,eAAEJ,OAAA,CAACZ,UAAU;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE;AAAW,CAAC,EAC3D;EAAEN,IAAI,EAAE,OAAO;EAAEC,IAAI,eAAEJ,OAAA,CAACV,SAAS;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAAEC,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAK,CAAC,CACxE;AAED,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC7B,MAAMC,KAAK,GAAGpC,QAAQ,CAAC,CAAC;EACxB,MAAMqC,QAAQ,GAAGpC,aAAa,CAACmC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAC9B,MAAM2D,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2D,IAAI;IAAEC;EAAO,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEyB;EAAY,CAAC,GAAGxB,eAAe,CAAC,CAAC;EAEzC,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAqB,IAAI,CAAC;EAElE,MAAMqE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BH,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMK,qBAAqB,GAAIC,KAAoC,IAAK;IACtEH,WAAW,CAACG,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnCL,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBX,MAAM,CAAC,CAAC;IACRC,WAAW,CAAC,yBAAyB,CAAC;IACtCJ,QAAQ,CAAC,QAAQ,CAAC;IAClBa,sBAAsB,CAAC,CAAC;EAC1B,CAAC;EAED,MAAME,gBAAgB,GAAIxB,IAAY,IAAK;IACzCS,QAAQ,CAACT,IAAI,CAAC;IACd,IAAIM,QAAQ,EAAE;MACZS,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMU,MAAM,gBACVlC,OAAA;IAAAmC,QAAA,gBACEnC,OAAA,CAAC5B,OAAO;MAAA+D,QAAA,eACNnC,OAAA,CAAC3B,UAAU;QAAC+D,OAAO,EAAC,IAAI;QAACC,MAAM;QAACC,SAAS,EAAC,KAAK;QAACC,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAL,QAAA,EAAC;MAE5E;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACVR,OAAA,CAACvB,OAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXR,OAAA,CAACjC,IAAI;MAAAoE,QAAA,EACFjC,eAAe,CACbuC,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAAChC,SAAS,IAAI,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,IAAI,MAAK,OAAO,CAAC,CACzDC,GAAG,CAAEF,IAAI,iBACR1C,OAAA,CAAChC,QAAQ;QAAiB6E,cAAc;QAAAV,QAAA,eACtCnC,OAAA,CAAC/B,cAAc;UACb6E,QAAQ,EAAE3B,QAAQ,CAAC4B,QAAQ,KAAKL,IAAI,CAACjC,IAAK;UAC1CuC,OAAO,EAAEA,CAAA,KAAMf,gBAAgB,CAACS,IAAI,CAACjC,IAAI,CAAE;UAC3C8B,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChBU,eAAe,EAAEnC,KAAK,CAACoC,OAAO,CAACC,OAAO,CAACC,IAAI,GAAG,IAAI;cAClD,SAAS,EAAE;gBACTH,eAAe,EAAEnC,KAAK,CAACoC,OAAO,CAACC,OAAO,CAACC,IAAI,GAAG;cAChD;YACF;UACF,CAAE;UAAAjB,QAAA,gBAEFnC,OAAA,CAAC9B,YAAY;YACXqE,EAAE,EAAE;cACFc,KAAK,EAAElC,QAAQ,CAAC4B,QAAQ,KAAKL,IAAI,CAACjC,IAAI,GAAGK,KAAK,CAACoC,OAAO,CAACC,OAAO,CAACC,IAAI,GAAG;YACxE,CAAE;YAAAjB,QAAA,EAEDO,IAAI,CAACtC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACfR,OAAA,CAAC7B,YAAY;YACXgF,OAAO,EAAET,IAAI,CAACvC,IAAK;YACnBoC,EAAE,EAAE;cACF,4BAA4B,EAAE;gBAC5Bc,KAAK,EAAElC,QAAQ,CAAC4B,QAAQ,KAAKL,IAAI,CAACjC,IAAI,GAAGK,KAAK,CAACoC,OAAO,CAACC,OAAO,CAACC,IAAI,GAAG,SAAS;gBAC/EZ,UAAU,EAAErB,QAAQ,CAAC4B,QAAQ,KAAKL,IAAI,CAACjC,IAAI,GAAG,GAAG,GAAG;cACtD;YACF;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC,GA7BJkC,IAAI,CAACvC,IAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8Bd,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACER,OAAA,CAACrC,GAAG;IAAC4E,EAAE,EAAE;MAAEe,OAAO,EAAE;IAAO,CAAE;IAAAnB,QAAA,gBAC3BnC,OAAA,CAACpC,WAAW;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfR,OAAA,CAACtC,MAAM;MACL6F,QAAQ,EAAC,OAAO;MAChBhB,EAAE,EAAE;QACFiB,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAexD,WAAW;QAAM,CAAC;QAC9CyD,EAAE,EAAE;UAAED,EAAE,EAAE,GAAGxD,WAAW;QAAK;MAC/B,CAAE;MAAAkC,QAAA,eAEFnC,OAAA,CAAC5B,OAAO;QAAA+D,QAAA,gBACNnC,OAAA,CAAClC,UAAU;UACTuF,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxBM,IAAI,EAAC,OAAO;UACZX,OAAO,EAAErB,kBAAmB;UAC5BY,EAAE,EAAE;YAAEqB,EAAE,EAAE,CAAC;YAAEN,OAAO,EAAE;cAAEG,EAAE,EAAE;YAAO;UAAE,CAAE;UAAAtB,QAAA,eAEvCnC,OAAA,CAACpB,QAAQ;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACbR,OAAA,CAAC3B,UAAU;UAAC+D,OAAO,EAAC,IAAI;UAACC,MAAM;UAACC,SAAS,EAAC,KAAK;UAACC,EAAE,EAAE;YAAEsB,QAAQ,EAAE;UAAE,CAAE;UAAA1B,QAAA,EAAC;QAErE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbR,OAAA,CAACrC,GAAG;UAAC4E,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEQ,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAA5B,QAAA,gBACzDnC,OAAA,CAAC3B,UAAU;YAAC+D,OAAO,EAAC,OAAO;YAACG,EAAE,EAAE;cAAEe,OAAO,EAAE;gBAAEU,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAQ;YAAE,CAAE;YAAA9B,QAAA,EACtEf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C;UAAS;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACbR,OAAA,CAAClC,UAAU;YACTqG,IAAI,EAAC,OAAO;YACZR,IAAI,EAAC,KAAK;YACV,cAAW,yBAAyB;YACpC,iBAAc,cAAc;YAC5B,iBAAc,MAAM;YACpBX,OAAO,EAAEpB,qBAAsB;YAC/ByB,KAAK,EAAC,SAAS;YAAAlB,QAAA,eAEfnC,OAAA,CAAC1B,MAAM;cAACiE,EAAE,EAAE;gBAAEiB,KAAK,EAAE,EAAE;gBAAEY,MAAM,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAiB,CAAE;cAAAlC,QAAA,EAC9Df,IAAI,aAAJA,IAAI,wBAAAP,eAAA,GAAJO,IAAI,CAAE8C,SAAS,cAAArD,eAAA,uBAAfA,eAAA,CAAiByD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETR,OAAA,CAACrC,GAAG;MACF2E,SAAS,EAAC,KAAK;MACfC,EAAE,EAAE;QAAEiB,KAAK,EAAE;UAAEC,EAAE,EAAExD;QAAY,CAAC;QAAEuE,UAAU,EAAE;UAAEf,EAAE,EAAE;QAAE;MAAE,CAAE;MAC1D,cAAW,YAAY;MAAAtB,QAAA,gBAEvBnC,OAAA,CAACnC,MAAM;QACLuE,OAAO,EAAC,WAAW;QACnBqC,IAAI,EAAElD,UAAW;QACjBmD,OAAO,EAAE/C,kBAAmB;QAC5BgD,UAAU,EAAE;UACVC,WAAW,EAAE,IAAI,CAAE;QACrB,CAAE;QACFrC,EAAE,EAAE;UACFe,OAAO,EAAE;YAAEU,EAAE,EAAE,OAAO;YAAEP,EAAE,EAAE;UAAO,CAAC;UACpC,oBAAoB,EAAE;YAAEoB,SAAS,EAAE,YAAY;YAAErB,KAAK,EAAEvD;UAAY;QACtE,CAAE;QAAAkC,QAAA,EAEDD;MAAM;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACTR,OAAA,CAACnC,MAAM;QACLuE,OAAO,EAAC,WAAW;QACnBG,EAAE,EAAE;UACFe,OAAO,EAAE;YAAEU,EAAE,EAAE,MAAM;YAAEP,EAAE,EAAE;UAAQ,CAAC;UACpC,oBAAoB,EAAE;YAAEoB,SAAS,EAAE,YAAY;YAAErB,KAAK,EAAEvD;UAAY;QACtE,CAAE;QACFwE,IAAI;QAAAtC,QAAA,EAEHD;MAAM;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENR,OAAA,CAACrC,GAAG;MACF2E,SAAS,EAAC,MAAM;MAChBC,EAAE,EAAE;QACFsB,QAAQ,EAAE,CAAC;QACXiB,CAAC,EAAE,CAAC;QACJtB,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAexD,WAAW;QAAM,CAAC;QAC9C8E,SAAS,EAAE,OAAO;QAClB9B,eAAe,EAAEnC,KAAK,CAACoC,OAAO,CAAC8B,UAAU,CAACC;MAC5C,CAAE;MAAA9C,QAAA,gBAEFnC,OAAA,CAAC5B,OAAO;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXR,OAAA,CAACzC,MAAM;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAENR,OAAA,CAACzB,IAAI;MACH2G,EAAE,EAAC,cAAc;MACjBzD,QAAQ,EAAEA,QAAS;MACnBgD,IAAI,EAAEU,OAAO,CAAC1D,QAAQ,CAAE;MACxBiD,OAAO,EAAE3C,sBAAuB;MAChCiB,OAAO,EAAEjB,sBAAuB;MAChCqD,UAAU,EAAE;QACVC,SAAS,EAAE,CAAC;QACZ9C,EAAE,EAAE;UACF+C,QAAQ,EAAE,SAAS;UACnB7C,MAAM,EAAE,2CAA2C;UACnD8C,EAAE,EAAE,GAAG;UACP,mBAAmB,EAAE;YACnB/B,KAAK,EAAE,EAAE;YACTY,MAAM,EAAE,EAAE;YACVV,EAAE,EAAE,CAAC,GAAG;YACRE,EAAE,EAAE;UACN,CAAC;UACD,UAAU,EAAE;YACV4B,OAAO,EAAE,IAAI;YACblC,OAAO,EAAE,OAAO;YAChBC,QAAQ,EAAE,UAAU;YACpBkC,GAAG,EAAE,CAAC;YACNC,KAAK,EAAE,EAAE;YACTlC,KAAK,EAAE,EAAE;YACTY,MAAM,EAAE,EAAE;YACVC,OAAO,EAAE,kBAAkB;YAC3BsB,SAAS,EAAE,gCAAgC;YAC3CC,MAAM,EAAE;UACV;QACF;MACF,CAAE;MACFC,eAAe,EAAE;QAAEC,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAM,CAAE;MAC1DC,YAAY,EAAE;QAAEF,UAAU,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAA5D,QAAA,gBAE1DnC,OAAA,CAACxB,QAAQ;QAACwE,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,UAAU,CAAE;QAAAiB,QAAA,gBAC5CnC,OAAA,CAACN,YAAY;UAAC6C,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE;QAAE;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXR,OAAA,CAACvB,OAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXR,OAAA,CAACxB,QAAQ;QAACwE,OAAO,EAAEhB,YAAa;QAAAG,QAAA,gBAC9BnC,OAAA,CAACR,UAAU;UAAC+C,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE;QAAE;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,UAE/B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACI,EAAA,CA7NID,MAAgB;EAAA,QACNjC,QAAQ,EACLC,aAAa,EACbnB,WAAW,EACXC,WAAW,EACHoC,OAAO,EACRC,eAAe;AAAA;AAAAmG,EAAA,GANnCtF,MAAgB;AA+NtB,eAAeA,MAAM;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}