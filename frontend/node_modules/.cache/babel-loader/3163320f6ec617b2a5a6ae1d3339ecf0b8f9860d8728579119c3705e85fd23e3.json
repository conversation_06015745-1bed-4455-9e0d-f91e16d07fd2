{"ast": null, "code": "import { apiClient } from './apiClient';\nclass AdminService {\n  // System Stats and Health\n  async getSystemStats() {\n    try {\n      var _backendData$recent_a;\n      const response = await apiClient.get('/admin/stats');\n      const backendData = response.data;\n      console.log('✅ Admin stats API success at', new Date().toISOString(), ':', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        totalBooks: backendData.total_books || 0,\n        totalUsers: backendData.total_users || 0,\n        totalSearches: backendData.total_searches || 0,\n        totalQuestions: backendData.total_questions || 0,\n        storageUsed: backendData.storage_used || 0,\n        storageLimit: backendData.storage_limit || 1000,\n        // Default limit in MB\n        activeProcessing: backendData.active_processing || 0,\n        systemHealth: backendData.system_health || 'healthy',\n        recentActivity: ((_backendData$recent_a = backendData.recent_activity) === null || _backendData$recent_a === void 0 ? void 0 : _backendData$recent_a.map(activity => ({\n          id: Math.random().toString(),\n          type: activity.type === 'book_upload' ? 'upload' : 'upload',\n          description: `${activity.title} - ${activity.status}`,\n          timestamp: activity.timestamp,\n          user: 'Admin'\n        }))) || [],\n        popularBooks: [],\n        // Not available in backend response\n        userGrowth: 0,\n        searchGrowth: 0\n      };\n    } catch (error) {\n      console.error('❌ Admin stats API failed at', new Date().toISOString(), ':', error);\n      // Return mock data for development\n      return {\n        totalBooks: 156,\n        totalUsers: 1247,\n        totalSearches: 3421,\n        totalQuestions: 892,\n        storageUsed: 15 * 1024 * 1024 * 1024,\n        // 15GB\n        storageLimit: 100 * 1024 * 1024 * 1024,\n        // 100GB\n        activeProcessing: 2,\n        systemHealth: 'healthy',\n        userGrowth: 12.5,\n        searchGrowth: 8.3,\n        recentActivity: [{\n          id: '1',\n          type: 'upload',\n          description: 'New book uploaded: \"Medical Anatomy\"',\n          timestamp: new Date().toISOString(),\n          user: '<EMAIL>'\n        }, {\n          id: '2',\n          type: 'search',\n          description: 'Search performed: \"cardiac anatomy\"',\n          timestamp: new Date(Date.now() - 300000).toISOString(),\n          user: '<EMAIL>'\n        }],\n        popularBooks: [{\n          id: '1',\n          title: 'Gray\\'s Anatomy',\n          authors: 'Henry Gray',\n          searchCount: 245,\n          uploadDate: '2024-01-15',\n          status: 'completed',\n          fileSize: 50 * 1024 * 1024,\n          language: 'en',\n          tags: ['anatomy', 'medical']\n        }, {\n          id: '2',\n          title: 'Harrison\\'s Principles of Internal Medicine',\n          authors: 'Dennis Kasper',\n          searchCount: 198,\n          uploadDate: '2024-01-20',\n          status: 'completed',\n          fileSize: 75 * 1024 * 1024,\n          language: 'en',\n          tags: ['internal medicine']\n        }]\n      };\n    }\n  }\n  async getSystemHealth() {\n    try {\n      var _backendData$services, _backendData$services2, _backendData$services3, _backendData$services4, _backendData$services5, _backendData$services6, _backendData$services7, _backendData$services8;\n      const backendData = await apiClient.get('/admin/health');\n      console.log('✅ Admin health API success:', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        status: backendData.status || 'healthy',\n        database: {\n          status: ((_backendData$services = backendData.services) === null || _backendData$services === void 0 ? void 0 : (_backendData$services2 = _backendData$services.database) === null || _backendData$services2 === void 0 ? void 0 : _backendData$services2.status) === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 45 // Default value\n        },\n        vector_db: {\n          status: ((_backendData$services3 = backendData.services) === null || _backendData$services3 === void 0 ? void 0 : (_backendData$services4 = _backendData$services3.indexing) === null || _backendData$services4 === void 0 ? void 0 : _backendData$services4.status) === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 0\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          // Default values\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15\n        },\n        services: {\n          embedding_service: ((_backendData$services5 = backendData.services) === null || _backendData$services5 === void 0 ? void 0 : (_backendData$services6 = _backendData$services5.indexing) === null || _backendData$services6 === void 0 ? void 0 : _backendData$services6.status) === 'healthy',\n          llm_service: ((_backendData$services7 = backendData.services) === null || _backendData$services7 === void 0 ? void 0 : (_backendData$services8 = _backendData$services7.indexing) === null || _backendData$services8 === void 0 ? void 0 : _backendData$services8.status) === 'healthy',\n          pdf_service: true // Assume PDF service is working\n        }\n      };\n    } catch (error) {\n      console.error('❌ Admin health API failed:', error);\n      // Return realistic health data based on what we know is working\n      return {\n        status: 'warning',\n        // Since vector DB is not running\n        database: {\n          status: 'connected',\n          // We know DB is working from other APIs\n          response_time: 45\n        },\n        vector_db: {\n          status: 'disconnected',\n          // Qdrant is not running\n          response_time: 0\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15\n        },\n        services: {\n          embedding_service: false,\n          // Requires vector DB\n          llm_service: false,\n          // Requires API keys\n          pdf_service: true // Basic PDF processing works\n        }\n      };\n    }\n  }\n\n  // Analytics\n  async getAnalytics(period = 'week') {\n    try {\n      var _searchAnalytics$most;\n      // Get system stats for basic counts\n      const systemStats = await this.getSystemStats();\n\n      // Get search analytics\n      const searchAnalytics = await apiClient.get('/search/analytics');\n\n      // Get books for popular books (using search count from books)\n      const booksData = await this.getBooks(1, 10);\n      return {\n        totalSearches: searchAnalytics.total_searches || 0,\n        totalQuestions: 0,\n        // Will need to add QA analytics endpoint\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        // Will need historical data for trends\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: booksData.books.slice(0, 5).map(book => ({\n          title: book.title,\n          authors: book.authors,\n          searchCount: book.searchCount || 0\n        })),\n        popularQueries: ((_searchAnalytics$most = searchAnalytics.most_common_queries) === null || _searchAnalytics$most === void 0 ? void 0 : _searchAnalytics$most.slice(0, 5)) || []\n      };\n    } catch (error) {\n      console.error('❌ Analytics API failed:', error);\n      // Return minimal real data on error\n      const systemStats = await this.getSystemStats();\n      return {\n        totalSearches: 0,\n        totalQuestions: 0,\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: [],\n        popularQueries: []\n      };\n    }\n  }\n\n  // Book Management\n  async getBooks(page = 1, limit = 10, search) {\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: limit.toString(),\n        ...(search && {\n          search\n        })\n      });\n      const backendData = await apiClient.get(`/admin/books?${params}`);\n\n      // Transform backend response to frontend format\n      console.log('✅ Admin books API success at', new Date().toISOString(), ':', backendData);\n      return {\n        books: backendData.books.map(book => ({\n          id: book.id,\n          title: book.title,\n          authors: Array.isArray(book.authors) ? book.authors.join(', ') : book.authors,\n          searchCount: 0,\n          // Not available in backend response\n          uploadDate: book.created_at,\n          status: book.processing_status,\n          fileSize: book.file_size,\n          language: book.language,\n          tags: book.tags || []\n        })),\n        total: backendData.total,\n        page: backendData.page,\n        totalPages: Math.ceil(backendData.total / backendData.page_size)\n      };\n    } catch (error) {\n      console.error('❌ Admin books API failed:', error);\n      // Return mock data for development\n      return {\n        books: [{\n          id: '1',\n          title: 'Gray\\'s Anatomy',\n          authors: 'Henry Gray',\n          searchCount: 245,\n          uploadDate: '2024-01-15',\n          status: 'completed',\n          fileSize: 50 * 1024 * 1024,\n          language: 'en',\n          tags: ['anatomy', 'medical', 'textbook']\n        }, {\n          id: '2',\n          title: 'Harrison\\'s Principles of Internal Medicine',\n          authors: 'Dennis Kasper',\n          searchCount: 198,\n          uploadDate: '2024-01-20',\n          status: 'processing',\n          fileSize: 75 * 1024 * 1024,\n          language: 'en',\n          tags: ['internal medicine', 'diagnosis']\n        }],\n        total: 156,\n        page: 1,\n        totalPages: 16\n      };\n    }\n  }\n  async uploadBook(file, data) {\n    const formData = new FormData();\n    formData.append('file', file);\n    Object.entries(data).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        formData.append(key, value.toString());\n      }\n    });\n    const response = await apiClient.post('/admin/books/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n  async deleteBook(bookId) {\n    const response = await apiClient.delete(`/admin/books/${bookId}`);\n    return response.data;\n  }\n  async updateBook(bookId, data) {\n    const response = await apiClient.put(`/admin/books/${bookId}`, data);\n    return response.data;\n  }\n  async getProcessingStatus() {\n    try {\n      // Note: This endpoint may not exist yet, using mock data for now\n      // const response = await apiClient.get('/admin/processing/status');\n      // return response.data;\n      throw new Error('Endpoint not implemented yet');\n    } catch (error) {\n      // Return mock data for development\n      return [{\n        book_id: '2',\n        status: 'processing',\n        progress: 65,\n        message: 'Extracting text from PDF...',\n        started_at: new Date().toISOString()\n      }, {\n        book_id: '3',\n        status: 'pending',\n        progress: 0,\n        message: 'Waiting in queue...',\n        started_at: new Date().toISOString()\n      }];\n    }\n  }\n  async reprocessBook(bookId) {\n    const response = await apiClient.post(`/admin/processing/reprocess/${bookId}`);\n    return response.data;\n  }\n  async cancelProcessing(bookId) {\n    const response = await apiClient.post(`/admin/processing/cancel/${bookId}`);\n    return response.data;\n  }\n\n  // User Management\n  async getUsers(page = 1, limit = 10, search) {\n    try {\n      const skip = (page - 1) * limit;\n      const params = new URLSearchParams({\n        skip: skip.toString(),\n        limit: limit.toString(),\n        ...(search && {\n          search\n        })\n      });\n      const response = await apiClient.get(`/users?${params}`);\n      console.log('✅ Users API response:', response.data);\n\n      // Transform backend response to frontend format\n      const responseData = response.data;\n      const users = responseData.users || responseData; // Handle both paginated and direct array responses\n      const total = responseData.total || users.length;\n      return {\n        users: users.map(user => ({\n          id: user.id,\n          email: user.email,\n          full_name: user.full_name || user.name || 'Unknown',\n          role: user.role.toUpperCase(),\n          created_at: user.created_at,\n          last_login: user.last_login || user.created_at,\n          is_active: user.is_active\n        })),\n        total: total,\n        page: page,\n        totalPages: Math.ceil(total / limit)\n      };\n    } catch (error) {\n      console.error('❌ Users API failed:', error);\n      throw error; // Let the UI handle the error instead of returning mock data\n    }\n  }\n  async updateUserRole(userId, role) {\n    // Note: Role update endpoint may need to be implemented\n    // For now, using activate/deactivate endpoints\n    throw new Error('Role update endpoint not implemented yet');\n  }\n  async toggleUserStatus(userId) {\n    // Use activate/deactivate endpoints\n    const response = await apiClient.put(`/users/${userId}/activate`);\n    return response.data;\n  }\n}\nexport const adminService = new AdminService();", "map": {"version": 3, "names": ["apiClient", "AdminService", "getSystemStats", "_backendData$recent_a", "response", "get", "backendData", "data", "console", "log", "Date", "toISOString", "totalBooks", "total_books", "totalUsers", "total_users", "totalSearches", "total_searches", "totalQuestions", "total_questions", "storageUsed", "storage_used", "storageLimit", "storage_limit", "activeProcessing", "active_processing", "systemHealth", "system_health", "recentActivity", "recent_activity", "map", "activity", "id", "Math", "random", "toString", "type", "description", "title", "status", "timestamp", "user", "popularBooks", "userGrowth", "searchGrowth", "error", "now", "authors", "searchCount", "uploadDate", "fileSize", "language", "tags", "getSystemHealth", "_backendData$services", "_backendData$services2", "_backendData$services3", "_backendData$services4", "_backendData$services5", "_backendData$services6", "_backendData$services7", "_backendData$services8", "database", "services", "response_time", "vector_db", "indexing", "storage", "available_space", "total_space", "usage_percentage", "embedding_service", "llm_service", "pdf_service", "getAnalytics", "period", "_searchAnalytics$most", "systemStats", "searchAnalytics", "booksData", "getBooks", "searchTrend", "questionTrend", "userTrend", "books", "slice", "book", "popularQueries", "most_common_queries", "page", "limit", "search", "params", "URLSearchParams", "page_size", "Array", "isArray", "join", "created_at", "processing_status", "file_size", "total", "totalPages", "ceil", "uploadBook", "file", "formData", "FormData", "append", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "post", "headers", "deleteBook", "bookId", "delete", "updateBook", "put", "getProcessingStatus", "Error", "book_id", "progress", "message", "started_at", "reprocessBook", "cancelProcessing", "getUsers", "skip", "responseData", "users", "length", "email", "full_name", "name", "role", "toUpperCase", "last_login", "is_active", "updateUserRole", "userId", "toggleUserStatus", "adminService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\n\nexport interface SystemStats {\n  totalBooks: number;\n  totalUsers: number;\n  totalSearches: number;\n  totalQuestions: number;\n  storageUsed: number;\n  storageLimit: number;\n  activeProcessing: number;\n  systemHealth: 'healthy' | 'warning' | 'critical';\n  recentActivity: ActivityItem[];\n  popularBooks: BookItem[];\n  userGrowth: number;\n  searchGrowth: number;\n}\n\nexport interface ActivityItem {\n  id: string;\n  type: 'upload' | 'search' | 'question' | 'user_signup';\n  description: string;\n  timestamp: string;\n  user?: string;\n}\n\nexport interface BookItem {\n  id: string;\n  title: string;\n  authors: string;\n  searchCount: number;\n  uploadDate: string;\n  status: 'processing' | 'completed' | 'failed';\n  fileSize: number;\n  language: string;\n  tags: string[];\n}\n\nexport interface User {\n  id: string;\n  email: string;\n  full_name: string;\n  role: 'USER' | 'ADMIN';\n  created_at: string;\n  last_login: string;\n  is_active: boolean;\n}\n\nexport interface BookUploadData {\n  title: string;\n  authors: string;\n  isbn?: string;\n  publisher?: string;\n  publication_year?: number;\n  edition?: string;\n  description?: string;\n  tags?: string;\n  language: string;\n}\n\nexport interface ProcessingStatus {\n  book_id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  message: string;\n  started_at: string;\n  completed_at?: string;\n  error_message?: string;\n}\n\nexport interface SystemHealth {\n  status: 'healthy' | 'warning' | 'critical';\n  database: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  vector_db: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  storage: {\n    available_space: number;\n    total_space: number;\n    usage_percentage: number;\n  };\n  services: {\n    embedding_service: boolean;\n    llm_service: boolean;\n    pdf_service: boolean;\n  };\n}\n\nclass AdminService {\n  // System Stats and Health\n  async getSystemStats(): Promise<SystemStats> {\n    try {\n      const response = await apiClient.get('/admin/stats');\n      const backendData = response.data;\n      console.log('✅ Admin stats API success at', new Date().toISOString(), ':', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        totalBooks: backendData.total_books || 0,\n        totalUsers: backendData.total_users || 0,\n        totalSearches: backendData.total_searches || 0,\n        totalQuestions: backendData.total_questions || 0,\n        storageUsed: backendData.storage_used || 0,\n        storageLimit: backendData.storage_limit || 1000, // Default limit in MB\n        activeProcessing: backendData.active_processing || 0,\n        systemHealth: backendData.system_health || 'healthy',\n        recentActivity: backendData.recent_activity?.map((activity: any) => ({\n          id: Math.random().toString(),\n          type: activity.type === 'book_upload' ? 'upload' as const : 'upload' as const,\n          description: `${activity.title} - ${activity.status}`,\n          timestamp: activity.timestamp,\n          user: 'Admin',\n        })) || [],\n        popularBooks: [], // Not available in backend response\n        userGrowth: 0,\n        searchGrowth: 0,\n      };\n    } catch (error) {\n      console.error('❌ Admin stats API failed at', new Date().toISOString(), ':', error);\n      // Return mock data for development\n      return {\n        totalBooks: 156,\n        totalUsers: 1247,\n        totalSearches: 3421,\n        totalQuestions: 892,\n        storageUsed: 15 * 1024 * 1024 * 1024, // 15GB\n        storageLimit: 100 * 1024 * 1024 * 1024, // 100GB\n        activeProcessing: 2,\n        systemHealth: 'healthy',\n        userGrowth: 12.5,\n        searchGrowth: 8.3,\n        recentActivity: [\n          {\n            id: '1',\n            type: 'upload',\n            description: 'New book uploaded: \"Medical Anatomy\"',\n            timestamp: new Date().toISOString(),\n            user: '<EMAIL>',\n          },\n          {\n            id: '2',\n            type: 'search',\n            description: 'Search performed: \"cardiac anatomy\"',\n            timestamp: new Date(Date.now() - 300000).toISOString(),\n            user: '<EMAIL>',\n          },\n        ],\n        popularBooks: [\n          {\n            id: '1',\n            title: 'Gray\\'s Anatomy',\n            authors: 'Henry Gray',\n            searchCount: 245,\n            uploadDate: '2024-01-15',\n            status: 'completed',\n            fileSize: 50 * 1024 * 1024,\n            language: 'en',\n            tags: ['anatomy', 'medical'],\n          },\n          {\n            id: '2',\n            title: 'Harrison\\'s Principles of Internal Medicine',\n            authors: 'Dennis Kasper',\n            searchCount: 198,\n            uploadDate: '2024-01-20',\n            status: 'completed',\n            fileSize: 75 * 1024 * 1024,\n            language: 'en',\n            tags: ['internal medicine'],\n          },\n        ],\n      };\n    }\n  }\n\n  async getSystemHealth(): Promise<SystemHealth> {\n    try {\n      const backendData = await apiClient.get('/admin/health');\n      console.log('✅ Admin health API success:', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        status: backendData.status || 'healthy',\n        database: {\n          status: backendData.services?.database?.status === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 45, // Default value\n        },\n        vector_db: {\n          status: backendData.services?.indexing?.status === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 0,\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024, // Default values\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15,\n        },\n        services: {\n          embedding_service: backendData.services?.indexing?.status === 'healthy',\n          llm_service: backendData.services?.indexing?.status === 'healthy',\n          pdf_service: true, // Assume PDF service is working\n        },\n      };\n    } catch (error) {\n      console.error('❌ Admin health API failed:', error);\n      // Return realistic health data based on what we know is working\n      return {\n        status: 'warning', // Since vector DB is not running\n        database: {\n          status: 'connected', // We know DB is working from other APIs\n          response_time: 45,\n        },\n        vector_db: {\n          status: 'disconnected', // Qdrant is not running\n          response_time: 0,\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15,\n        },\n        services: {\n          embedding_service: false, // Requires vector DB\n          llm_service: false, // Requires API keys\n          pdf_service: true, // Basic PDF processing works\n        },\n      };\n    }\n  }\n\n  // Analytics\n  async getAnalytics(period: string = 'week'): Promise<{\n    totalSearches: number;\n    totalQuestions: number;\n    totalUsers: number;\n    totalBooks: number;\n    searchTrend: number;\n    questionTrend: number;\n    userTrend: number;\n    popularBooks: Array<{\n      title: string;\n      authors: string;\n      searchCount: number;\n    }>;\n    popularQueries: Array<{\n      query: string;\n      count: number;\n    }>;\n  }> {\n    try {\n      // Get system stats for basic counts\n      const systemStats = await this.getSystemStats();\n\n      // Get search analytics\n      const searchAnalytics = await apiClient.get('/search/analytics');\n\n      // Get books for popular books (using search count from books)\n      const booksData = await this.getBooks(1, 10);\n\n      return {\n        totalSearches: searchAnalytics.total_searches || 0,\n        totalQuestions: 0, // Will need to add QA analytics endpoint\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0, // Will need historical data for trends\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: booksData.books.slice(0, 5).map(book => ({\n          title: book.title,\n          authors: book.authors,\n          searchCount: book.searchCount || 0,\n        })),\n        popularQueries: searchAnalytics.most_common_queries?.slice(0, 5) || [],\n      };\n    } catch (error) {\n      console.error('❌ Analytics API failed:', error);\n      // Return minimal real data on error\n      const systemStats = await this.getSystemStats();\n      return {\n        totalSearches: 0,\n        totalQuestions: 0,\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: [],\n        popularQueries: [],\n      };\n    }\n  }\n\n  // Book Management\n  async getBooks(page: number = 1, limit: number = 10, search?: string): Promise<{\n    books: BookItem[];\n    total: number;\n    page: number;\n    totalPages: number;\n  }> {\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: limit.toString(),\n        ...(search && { search }),\n      });\n      const backendData = await apiClient.get(`/admin/books?${params}`);\n\n      // Transform backend response to frontend format\n      console.log('✅ Admin books API success at', new Date().toISOString(), ':', backendData);\n      return {\n        books: backendData.books.map((book: any) => ({\n          id: book.id,\n          title: book.title,\n          authors: Array.isArray(book.authors) ? book.authors.join(', ') : book.authors,\n          searchCount: 0, // Not available in backend response\n          uploadDate: book.created_at,\n          status: book.processing_status,\n          fileSize: book.file_size,\n          language: book.language,\n          tags: book.tags || [],\n        })),\n        total: backendData.total,\n        page: backendData.page,\n        totalPages: Math.ceil(backendData.total / backendData.page_size),\n      };\n    } catch (error) {\n      console.error('❌ Admin books API failed:', error);\n      // Return mock data for development\n      return {\n        books: [\n          {\n            id: '1',\n            title: 'Gray\\'s Anatomy',\n            authors: 'Henry Gray',\n            searchCount: 245,\n            uploadDate: '2024-01-15',\n            status: 'completed',\n            fileSize: 50 * 1024 * 1024,\n            language: 'en',\n            tags: ['anatomy', 'medical', 'textbook'],\n          },\n          {\n            id: '2',\n            title: 'Harrison\\'s Principles of Internal Medicine',\n            authors: 'Dennis Kasper',\n            searchCount: 198,\n            uploadDate: '2024-01-20',\n            status: 'processing',\n            fileSize: 75 * 1024 * 1024,\n            language: 'en',\n            tags: ['internal medicine', 'diagnosis'],\n          },\n        ],\n        total: 156,\n        page: 1,\n        totalPages: 16,\n      };\n    }\n  }\n\n  async uploadBook(file: File, data: BookUploadData): Promise<{ book_id: string; message: string }> {\n    const formData = new FormData();\n    formData.append('file', file);\n    Object.entries(data).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        formData.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.post('/admin/books/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  }\n\n  async deleteBook(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/admin/books/${bookId}`);\n    return response.data;\n  }\n\n  async updateBook(bookId: string, data: Partial<BookUploadData>): Promise<BookItem> {\n    const response = await apiClient.put(`/admin/books/${bookId}`, data);\n    return response.data;\n  }\n\n  async getProcessingStatus(): Promise<ProcessingStatus[]> {\n    try {\n      // Note: This endpoint may not exist yet, using mock data for now\n      // const response = await apiClient.get('/admin/processing/status');\n      // return response.data;\n      throw new Error('Endpoint not implemented yet');\n    } catch (error) {\n      // Return mock data for development\n      return [\n        {\n          book_id: '2',\n          status: 'processing',\n          progress: 65,\n          message: 'Extracting text from PDF...',\n          started_at: new Date().toISOString(),\n        },\n        {\n          book_id: '3',\n          status: 'pending',\n          progress: 0,\n          message: 'Waiting in queue...',\n          started_at: new Date().toISOString(),\n        },\n      ];\n    }\n  }\n\n  async reprocessBook(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.post(`/admin/processing/reprocess/${bookId}`);\n    return response.data;\n  }\n\n  async cancelProcessing(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.post(`/admin/processing/cancel/${bookId}`);\n    return response.data;\n  }\n\n  // User Management\n  async getUsers(page: number = 1, limit: number = 10, search?: string): Promise<{\n    users: User[];\n    total: number;\n    page: number;\n    totalPages: number;\n  }> {\n    try {\n      const skip = (page - 1) * limit;\n      const params = new URLSearchParams({\n        skip: skip.toString(),\n        limit: limit.toString(),\n        ...(search && { search }),\n      });\n      const response = await apiClient.get(`/users?${params}`);\n      console.log('✅ Users API response:', response.data);\n\n      // Transform backend response to frontend format\n      const responseData = response.data;\n      const users = responseData.users || responseData; // Handle both paginated and direct array responses\n      const total = responseData.total || users.length;\n\n      return {\n        users: users.map((user: any) => ({\n          id: user.id,\n          email: user.email,\n          full_name: user.full_name || user.name || 'Unknown',\n          role: user.role.toUpperCase(),\n          created_at: user.created_at,\n          last_login: user.last_login || user.created_at,\n          is_active: user.is_active,\n        })),\n        total: total,\n        page: page,\n        totalPages: Math.ceil(total / limit),\n      };\n    } catch (error) {\n      console.error('❌ Users API failed:', error);\n      throw error; // Let the UI handle the error instead of returning mock data\n    }\n  }\n\n  async updateUserRole(userId: string, role: 'USER' | 'ADMIN'): Promise<User> {\n    // Note: Role update endpoint may need to be implemented\n    // For now, using activate/deactivate endpoints\n    throw new Error('Role update endpoint not implemented yet');\n  }\n\n  async toggleUserStatus(userId: string): Promise<User> {\n    // Use activate/deactivate endpoints\n    const response = await apiClient.put(`/users/${userId}/activate`);\n    return response.data;\n  }\n}\n\nexport const adminService = new AdminService();\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AA2FvC,MAAMC,YAAY,CAAC;EACjB;EACA,MAAMC,cAAcA,CAAA,EAAyB;IAC3C,IAAI;MAAA,IAAAC,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,cAAc,CAAC;MACpD,MAAMC,WAAW,GAAGF,QAAQ,CAACG,IAAI;MACjCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEL,WAAW,CAAC;;MAEvF;MACA,OAAO;QACLM,UAAU,EAAEN,WAAW,CAACO,WAAW,IAAI,CAAC;QACxCC,UAAU,EAAER,WAAW,CAACS,WAAW,IAAI,CAAC;QACxCC,aAAa,EAAEV,WAAW,CAACW,cAAc,IAAI,CAAC;QAC9CC,cAAc,EAAEZ,WAAW,CAACa,eAAe,IAAI,CAAC;QAChDC,WAAW,EAAEd,WAAW,CAACe,YAAY,IAAI,CAAC;QAC1CC,YAAY,EAAEhB,WAAW,CAACiB,aAAa,IAAI,IAAI;QAAE;QACjDC,gBAAgB,EAAElB,WAAW,CAACmB,iBAAiB,IAAI,CAAC;QACpDC,YAAY,EAAEpB,WAAW,CAACqB,aAAa,IAAI,SAAS;QACpDC,cAAc,EAAE,EAAAzB,qBAAA,GAAAG,WAAW,CAACuB,eAAe,cAAA1B,qBAAA,uBAA3BA,qBAAA,CAA6B2B,GAAG,CAAEC,QAAa,KAAM;UACnEC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC5BC,IAAI,EAAEL,QAAQ,CAACK,IAAI,KAAK,aAAa,GAAG,QAAQ,GAAY,QAAiB;UAC7EC,WAAW,EAAE,GAAGN,QAAQ,CAACO,KAAK,MAAMP,QAAQ,CAACQ,MAAM,EAAE;UACrDC,SAAS,EAAET,QAAQ,CAACS,SAAS;UAC7BC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC,KAAI,EAAE;QACTC,YAAY,EAAE,EAAE;QAAE;QAClBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,6BAA6B,EAAE,IAAInC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEkC,KAAK,CAAC;MAClF;MACA,OAAO;QACLjC,UAAU,EAAE,GAAG;QACfE,UAAU,EAAE,IAAI;QAChBE,aAAa,EAAE,IAAI;QACnBE,cAAc,EAAE,GAAG;QACnBE,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;QAAE;QACtCE,YAAY,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;QAAE;QACxCE,gBAAgB,EAAE,CAAC;QACnBE,YAAY,EAAE,SAAS;QACvBiB,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,GAAG;QACjBhB,cAAc,EAAE,CACd;UACEI,EAAE,EAAE,GAAG;UACPI,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,sCAAsC;UACnDG,SAAS,EAAE,IAAI9B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACnC8B,IAAI,EAAE;QACR,CAAC,EACD;UACET,EAAE,EAAE,GAAG;UACPI,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,qCAAqC;UAClDG,SAAS,EAAE,IAAI9B,IAAI,CAACA,IAAI,CAACoC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACnC,WAAW,CAAC,CAAC;UACtD8B,IAAI,EAAE;QACR,CAAC,CACF;QACDC,YAAY,EAAE,CACZ;UACEV,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,iBAAiB;UACxBS,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE,YAAY;UACxBV,MAAM,EAAE,WAAW;UACnBW,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS;QAC7B,CAAC,EACD;UACEpB,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,6CAA6C;UACpDS,OAAO,EAAE,eAAe;UACxBC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE,YAAY;UACxBV,MAAM,EAAE,WAAW;UACnBW,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,mBAAmB;QAC5B,CAAC;MAEL,CAAC;IACH;EACF;EAEA,MAAMC,eAAeA,CAAA,EAA0B;IAC7C,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMvD,WAAW,GAAG,MAAMN,SAAS,CAACK,GAAG,CAAC,eAAe,CAAC;MACxDG,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,WAAW,CAAC;;MAEvD;MACA,OAAO;QACLiC,MAAM,EAAEjC,WAAW,CAACiC,MAAM,IAAI,SAAS;QACvCuB,QAAQ,EAAE;UACRvB,MAAM,EAAE,EAAAe,qBAAA,GAAAhD,WAAW,CAACyD,QAAQ,cAAAT,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBQ,QAAQ,cAAAP,sBAAA,uBAA9BA,sBAAA,CAAgChB,MAAM,MAAK,SAAS,GAAG,WAAW,GAAG,cAAc;UAC3FyB,aAAa,EAAE,EAAE,CAAE;QACrB,CAAC;QACDC,SAAS,EAAE;UACT1B,MAAM,EAAE,EAAAiB,sBAAA,GAAAlD,WAAW,CAACyD,QAAQ,cAAAP,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBU,QAAQ,cAAAT,sBAAA,uBAA9BA,sBAAA,CAAgClB,MAAM,MAAK,SAAS,GAAG,WAAW,GAAG,cAAc;UAC3FyB,aAAa,EAAE;QACjB,CAAC;QACDG,OAAO,EAAE;UACPC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UAAE;UAC1CC,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACrCC,gBAAgB,EAAE;QACpB,CAAC;QACDP,QAAQ,EAAE;UACRQ,iBAAiB,EAAE,EAAAb,sBAAA,GAAApD,WAAW,CAACyD,QAAQ,cAAAL,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBQ,QAAQ,cAAAP,sBAAA,uBAA9BA,sBAAA,CAAgCpB,MAAM,MAAK,SAAS;UACvEiC,WAAW,EAAE,EAAAZ,sBAAA,GAAAtD,WAAW,CAACyD,QAAQ,cAAAH,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBM,QAAQ,cAAAL,sBAAA,uBAA9BA,sBAAA,CAAgCtB,MAAM,MAAK,SAAS;UACjEkC,WAAW,EAAE,IAAI,CAAE;QACrB;MACF,CAAC;IACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLN,MAAM,EAAE,SAAS;QAAE;QACnBuB,QAAQ,EAAE;UACRvB,MAAM,EAAE,WAAW;UAAE;UACrByB,aAAa,EAAE;QACjB,CAAC;QACDC,SAAS,EAAE;UACT1B,MAAM,EAAE,cAAc;UAAE;UACxByB,aAAa,EAAE;QACjB,CAAC;QACDG,OAAO,EAAE;UACPC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACxCC,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACrCC,gBAAgB,EAAE;QACpB,CAAC;QACDP,QAAQ,EAAE;UACRQ,iBAAiB,EAAE,KAAK;UAAE;UAC1BC,WAAW,EAAE,KAAK;UAAE;UACpBC,WAAW,EAAE,IAAI,CAAE;QACrB;MACF,CAAC;IACH;EACF;;EAEA;EACA,MAAMC,YAAYA,CAACC,MAAc,GAAG,MAAM,EAiBvC;IACD,IAAI;MAAA,IAAAC,qBAAA;MACF;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAAC3E,cAAc,CAAC,CAAC;;MAE/C;MACA,MAAM4E,eAAe,GAAG,MAAM9E,SAAS,CAACK,GAAG,CAAC,mBAAmB,CAAC;;MAEhE;MACA,MAAM0E,SAAS,GAAG,MAAM,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;MAE5C,OAAO;QACLhE,aAAa,EAAE8D,eAAe,CAAC7D,cAAc,IAAI,CAAC;QAClDC,cAAc,EAAE,CAAC;QAAE;QACnBJ,UAAU,EAAE+D,WAAW,CAAC/D,UAAU;QAClCF,UAAU,EAAEiE,WAAW,CAACjE,UAAU;QAClCqE,WAAW,EAAE,CAAC;QAAE;QAChBC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZzC,YAAY,EAAEqC,SAAS,CAACK,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACwD,IAAI,KAAK;UACrDhD,KAAK,EAAEgD,IAAI,CAAChD,KAAK;UACjBS,OAAO,EAAEuC,IAAI,CAACvC,OAAO;UACrBC,WAAW,EAAEsC,IAAI,CAACtC,WAAW,IAAI;QACnC,CAAC,CAAC,CAAC;QACHuC,cAAc,EAAE,EAAAX,qBAAA,GAAAE,eAAe,CAACU,mBAAmB,cAAAZ,qBAAA,uBAAnCA,qBAAA,CAAqCS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;MACtE,CAAC;IACH,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACA,MAAMgC,WAAW,GAAG,MAAM,IAAI,CAAC3E,cAAc,CAAC,CAAC;MAC/C,OAAO;QACLc,aAAa,EAAE,CAAC;QAChBE,cAAc,EAAE,CAAC;QACjBJ,UAAU,EAAE+D,WAAW,CAAC/D,UAAU;QAClCF,UAAU,EAAEiE,WAAW,CAACjE,UAAU;QAClCqE,WAAW,EAAE,CAAC;QACdC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZzC,YAAY,EAAE,EAAE;QAChB6C,cAAc,EAAE;MAClB,CAAC;IACH;EACF;;EAEA;EACA,MAAMP,QAAQA,CAACS,IAAY,GAAG,CAAC,EAAEC,KAAa,GAAG,EAAE,EAAEC,MAAe,EAKjE;IACD,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCJ,IAAI,EAAEA,IAAI,CAACtD,QAAQ,CAAC,CAAC;QACrB2D,SAAS,EAAEJ,KAAK,CAACvD,QAAQ,CAAC,CAAC;QAC3B,IAAIwD,MAAM,IAAI;UAAEA;QAAO,CAAC;MAC1B,CAAC,CAAC;MACF,MAAMrF,WAAW,GAAG,MAAMN,SAAS,CAACK,GAAG,CAAC,gBAAgBuF,MAAM,EAAE,CAAC;;MAEjE;MACApF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEL,WAAW,CAAC;MACvF,OAAO;QACL8E,KAAK,EAAE9E,WAAW,CAAC8E,KAAK,CAACtD,GAAG,CAAEwD,IAAS,KAAM;UAC3CtD,EAAE,EAAEsD,IAAI,CAACtD,EAAE;UACXM,KAAK,EAAEgD,IAAI,CAAChD,KAAK;UACjBS,OAAO,EAAEgD,KAAK,CAACC,OAAO,CAACV,IAAI,CAACvC,OAAO,CAAC,GAAGuC,IAAI,CAACvC,OAAO,CAACkD,IAAI,CAAC,IAAI,CAAC,GAAGX,IAAI,CAACvC,OAAO;UAC7EC,WAAW,EAAE,CAAC;UAAE;UAChBC,UAAU,EAAEqC,IAAI,CAACY,UAAU;UAC3B3D,MAAM,EAAE+C,IAAI,CAACa,iBAAiB;UAC9BjD,QAAQ,EAAEoC,IAAI,CAACc,SAAS;UACxBjD,QAAQ,EAAEmC,IAAI,CAACnC,QAAQ;UACvBC,IAAI,EAAEkC,IAAI,CAAClC,IAAI,IAAI;QACrB,CAAC,CAAC,CAAC;QACHiD,KAAK,EAAE/F,WAAW,CAAC+F,KAAK;QACxBZ,IAAI,EAAEnF,WAAW,CAACmF,IAAI;QACtBa,UAAU,EAAErE,IAAI,CAACsE,IAAI,CAACjG,WAAW,CAAC+F,KAAK,GAAG/F,WAAW,CAACwF,SAAS;MACjE,CAAC;IACH,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA,OAAO;QACLuC,KAAK,EAAE,CACL;UACEpD,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,iBAAiB;UACxBS,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE,YAAY;UACxBV,MAAM,EAAE,WAAW;UACnBW,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU;QACzC,CAAC,EACD;UACEpB,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,6CAA6C;UACpDS,OAAO,EAAE,eAAe;UACxBC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE,YAAY;UACxBV,MAAM,EAAE,YAAY;UACpBW,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,mBAAmB,EAAE,WAAW;QACzC,CAAC,CACF;QACDiD,KAAK,EAAE,GAAG;QACVZ,IAAI,EAAE,CAAC;QACPa,UAAU,EAAE;MACd,CAAC;IACH;EACF;EAEA,MAAME,UAAUA,CAACC,IAAU,EAAElG,IAAoB,EAAiD;IAChG,MAAMmG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7BI,MAAM,CAACC,OAAO,CAACvG,IAAI,CAAC,CAACwG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAC7C,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCP,QAAQ,CAACE,MAAM,CAACI,GAAG,EAAEC,KAAK,CAAC9E,QAAQ,CAAC,CAAC,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,MAAM/B,QAAQ,GAAG,MAAMJ,SAAS,CAACmH,IAAI,CAAC,qBAAqB,EAAET,QAAQ,EAAE;MACrEU,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOhH,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAM8G,UAAUA,CAACC,MAAc,EAAgC;IAC7D,MAAMlH,QAAQ,GAAG,MAAMJ,SAAS,CAACuH,MAAM,CAAC,gBAAgBD,MAAM,EAAE,CAAC;IACjE,OAAOlH,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMiH,UAAUA,CAACF,MAAc,EAAE/G,IAA6B,EAAqB;IACjF,MAAMH,QAAQ,GAAG,MAAMJ,SAAS,CAACyH,GAAG,CAAC,gBAAgBH,MAAM,EAAE,EAAE/G,IAAI,CAAC;IACpE,OAAOH,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAMmH,mBAAmBA,CAAA,EAAgC;IACvD,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;IACjD,CAAC,CAAC,OAAO9E,KAAK,EAAE;MACd;MACA,OAAO,CACL;QACE+E,OAAO,EAAE,GAAG;QACZrF,MAAM,EAAE,YAAY;QACpBsF,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,6BAA6B;QACtCC,UAAU,EAAE,IAAIrH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEiH,OAAO,EAAE,GAAG;QACZrF,MAAM,EAAE,SAAS;QACjBsF,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAE,qBAAqB;QAC9BC,UAAU,EAAE,IAAIrH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;IACH;EACF;EAEA,MAAMqH,aAAaA,CAACV,MAAc,EAAgC;IAChE,MAAMlH,QAAQ,GAAG,MAAMJ,SAAS,CAACmH,IAAI,CAAC,+BAA+BG,MAAM,EAAE,CAAC;IAC9E,OAAOlH,QAAQ,CAACG,IAAI;EACtB;EAEA,MAAM0H,gBAAgBA,CAACX,MAAc,EAAgC;IACnE,MAAMlH,QAAQ,GAAG,MAAMJ,SAAS,CAACmH,IAAI,CAAC,4BAA4BG,MAAM,EAAE,CAAC;IAC3E,OAAOlH,QAAQ,CAACG,IAAI;EACtB;;EAEA;EACA,MAAM2H,QAAQA,CAACzC,IAAY,GAAG,CAAC,EAAEC,KAAa,GAAG,EAAE,EAAEC,MAAe,EAKjE;IACD,IAAI;MACF,MAAMwC,IAAI,GAAG,CAAC1C,IAAI,GAAG,CAAC,IAAIC,KAAK;MAC/B,MAAME,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCsC,IAAI,EAAEA,IAAI,CAAChG,QAAQ,CAAC,CAAC;QACrBuD,KAAK,EAAEA,KAAK,CAACvD,QAAQ,CAAC,CAAC;QACvB,IAAIwD,MAAM,IAAI;UAAEA;QAAO,CAAC;MAC1B,CAAC,CAAC;MACF,MAAMvF,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,UAAUuF,MAAM,EAAE,CAAC;MACxDpF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEL,QAAQ,CAACG,IAAI,CAAC;;MAEnD;MACA,MAAM6H,YAAY,GAAGhI,QAAQ,CAACG,IAAI;MAClC,MAAM8H,KAAK,GAAGD,YAAY,CAACC,KAAK,IAAID,YAAY,CAAC,CAAC;MAClD,MAAM/B,KAAK,GAAG+B,YAAY,CAAC/B,KAAK,IAAIgC,KAAK,CAACC,MAAM;MAEhD,OAAO;QACLD,KAAK,EAAEA,KAAK,CAACvG,GAAG,CAAEW,IAAS,KAAM;UAC/BT,EAAE,EAAES,IAAI,CAACT,EAAE;UACXuG,KAAK,EAAE9F,IAAI,CAAC8F,KAAK;UACjBC,SAAS,EAAE/F,IAAI,CAAC+F,SAAS,IAAI/F,IAAI,CAACgG,IAAI,IAAI,SAAS;UACnDC,IAAI,EAAEjG,IAAI,CAACiG,IAAI,CAACC,WAAW,CAAC,CAAC;UAC7BzC,UAAU,EAAEzD,IAAI,CAACyD,UAAU;UAC3B0C,UAAU,EAAEnG,IAAI,CAACmG,UAAU,IAAInG,IAAI,CAACyD,UAAU;UAC9C2C,SAAS,EAAEpG,IAAI,CAACoG;QAClB,CAAC,CAAC,CAAC;QACHxC,KAAK,EAAEA,KAAK;QACZZ,IAAI,EAAEA,IAAI;QACVa,UAAU,EAAErE,IAAI,CAACsE,IAAI,CAACF,KAAK,GAAGX,KAAK;MACrC,CAAC;IACH,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAAC,CAAC;IACf;EACF;EAEA,MAAMiG,cAAcA,CAACC,MAAc,EAAEL,IAAsB,EAAiB;IAC1E;IACA;IACA,MAAM,IAAIf,KAAK,CAAC,0CAA0C,CAAC;EAC7D;EAEA,MAAMqB,gBAAgBA,CAACD,MAAc,EAAiB;IACpD;IACA,MAAM3I,QAAQ,GAAG,MAAMJ,SAAS,CAACyH,GAAG,CAAC,UAAUsB,MAAM,WAAW,CAAC;IACjE,OAAO3I,QAAQ,CAACG,IAAI;EACtB;AACF;AAEA,OAAO,MAAM0I,YAAY,GAAG,IAAIhJ,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}