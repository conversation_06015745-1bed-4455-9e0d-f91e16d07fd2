{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as MuiThemeProvider, useTheme as usePrivateTheme } from '@mui/private-theming';\nimport exactProp from '@mui/utils/exactProp';\nimport { ThemeContext as StyledEngineThemeContext } from '@mui/styled-engine';\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nimport RtlProvider from \"../RtlProvider/index.js\";\nimport DefaultPropsProvider from \"../DefaultPropsProvider/index.js\";\nimport useLayerOrder from \"./useLayerOrder.js\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nconst EMPTY_THEME = {};\nfunction useThemeScoping(themeId, upperTheme, localTheme) {\n  let isPrivate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  return React.useMemo(() => {\n    const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n    if (typeof localTheme === 'function') {\n      const mergedTheme = localTheme(resolvedTheme);\n      const result = themeId ? {\n        ...upperTheme,\n        [themeId]: mergedTheme\n      } : mergedTheme;\n      // must return a function for the private theme to NOT merge with the upper theme.\n      // see the test case \"use provided theme from a callback\" in ThemeProvider.test.js\n      if (isPrivate) {\n        return () => result;\n      }\n      return result;\n    }\n    return themeId ? {\n      ...upperTheme,\n      [themeId]: localTheme\n    } : {\n      ...upperTheme,\n      ...localTheme\n    };\n  }, [themeId, upperTheme, localTheme, isPrivate]);\n}\n\n/**\n * This component makes the `theme` available down the React tree.\n * It should preferably be used at **the root of your component tree**.\n *\n * <ThemeProvider theme={theme}> // existing use case\n * <ThemeProvider theme={{ id: theme }}> // theme scoping\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme,\n    themeId\n  } = props;\n  const upperTheme = useThemeWithoutDefault(EMPTY_THEME);\n  const upperPrivateTheme = usePrivateTheme() || EMPTY_THEME;\n  if (process.env.NODE_ENV !== 'production') {\n    if (upperTheme === null && typeof localTheme === 'function' || themeId && upperTheme && !upperTheme[themeId] && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const engineTheme = useThemeScoping(themeId, upperTheme, localTheme);\n  const privateTheme = useThemeScoping(themeId, upperPrivateTheme, localTheme, true);\n  const rtlValue = (themeId ? engineTheme[themeId] : engineTheme).direction === 'rtl';\n  const layerOrder = useLayerOrder(engineTheme);\n  return /*#__PURE__*/_jsx(MuiThemeProvider, {\n    theme: privateTheme,\n    children: /*#__PURE__*/_jsx(StyledEngineThemeContext.Provider, {\n      value: engineTheme,\n      children: /*#__PURE__*/_jsx(RtlProvider, {\n        value: rtlValue,\n        children: /*#__PURE__*/_jsxs(DefaultPropsProvider, {\n          value: themeId ? engineTheme[themeId].components : engineTheme.components,\n          children: [layerOrder, children]\n        })\n      })\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n  /**\n   * The design system's unique id for getting the corresponded theme when there are multiple design systems.\n   */\n  themeId: PropTypes.string\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;", "map": {"version": 3, "names": ["React", "PropTypes", "ThemeProvider", "MuiThemeProvider", "useTheme", "usePrivateTheme", "exactProp", "ThemeContext", "StyledEngineThemeContext", "useThemeWithoutDefault", "RtlProvider", "DefaultPropsProvider", "useLayerOrder", "jsxs", "_jsxs", "jsx", "_jsx", "EMPTY_THEME", "useThemeScoping", "themeId", "upperTheme", "localTheme", "isPrivate", "arguments", "length", "undefined", "useMemo", "resolvedTheme", "mergedTheme", "result", "props", "children", "theme", "upperPrivateTheme", "process", "env", "NODE_ENV", "console", "error", "join", "engineTheme", "privateTheme", "rtlValue", "direction", "layerOrder", "Provider", "value", "components", "propTypes", "node", "oneOfType", "func", "object", "isRequired", "string"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as MuiThemeProvider, useTheme as usePrivateTheme } from '@mui/private-theming';\nimport exactProp from '@mui/utils/exactProp';\nimport { ThemeContext as StyledEngineThemeContext } from '@mui/styled-engine';\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nimport RtlProvider from \"../RtlProvider/index.js\";\nimport DefaultPropsProvider from \"../DefaultPropsProvider/index.js\";\nimport useLayerOrder from \"./useLayerOrder.js\";\nimport { jsxs as _jsxs, jsx as _jsx } from \"react/jsx-runtime\";\nconst EMPTY_THEME = {};\nfunction useThemeScoping(themeId, upperTheme, localTheme, isPrivate = false) {\n  return React.useMemo(() => {\n    const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n    if (typeof localTheme === 'function') {\n      const mergedTheme = localTheme(resolvedTheme);\n      const result = themeId ? {\n        ...upperTheme,\n        [themeId]: mergedTheme\n      } : mergedTheme;\n      // must return a function for the private theme to NOT merge with the upper theme.\n      // see the test case \"use provided theme from a callback\" in ThemeProvider.test.js\n      if (isPrivate) {\n        return () => result;\n      }\n      return result;\n    }\n    return themeId ? {\n      ...upperTheme,\n      [themeId]: localTheme\n    } : {\n      ...upperTheme,\n      ...localTheme\n    };\n  }, [themeId, upperTheme, localTheme, isPrivate]);\n}\n\n/**\n * This component makes the `theme` available down the React tree.\n * It should preferably be used at **the root of your component tree**.\n *\n * <ThemeProvider theme={theme}> // existing use case\n * <ThemeProvider theme={{ id: theme }}> // theme scoping\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme,\n    themeId\n  } = props;\n  const upperTheme = useThemeWithoutDefault(EMPTY_THEME);\n  const upperPrivateTheme = usePrivateTheme() || EMPTY_THEME;\n  if (process.env.NODE_ENV !== 'production') {\n    if (upperTheme === null && typeof localTheme === 'function' || themeId && upperTheme && !upperTheme[themeId] && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const engineTheme = useThemeScoping(themeId, upperTheme, localTheme);\n  const privateTheme = useThemeScoping(themeId, upperPrivateTheme, localTheme, true);\n  const rtlValue = (themeId ? engineTheme[themeId] : engineTheme).direction === 'rtl';\n  const layerOrder = useLayerOrder(engineTheme);\n  return /*#__PURE__*/_jsx(MuiThemeProvider, {\n    theme: privateTheme,\n    children: /*#__PURE__*/_jsx(StyledEngineThemeContext.Provider, {\n      value: engineTheme,\n      children: /*#__PURE__*/_jsx(RtlProvider, {\n        value: rtlValue,\n        children: /*#__PURE__*/_jsxs(DefaultPropsProvider, {\n          value: themeId ? engineTheme[themeId].components : engineTheme.components,\n          children: [layerOrder, children]\n        })\n      })\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n  /**\n   * The design system's unique id for getting the corresponded theme when there are multiple design systems.\n   */\n  themeId: PropTypes.string\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,IAAIC,gBAAgB,EAAEC,QAAQ,IAAIC,eAAe,QAAQ,sBAAsB;AACrG,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,YAAY,IAAIC,wBAAwB,QAAQ,oBAAoB;AAC7E,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,IAAI,IAAIC,KAAK,EAAEC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC9D,MAAMC,WAAW,GAAG,CAAC,CAAC;AACtB,SAASC,eAAeA,CAACC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAqB;EAAA,IAAnBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACzE,OAAOvB,KAAK,CAAC0B,OAAO,CAAC,MAAM;IACzB,MAAMC,aAAa,GAAGR,OAAO,GAAGC,UAAU,CAACD,OAAO,CAAC,IAAIC,UAAU,GAAGA,UAAU;IAC9E,IAAI,OAAOC,UAAU,KAAK,UAAU,EAAE;MACpC,MAAMO,WAAW,GAAGP,UAAU,CAACM,aAAa,CAAC;MAC7C,MAAME,MAAM,GAAGV,OAAO,GAAG;QACvB,GAAGC,UAAU;QACb,CAACD,OAAO,GAAGS;MACb,CAAC,GAAGA,WAAW;MACf;MACA;MACA,IAAIN,SAAS,EAAE;QACb,OAAO,MAAMO,MAAM;MACrB;MACA,OAAOA,MAAM;IACf;IACA,OAAOV,OAAO,GAAG;MACf,GAAGC,UAAU;MACb,CAACD,OAAO,GAAGE;IACb,CAAC,GAAG;MACF,GAAGD,UAAU;MACb,GAAGC;IACL,CAAC;EACH,CAAC,EAAE,CAACF,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,CAAC,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpB,aAAaA,CAAC4B,KAAK,EAAE;EAC5B,MAAM;IACJC,QAAQ;IACRC,KAAK,EAAEX,UAAU;IACjBF;EACF,CAAC,GAAGW,KAAK;EACT,MAAMV,UAAU,GAAGX,sBAAsB,CAACQ,WAAW,CAAC;EACtD,MAAMgB,iBAAiB,GAAG5B,eAAe,CAAC,CAAC,IAAIY,WAAW;EAC1D,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhB,UAAU,KAAK,IAAI,IAAI,OAAOC,UAAU,KAAK,UAAU,IAAIF,OAAO,IAAIC,UAAU,IAAI,CAACA,UAAU,CAACD,OAAO,CAAC,IAAI,OAAOE,UAAU,KAAK,UAAU,EAAE;MAChJgB,OAAO,CAACC,KAAK,CAAC,CAAC,8EAA8E,EAAE,oDAAoD,EAAE,EAAE,EAAE,qCAAqC,EAAE,iEAAiE,GAAG,4BAA4B,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/S;EACF;EACA,MAAMC,WAAW,GAAGtB,eAAe,CAACC,OAAO,EAAEC,UAAU,EAAEC,UAAU,CAAC;EACpE,MAAMoB,YAAY,GAAGvB,eAAe,CAACC,OAAO,EAAEc,iBAAiB,EAAEZ,UAAU,EAAE,IAAI,CAAC;EAClF,MAAMqB,QAAQ,GAAG,CAACvB,OAAO,GAAGqB,WAAW,CAACrB,OAAO,CAAC,GAAGqB,WAAW,EAAEG,SAAS,KAAK,KAAK;EACnF,MAAMC,UAAU,GAAGhC,aAAa,CAAC4B,WAAW,CAAC;EAC7C,OAAO,aAAaxB,IAAI,CAACb,gBAAgB,EAAE;IACzC6B,KAAK,EAAES,YAAY;IACnBV,QAAQ,EAAE,aAAaf,IAAI,CAACR,wBAAwB,CAACqC,QAAQ,EAAE;MAC7DC,KAAK,EAAEN,WAAW;MAClBT,QAAQ,EAAE,aAAaf,IAAI,CAACN,WAAW,EAAE;QACvCoC,KAAK,EAAEJ,QAAQ;QACfX,QAAQ,EAAE,aAAajB,KAAK,CAACH,oBAAoB,EAAE;UACjDmC,KAAK,EAAE3B,OAAO,GAAGqB,WAAW,CAACrB,OAAO,CAAC,CAAC4B,UAAU,GAAGP,WAAW,CAACO,UAAU;UACzEhB,QAAQ,EAAE,CAACa,UAAU,EAAEb,QAAQ;QACjC,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ;AACAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlC,aAAa,CAAC8C,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjB,QAAQ,EAAE9B,SAAS,CAACgD,IAAI;EACxB;AACF;AACA;EACEjB,KAAK,EAAE/B,SAAS,CAACiD,SAAS,CAAC,CAACjD,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAACmD,MAAM,CAAC,CAAC,CAACC,UAAU;EACzE;AACF;AACA;EACElC,OAAO,EAAElB,SAAS,CAACqD;AACrB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIpB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlC,aAAa,CAAC8C,SAAS,GAAG1C,SAAS,CAACJ,aAAa,CAAC8C,SAAS,CAAC,GAAG,KAAK,CAAC;AAC/G;AACA,eAAe9C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}