{"ast": null, "code": "export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !Number.isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, componentName, location);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nconst integerPropType = process.env.NODE_ENV === 'production' ? validatorNoop : validator;\nexport default integerPropType;", "map": {"version": 3, "names": ["getTypeByValue", "value", "valueType", "Number", "isNaN", "isFinite", "Math", "floor", "constructor", "name", "requiredInteger", "props", "propName", "componentName", "location", "propValue", "isInteger", "propType", "RangeError", "validator", "undefined", "validatorNoop", "isRequired", "integerPropType", "process", "env", "NODE_ENV"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/integerPropType/integerPropType.js"], "sourcesContent": ["export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !Number.isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, componentName, location);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nconst integerPropType = process.env.NODE_ENV === 'production' ? validatorNoop : validator;\nexport default integerPropType;"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,KAAK,EAAE;EACpC,MAAMC,SAAS,GAAG,OAAOD,KAAK;EAC9B,QAAQC,SAAS;IACf,KAAK,QAAQ;MACX,IAAIC,MAAM,CAACC,KAAK,CAACH,KAAK,CAAC,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IAAI,CAACE,MAAM,CAACE,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAC3B,OAAO,UAAU;MACnB;MACA,IAAIA,KAAK,KAAKK,IAAI,CAACC,KAAK,CAACN,KAAK,CAAC,EAAE;QAC/B,OAAO,OAAO;MAChB;MACA,OAAO,QAAQ;IACjB,KAAK,QAAQ;MACX,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,MAAM;MACf;MACA,OAAOA,KAAK,CAACO,WAAW,CAACC,IAAI;IAC/B;MACE,OAAOP,SAAS;EACpB;AACF;AACA,SAASQ,eAAeA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACjE,MAAMC,SAAS,GAAGJ,KAAK,CAACC,QAAQ,CAAC;EACjC,IAAIG,SAAS,IAAI,IAAI,IAAI,CAACZ,MAAM,CAACa,SAAS,CAACD,SAAS,CAAC,EAAE;IACrD,MAAME,QAAQ,GAAGjB,cAAc,CAACe,SAAS,CAAC;IAC1C,OAAO,IAAIG,UAAU,CAAC,WAAWJ,QAAQ,MAAMF,QAAQ,gBAAgBK,QAAQ,oBAAoBJ,aAAa,2BAA2B,CAAC;EAC9I;EACA,OAAO,IAAI;AACb;AACA,SAASM,SAASA,CAACR,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAC3D,MAAMC,SAAS,GAAGJ,KAAK,CAACC,QAAQ,CAAC;EACjC,IAAIG,SAAS,KAAKK,SAAS,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,OAAOV,eAAe,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,CAAC;AAClE;AACA,SAASO,aAAaA,CAAA,EAAG;EACvB,OAAO,IAAI;AACb;AACAF,SAAS,CAACG,UAAU,GAAGZ,eAAe;AACtCW,aAAa,CAACC,UAAU,GAAGD,aAAa;AACxC,MAAME,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,aAAa,GAAGF,SAAS;AACzF,eAAeI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}