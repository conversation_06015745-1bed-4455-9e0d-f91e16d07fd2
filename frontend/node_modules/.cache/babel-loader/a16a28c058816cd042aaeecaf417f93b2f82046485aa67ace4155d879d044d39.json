{"ast": null, "code": "// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize() {\n  let win = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window;\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = win.document.documentElement.clientWidth;\n  return win.innerWidth - documentWidth;\n}", "map": {"version": 3, "names": ["getScrollbarSize", "win", "arguments", "length", "undefined", "window", "documentWidth", "document", "documentElement", "clientWidth", "innerWidth"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/getScrollbarSize/getScrollbarSize.js"], "sourcesContent": ["// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(win = window) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = win.document.documentElement.clientWidth;\n  return win.innerWidth - documentWidth;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,gBAAgBA,CAAA,EAAe;EAAA,IAAdC,GAAG,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,MAAM;EACnD;EACA,MAAMC,aAAa,GAAGL,GAAG,CAACM,QAAQ,CAACC,eAAe,CAACC,WAAW;EAC9D,OAAOR,GAAG,CAACS,UAAU,GAAGJ,aAAa;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}