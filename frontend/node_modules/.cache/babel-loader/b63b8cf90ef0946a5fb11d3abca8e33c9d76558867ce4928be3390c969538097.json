{"ast": null, "code": "/**\n * Authentication service for API calls\n */\nimport { apiClient } from './apiClient';\nclass AuthService {\n  setToken(token) {\n    apiClient.setAuthToken(token);\n  }\n  clearToken() {\n    apiClient.clearAuthToken();\n  }\n  async login(credentials) {\n    const formData = new FormData();\n    formData.append('username', credentials.email);\n    formData.append('password', credentials.password);\n    return apiClient.post('/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      skipAuth: true\n    });\n  }\n  async signup(userData) {\n    return apiClient.post('/auth/signup', userData, {\n      skipAuth: true\n    });\n  }\n  async logout() {\n    try {\n      await apiClient.post('/auth/logout');\n    } catch (error) {\n      // Ignore logout errors\n      console.warn('Logout request failed:', error);\n    } finally {\n      this.clearToken();\n    }\n  }\n  async getCurrentUser() {\n    return apiClient.get('/users/me');\n  }\n  async updateProfile(userData) {\n    return apiClient.put('/users/me', userData);\n  }\n  async changePassword(currentPassword, newPassword) {\n    return apiClient.post('/users/me/change-password', {\n      current_password: currentPassword,\n      new_password: newPassword\n    });\n  }\n  async updatePreferences(preferences) {\n    return apiClient.put('/users/me/preferences', preferences);\n  }\n\n  // Health check\n  async healthCheck() {\n    return apiClient.healthCheck();\n  }\n}\nexport const authService = new AuthService();", "map": {"version": 3, "names": ["apiClient", "AuthService", "setToken", "token", "setAuthToken", "clearToken", "clearAuthToken", "login", "credentials", "formData", "FormData", "append", "email", "password", "post", "headers", "<PERSON><PERSON><PERSON>", "signup", "userData", "logout", "error", "console", "warn", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "updatePreferences", "preferences", "healthCheck", "authService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts"], "sourcesContent": ["/**\n * Authentication service for API calls\n */\nimport { apiClient } from './apiClient';\nimport { User, AuthResponse, LoginRequest, SignupRequest, ApiResponse } from '../types';\n\nclass AuthService {\n  setToken(token: string) {\n    apiClient.setAuthToken(token);\n  }\n\n  clearToken() {\n    apiClient.clearAuthToken();\n  }\n\n  async login(credentials: LoginRequest): Promise<AuthResponse> {\n    const formData = new FormData();\n    formData.append('username', credentials.email);\n    formData.append('password', credentials.password);\n\n    return apiClient.post<AuthResponse>('/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n      skipAuth: true,\n    });\n  }\n\n  async signup(userData: SignupRequest): Promise<ApiResponse> {\n    return apiClient.post<ApiResponse>('/auth/signup', userData, { skipAuth: true });\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await apiClient.post('/auth/logout');\n    } catch (error) {\n      // Ignore logout errors\n      console.warn('Logout request failed:', error);\n    } finally {\n      this.clearToken();\n    }\n  }\n\n  async getCurrentUser(): Promise<User> {\n    return apiClient.get<User>('/users/me');\n  }\n\n  async updateProfile(userData: Partial<User>): Promise<User> {\n    return apiClient.put<User>('/users/me', userData);\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {\n    return apiClient.post<ApiResponse>('/users/me/change-password', {\n      current_password: currentPassword,\n      new_password: newPassword,\n    });\n  }\n\n  async updatePreferences(preferences: Record<string, any>): Promise<ApiResponse> {\n    return apiClient.put<ApiResponse>('/users/me/preferences', preferences);\n  }\n\n  // Health check\n  async healthCheck(): Promise<any> {\n    return apiClient.healthCheck();\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,aAAa;AAGvC,MAAMC,WAAW,CAAC;EAChBC,QAAQA,CAACC,KAAa,EAAE;IACtBH,SAAS,CAACI,YAAY,CAACD,KAAK,CAAC;EAC/B;EAEAE,UAAUA,CAAA,EAAG;IACXL,SAAS,CAACM,cAAc,CAAC,CAAC;EAC5B;EAEA,MAAMC,KAAKA,CAACC,WAAyB,EAAyB;IAC5D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACI,KAAK,CAAC;IAC9CH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACK,QAAQ,CAAC;IAEjD,OAAOb,SAAS,CAACc,IAAI,CAAe,aAAa,EAAEL,QAAQ,EAAE;MAC3DM,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,MAAMC,MAAMA,CAACC,QAAuB,EAAwB;IAC1D,OAAOlB,SAAS,CAACc,IAAI,CAAc,cAAc,EAAEI,QAAQ,EAAE;MAAEF,QAAQ,EAAE;IAAK,CAAC,CAAC;EAClF;EAEA,MAAMG,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAMnB,SAAS,CAACc,IAAI,CAAC,cAAc,CAAC;IACtC,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd;MACAC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEF,KAAK,CAAC;IAC/C,CAAC,SAAS;MACR,IAAI,CAACf,UAAU,CAAC,CAAC;IACnB;EACF;EAEA,MAAMkB,cAAcA,CAAA,EAAkB;IACpC,OAAOvB,SAAS,CAACwB,GAAG,CAAO,WAAW,CAAC;EACzC;EAEA,MAAMC,aAAaA,CAACP,QAAuB,EAAiB;IAC1D,OAAOlB,SAAS,CAAC0B,GAAG,CAAO,WAAW,EAAER,QAAQ,CAAC;EACnD;EAEA,MAAMS,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAwB;IACvF,OAAO7B,SAAS,CAACc,IAAI,CAAc,2BAA2B,EAAE;MAC9DgB,gBAAgB,EAAEF,eAAe;MACjCG,YAAY,EAAEF;IAChB,CAAC,CAAC;EACJ;EAEA,MAAMG,iBAAiBA,CAACC,WAAgC,EAAwB;IAC9E,OAAOjC,SAAS,CAAC0B,GAAG,CAAc,uBAAuB,EAAEO,WAAW,CAAC;EACzE;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAAiB;IAChC,OAAOlC,SAAS,CAACkC,WAAW,CAAC,CAAC;EAChC;AACF;AAEA,OAAO,MAAMC,WAAW,GAAG,IAAIlC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}