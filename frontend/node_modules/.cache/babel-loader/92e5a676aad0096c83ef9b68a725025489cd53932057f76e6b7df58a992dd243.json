{"ast": null, "code": "/**\n * Search service for API calls\n */\nimport { apiClient } from './apiClient';\nclass SearchService {\n  async search(searchRequest) {\n    return apiClient.post('/search', searchRequest);\n  }\n  async getSuggestions(query, limit = 10) {\n    const response = await apiClient.get('/search/suggestions', {\n      params: {\n        q: query,\n        limit\n      }\n    });\n    return response.suggestions;\n  }\n  async findSimilarChunks(chunkId, limit = 10, scoreThreshold = 0.8, excludeSameBook = false) {\n    return apiClient.post('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook\n    });\n  }\n  async getSearchHistory(limit = 50) {\n    return apiClient.get('/search/history', {\n      params: {\n        limit\n      }\n    });\n  }\n  async getSearchAnalytics() {\n    return apiClient.get('/search/analytics');\n  }\n}\nexport const searchService = new SearchService();", "map": {"version": 3, "names": ["apiClient", "SearchService", "search", "searchRequest", "post", "getSuggestions", "query", "limit", "response", "get", "params", "q", "suggestions", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunkId", "scoreThreshold", "excludeSameBook", "chunk_id", "score_threshold", "exclude_same_book", "getSearchHistory", "getSearchAnalytics", "searchService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/searchService.ts"], "sourcesContent": ["/**\n * Search service for API calls\n */\nimport { apiClient } from './apiClient';\nimport {\n  SearchRequest,\n  SearchResponse,\n  SearchResult,\n  SearchHistoryItem,\n  PaginatedResponse\n} from '../types';\n\nclass SearchService {\n\n  async search(searchRequest: SearchRequest): Promise<SearchResponse> {\n    return apiClient.post<SearchResponse>('/search', searchRequest);\n  }\n\n  async getSuggestions(query: string, limit: number = 10): Promise<string[]> {\n    const response = await apiClient.get<{ suggestions: string[] }>('/search/suggestions', {\n      params: { q: query, limit },\n    });\n    return response.suggestions;\n  }\n\n  async findSimilarChunks(\n    chunkId: string,\n    limit: number = 10,\n    scoreThreshold: number = 0.8,\n    excludeSameBook: boolean = false\n  ): Promise<SearchResult[]> {\n    return apiClient.post<SearchResult[]>('/search/similar', {\n      chunk_id: chunkId,\n      limit,\n      score_threshold: scoreThreshold,\n      exclude_same_book: excludeSameBook,\n    });\n  }\n\n  async getSearchHistory(limit: number = 50): Promise<PaginatedResponse<SearchHistoryItem>> {\n    return apiClient.get<PaginatedResponse<SearchHistoryItem>>('/search/history', {\n      params: { limit },\n    });\n  }\n\n  async getSearchAnalytics(): Promise<any> {\n    return apiClient.get('/search/analytics');\n  }\n}\n\nexport const searchService = new SearchService();\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,aAAa;AASvC,MAAMC,aAAa,CAAC;EAElB,MAAMC,MAAMA,CAACC,aAA4B,EAA2B;IAClE,OAAOH,SAAS,CAACI,IAAI,CAAiB,SAAS,EAAED,aAAa,CAAC;EACjE;EAEA,MAAME,cAAcA,CAACC,KAAa,EAAEC,KAAa,GAAG,EAAE,EAAqB;IACzE,MAAMC,QAAQ,GAAG,MAAMR,SAAS,CAACS,GAAG,CAA4B,qBAAqB,EAAE;MACrFC,MAAM,EAAE;QAAEC,CAAC,EAAEL,KAAK;QAAEC;MAAM;IAC5B,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACI,WAAW;EAC7B;EAEA,MAAMC,iBAAiBA,CACrBC,OAAe,EACfP,KAAa,GAAG,EAAE,EAClBQ,cAAsB,GAAG,GAAG,EAC5BC,eAAwB,GAAG,KAAK,EACP;IACzB,OAAOhB,SAAS,CAACI,IAAI,CAAiB,iBAAiB,EAAE;MACvDa,QAAQ,EAAEH,OAAO;MACjBP,KAAK;MACLW,eAAe,EAAEH,cAAc;MAC/BI,iBAAiB,EAAEH;IACrB,CAAC,CAAC;EACJ;EAEA,MAAMI,gBAAgBA,CAACb,KAAa,GAAG,EAAE,EAAiD;IACxF,OAAOP,SAAS,CAACS,GAAG,CAAuC,iBAAiB,EAAE;MAC5EC,MAAM,EAAE;QAAEH;MAAM;IAClB,CAAC,CAAC;EACJ;EAEA,MAAMc,kBAAkBA,CAAA,EAAiB;IACvC,OAAOrB,SAAS,CAACS,GAAG,CAAC,mBAAmB,CAAC;EAC3C;AACF;AAEA,OAAO,MAAMa,aAAa,GAAG,IAAIrB,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}