{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/components/TopicViewer.tsx\",\n  _s = $RefreshSig$();\n/**\n * TopicViewer Component - Production-quality offline topic search with citation viewer\n * \n * Features:\n * - Semantic search using local embeddings\n * - Results grouped by medical section types (Diagnosis, Treatment, etc.)\n * - Expandable citation accordions\n * - Full text viewing with metadata\n * - Error handling and loading states\n * - Responsive design\n */\n\nimport React, { useState, useCallback } from 'react';\nimport { Box, TextField, Button, Typography, Paper, Accordion, AccordionSummary, AccordionDetails, Chip, Alert, CircularProgress, Card, CardContent, Divider, IconButton, Tooltip, Badge, Stack, Container } from '@mui/material';\nimport { ExpandMore as ExpandMoreIcon, Search as SearchIcon, Book as BookIcon, LocalHospital as MedicalIcon, Visibility as ViewIcon, VisibilityOff as HideIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\n\n// Types for the topic search functionality\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Styled components for better visual hierarchy\nconst SearchContainer = styled(Paper)(({\n  theme\n}) => ({\n  padding: theme.spacing(3),\n  marginBottom: theme.spacing(3),\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  color: 'white'\n}));\n_c = SearchContainer;\nconst ResultsContainer = styled(Box)(({\n  theme\n}) => ({\n  marginTop: theme.spacing(2)\n}));\n_c2 = ResultsContainer;\nconst SectionAccordion = styled(Accordion)(({\n  theme\n}) => ({\n  marginBottom: theme.spacing(2),\n  '&:before': {\n    display: 'none'\n  },\n  boxShadow: theme.shadows[2]\n}));\n_c3 = SectionAccordion;\nconst CitationCard = styled(Card)(({\n  theme\n}) => ({\n  marginBottom: theme.spacing(2),\n  border: `1px solid ${theme.palette.divider}`,\n  '&:hover': {\n    boxShadow: theme.shadows[4],\n    transform: 'translateY(-2px)',\n    transition: 'all 0.2s ease-in-out'\n  }\n}));\n_c4 = CitationCard;\nconst ScoreChip = styled(Chip)(({\n  theme,\n  score\n}) => ({\n  backgroundColor: score > 0.8 ? theme.palette.success.main : score > 0.6 ? theme.palette.warning.main : theme.palette.error.main,\n  color: 'white',\n  fontWeight: 'bold'\n}));\n\n// Section type icons and colors\n_c5 = ScoreChip;\nconst sectionConfig = {\n  'Diagnosis': {\n    icon: '🔍',\n    color: '#1976d2',\n    description: 'Diagnostic criteria and methods'\n  },\n  'Treatment': {\n    icon: '💊',\n    color: '#2e7d32',\n    description: 'Treatment protocols and medications'\n  },\n  'Prognosis': {\n    icon: '📈',\n    color: '#ed6c02',\n    description: 'Outcomes and prognosis information'\n  },\n  'Anatomy': {\n    icon: '🫀',\n    color: '#9c27b0',\n    description: 'Anatomical structures and systems'\n  },\n  'Pathophysiology': {\n    icon: '🧬',\n    color: '#d32f2f',\n    description: 'Disease mechanisms and pathways'\n  },\n  'Other': {\n    icon: '📚',\n    color: '#757575',\n    description: 'General medical information'\n  }\n};\nconst TopicViewer = () => {\n  _s();\n  const [query, setQuery] = useState('');\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [expandedSections, setExpandedSections] = useState(new Set());\n  const [expandedTexts, setExpandedTexts] = useState(new Set());\n\n  // Handle search submission\n  const handleSearch = useCallback(async () => {\n    if (!query.trim()) {\n      setError('Please enter a search query');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setResults(null);\n    try {\n      const response = await fetch('/api/v1/search/topic', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          query: query.trim(),\n          limit: 20,\n          score_threshold: 0.6\n        })\n      });\n      if (!response.ok) {\n        if (response.status === 403) {\n          throw new Error('Authentication required. Please log in to search.');\n        }\n        throw new Error(`Search failed: ${response.statusText}`);\n      }\n      const data = await response.json();\n      setResults(data);\n\n      // Auto-expand sections with results\n      const sectionsWithResults = Object.keys(data.grouped_results);\n      setExpandedSections(new Set(sectionsWithResults.slice(0, 2))); // Expand first 2 sections\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Search failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  }, [query]);\n\n  // Handle section accordion toggle\n  const handleSectionToggle = useCallback(section => {\n    setExpandedSections(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(section)) {\n        newSet.delete(section);\n      } else {\n        newSet.add(section);\n      }\n      return newSet;\n    });\n  }, []);\n\n  // Handle text expansion toggle\n  const handleTextToggle = useCallback(resultId => {\n    setExpandedTexts(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(resultId)) {\n        newSet.delete(resultId);\n      } else {\n        newSet.add(resultId);\n      }\n      return newSet;\n    });\n  }, []);\n\n  // Truncate text for preview\n  const truncateText = (text, maxLength = 200) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = event => {\n    if (event.key === 'Enter') {\n      handleSearch();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        align: \"center\",\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(MedicalIcon, {\n          sx: {\n            mr: 2,\n            verticalAlign: 'middle'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), \"Medical Topic Search\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchContainer, {\n        elevation: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Search Medical Literature\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 3,\n            opacity: 0.9\n          },\n          children: \"Search through medical textbooks using AI-powered semantic search. Results are grouped by medical sections for easy navigation.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            variant: \"outlined\",\n            placeholder: \"e.g., diabetes mellitus diagnosis, kawasaki disease treatment...\",\n            value: query,\n            onChange: e => setQuery(e.target.value),\n            onKeyPress: handleKeyPress,\n            disabled: loading,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                '& fieldset': {\n                  borderColor: 'rgba(255, 255, 255, 0.3)'\n                },\n                '&:hover fieldset': {\n                  borderColor: 'rgba(255, 255, 255, 0.5)'\n                },\n                '&.Mui-focused fieldset': {\n                  borderColor: 'white'\n                }\n              },\n              '& .MuiInputBase-input': {\n                color: 'white'\n              },\n              '& .MuiInputBase-input::placeholder': {\n                color: 'rgba(255, 255, 255, 0.7)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleSearch,\n            disabled: loading || !query.trim(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 85\n            }, this),\n            sx: {\n              minWidth: 120,\n              backgroundColor: 'rgba(255, 255, 255, 0.2)',\n              '&:hover': {\n                backgroundColor: 'rgba(255, 255, 255, 0.3)'\n              }\n            },\n            children: loading ? 'Searching...' : 'Search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 3\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), results && /*#__PURE__*/_jsxDEV(ResultsContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Search Results for \\\"\", results.query, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: results.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), results.suggestions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Suggestions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              flexWrap: \"wrap\",\n              children: results.suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: suggestion,\n                size: \"small\",\n                variant: \"outlined\",\n                onClick: () => setQuery(suggestion),\n                sx: {\n                  mb: 1\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), Object.entries(results.grouped_results).map(([sectionType, sectionResults]) => {\n          const config = sectionConfig[sectionType] || sectionConfig.Other;\n          const isExpanded = expandedSections.has(sectionType);\n          return /*#__PURE__*/_jsxDEV(SectionAccordion, {\n            expanded: isExpanded,\n            onChange: () => handleSectionToggle(sectionType),\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                alignItems: \"center\",\n                spacing: 2,\n                sx: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginRight: 8,\n                      fontSize: '1.2em'\n                    },\n                    children: config.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this), sectionType]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  badgeContent: sectionResults.length,\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: config.description,\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                    sx: {\n                      color: 'text.secondary',\n                      fontSize: 16\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: sectionResults.map((result, index) => {\n                const resultId = `${sectionType}-${index}`;\n                const isTextExpanded = expandedTexts.has(resultId);\n                const displayText = isTextExpanded ? result.text : truncateText(result.text);\n                return /*#__PURE__*/_jsxDEV(CitationCard, {\n                  elevation: 1,\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"flex-start\",\n                      sx: {\n                        mb: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        alignItems: \"center\",\n                        children: [/*#__PURE__*/_jsxDEV(BookIcon, {\n                          sx: {\n                            color: config.color\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 341,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle1\",\n                          fontWeight: \"bold\",\n                          children: result.metadata.book_title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 342,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(ScoreChip, {\n                          score: result.score,\n                          label: `${(result.score * 100).toFixed(1)}%`,\n                          size: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 345,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: isTextExpanded ? \"Show less\" : \"Show full text\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          size: \"small\",\n                          onClick: () => handleTextToggle(resultId),\n                          children: isTextExpanded ? /*#__PURE__*/_jsxDEV(HideIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 357,\n                            columnNumber: 53\n                          }, this) : /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 357,\n                            columnNumber: 68\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 353,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 2,\n                      sx: {\n                        mb: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: `Chapter: ${result.metadata.chapter}`,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `Page: ${result.metadata.page_number}`,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `Author: ${result.metadata.author}`,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                      sx: {\n                        my: 2\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        lineHeight: 1.6\n                      },\n                      children: displayText\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 2,\n                      sx: {\n                        mt: 2\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: [result.metadata.word_count, \" words\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: [result.metadata.char_count, \" characters\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 27\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this)]\n          }, sectionType, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 17\n          }, this);\n        }), Object.keys(results.grouped_results).length === 0 && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 4,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"No results found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: \"Try using different keywords or broader medical terms.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(TopicViewer, \"PixGjQ7msufMX4V9Ez6nyEtg9w0=\");\n_c6 = TopicViewer;\nexport default TopicViewer;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"SearchContainer\");\n$RefreshReg$(_c2, \"ResultsContainer\");\n$RefreshReg$(_c3, \"SectionAccordion\");\n$RefreshReg$(_c4, \"CitationCard\");\n$RefreshReg$(_c5, \"ScoreChip\");\n$RefreshReg$(_c6, \"TopicViewer\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "<PERSON><PERSON>", "CircularProgress", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "<PERSON><PERSON>", "Container", "ExpandMore", "ExpandMoreIcon", "Search", "SearchIcon", "Book", "BookIcon", "LocalHospital", "MedicalIcon", "Visibility", "ViewIcon", "VisibilityOff", "HideIcon", "Info", "InfoIcon", "styled", "jsxDEV", "_jsxDEV", "SearchContainer", "theme", "padding", "spacing", "marginBottom", "background", "color", "_c", "ResultsContainer", "marginTop", "_c2", "SectionAccordion", "display", "boxShadow", "shadows", "_c3", "CitationCard", "border", "palette", "divider", "transform", "transition", "_c4", "ScoreChip", "score", "backgroundColor", "success", "main", "warning", "error", "fontWeight", "_c5", "sectionConfig", "icon", "description", "Topic<PERSON>iewer", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "results", "setResults", "loading", "setLoading", "setError", "expandedSections", "setExpandedSections", "Set", "expandedTexts", "setExpandedTexts", "handleSearch", "trim", "response", "fetch", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "limit", "score_threshold", "ok", "status", "Error", "statusText", "data", "json", "sectionsWithResults", "Object", "keys", "grouped_results", "slice", "err", "message", "handleSectionToggle", "section", "prev", "newSet", "has", "delete", "add", "handleTextToggle", "resultId", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "handleKeyPress", "event", "key", "max<PERSON><PERSON><PERSON>", "children", "sx", "py", "variant", "component", "gutterBottom", "align", "mb", "mr", "verticalAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "opacity", "direction", "alignItems", "fullWidth", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "disabled", "borderColor", "onClick", "startIcon", "size", "min<PERSON><PERSON><PERSON>", "severity", "p", "suggestions", "mt", "flexWrap", "map", "suggestion", "index", "label", "entries", "sectionType", "sectionResults", "config", "Other", "isExpanded", "expanded", "expandIcon", "width", "style", "marginRight", "fontSize", "badgeContent", "title", "result", "isTextExpanded", "displayText", "justifyContent", "metadata", "book_title", "toFixed", "chapter", "page_number", "author", "my", "lineHeight", "word_count", "char_count", "textAlign", "_c6", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/components/TopicViewer.tsx"], "sourcesContent": ["/**\n * TopicViewer Component - Production-quality offline topic search with citation viewer\n * \n * Features:\n * - Semantic search using local embeddings\n * - Results grouped by medical section types (Diagnosis, Treatment, etc.)\n * - Expandable citation accordions\n * - Full text viewing with metadata\n * - Error handling and loading states\n * - Responsive design\n */\n\nimport React, { useState, useCallback } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Chip,\n  Alert,\n  CircularProgress,\n  Card,\n  CardContent,\n  Divider,\n  IconButton,\n  Tooltip,\n  Badge,\n  Stack,\n  Container\n} from '@mui/material';\nimport {\n  ExpandMore as ExpandMoreIcon,\n  Search as SearchIcon,\n  Book as BookIcon,\n  LocalHospital as MedicalIcon,\n  Visibility as ViewIcon,\n  VisibilityOff as HideIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { styled } from '@mui/material/styles';\n\n// Types for the topic search functionality\ninterface TopicSearchResult {\n  text: string;\n  score: number;\n  metadata: {\n    section_type: string;\n    book_title: string;\n    chapter: string;\n    page_number: number;\n    author: string;\n    word_count: number;\n    char_count: number;\n  };\n}\n\ninterface TopicSearchResponse {\n  query: string;\n  total_results: number;\n  grouped_results: Record<string, TopicSearchResult[]>;\n  message: string;\n  suggestions: string[];\n}\n\n// Styled components for better visual hierarchy\nconst SearchContainer = styled(Paper)(({ theme }) => ({\n  padding: theme.spacing(3),\n  marginBottom: theme.spacing(3),\n  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n  color: 'white',\n}));\n\nconst ResultsContainer = styled(Box)(({ theme }) => ({\n  marginTop: theme.spacing(2),\n}));\n\nconst SectionAccordion = styled(Accordion)(({ theme }) => ({\n  marginBottom: theme.spacing(2),\n  '&:before': {\n    display: 'none',\n  },\n  boxShadow: theme.shadows[2],\n}));\n\nconst CitationCard = styled(Card)(({ theme }) => ({\n  marginBottom: theme.spacing(2),\n  border: `1px solid ${theme.palette.divider}`,\n  '&:hover': {\n    boxShadow: theme.shadows[4],\n    transform: 'translateY(-2px)',\n    transition: 'all 0.2s ease-in-out',\n  },\n}));\n\nconst ScoreChip = styled(Chip)<{ score: number }>(({ theme, score }) => ({\n  backgroundColor: score > 0.8 ? theme.palette.success.main : \n                   score > 0.6 ? theme.palette.warning.main : \n                   theme.palette.error.main,\n  color: 'white',\n  fontWeight: 'bold',\n}));\n\n// Section type icons and colors\nconst sectionConfig = {\n  'Diagnosis': { icon: '🔍', color: '#1976d2', description: 'Diagnostic criteria and methods' },\n  'Treatment': { icon: '💊', color: '#2e7d32', description: 'Treatment protocols and medications' },\n  'Prognosis': { icon: '📈', color: '#ed6c02', description: 'Outcomes and prognosis information' },\n  'Anatomy': { icon: '🫀', color: '#9c27b0', description: 'Anatomical structures and systems' },\n  'Pathophysiology': { icon: '🧬', color: '#d32f2f', description: 'Disease mechanisms and pathways' },\n  'Other': { icon: '📚', color: '#757575', description: 'General medical information' },\n};\n\nconst TopicViewer: React.FC = () => {\n  const [query, setQuery] = useState('');\n  const [results, setResults] = useState<TopicSearchResponse | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());\n  const [expandedTexts, setExpandedTexts] = useState<Set<string>>(new Set());\n\n  // Handle search submission\n  const handleSearch = useCallback(async () => {\n    if (!query.trim()) {\n      setError('Please enter a search query');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setResults(null);\n\n    try {\n      const response = await fetch('/api/v1/search/topic', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n        },\n        body: JSON.stringify({\n          query: query.trim(),\n          limit: 20,\n          score_threshold: 0.6,\n        }),\n      });\n\n      if (!response.ok) {\n        if (response.status === 403) {\n          throw new Error('Authentication required. Please log in to search.');\n        }\n        throw new Error(`Search failed: ${response.statusText}`);\n      }\n\n      const data: TopicSearchResponse = await response.json();\n      setResults(data);\n\n      // Auto-expand sections with results\n      const sectionsWithResults = Object.keys(data.grouped_results);\n      setExpandedSections(new Set(sectionsWithResults.slice(0, 2))); // Expand first 2 sections\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Search failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  }, [query]);\n\n  // Handle section accordion toggle\n  const handleSectionToggle = useCallback((section: string) => {\n    setExpandedSections(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(section)) {\n        newSet.delete(section);\n      } else {\n        newSet.add(section);\n      }\n      return newSet;\n    });\n  }, []);\n\n  // Handle text expansion toggle\n  const handleTextToggle = useCallback((resultId: string) => {\n    setExpandedTexts(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(resultId)) {\n        newSet.delete(resultId);\n      } else {\n        newSet.add(resultId);\n      }\n      return newSet;\n    });\n  }, []);\n\n  // Truncate text for preview\n  const truncateText = (text: string, maxLength: number = 200): string => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = (event: React.KeyboardEvent) => {\n    if (event.key === 'Enter') {\n      handleSearch();\n    }\n  };\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ py: 4 }}>\n        {/* Header */}\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom align=\"center\" sx={{ mb: 4 }}>\n          <MedicalIcon sx={{ mr: 2, verticalAlign: 'middle' }} />\n          Medical Topic Search\n        </Typography>\n\n        {/* Search Interface */}\n        <SearchContainer elevation={3}>\n          <Typography variant=\"h6\" gutterBottom>\n            Search Medical Literature\n          </Typography>\n          <Typography variant=\"body2\" sx={{ mb: 3, opacity: 0.9 }}>\n            Search through medical textbooks using AI-powered semantic search. \n            Results are grouped by medical sections for easy navigation.\n          </Typography>\n          \n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <TextField\n              fullWidth\n              variant=\"outlined\"\n              placeholder=\"e.g., diabetes mellitus diagnosis, kawasaki disease treatment...\"\n              value={query}\n              onChange={(e) => setQuery(e.target.value)}\n              onKeyPress={handleKeyPress}\n              disabled={loading}\n              sx={{\n                '& .MuiOutlinedInput-root': {\n                  backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                  '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },\n                  '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.5)' },\n                  '&.Mui-focused fieldset': { borderColor: 'white' },\n                },\n                '& .MuiInputBase-input': { color: 'white' },\n                '& .MuiInputBase-input::placeholder': { color: 'rgba(255, 255, 255, 0.7)' },\n              }}\n            />\n            <Button\n              variant=\"contained\"\n              onClick={handleSearch}\n              disabled={loading || !query.trim()}\n              startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <SearchIcon />}\n              sx={{\n                minWidth: 120,\n                backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.3)' },\n              }}\n            >\n              {loading ? 'Searching...' : 'Search'}\n            </Button>\n          </Stack>\n        </SearchContainer>\n\n        {/* Error Display */}\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 3 }}>\n            {error}\n          </Alert>\n        )}\n\n        {/* Results Display */}\n        {results && (\n          <ResultsContainer>\n            {/* Results Summary */}\n            <Paper sx={{ p: 3, mb: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Search Results for \"{results.query}\"\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\" gutterBottom>\n                {results.message}\n              </Typography>\n              \n              {results.suggestions.length > 0 && (\n                <Box sx={{ mt: 2 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                    Suggestions:\n                  </Typography>\n                  <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\">\n                    {results.suggestions.map((suggestion, index) => (\n                      <Chip\n                        key={index}\n                        label={suggestion}\n                        size=\"small\"\n                        variant=\"outlined\"\n                        onClick={() => setQuery(suggestion)}\n                        sx={{ mb: 1 }}\n                      />\n                    ))}\n                  </Stack>\n                </Box>\n              )}\n            </Paper>\n\n            {/* Grouped Results by Section */}\n            {Object.entries(results.grouped_results).map(([sectionType, sectionResults]) => {\n              const config = sectionConfig[sectionType as keyof typeof sectionConfig] || sectionConfig.Other;\n              const isExpanded = expandedSections.has(sectionType);\n\n              return (\n                <SectionAccordion\n                  key={sectionType}\n                  expanded={isExpanded}\n                  onChange={() => handleSectionToggle(sectionType)}\n                >\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={2} sx={{ width: '100%' }}>\n                      <Typography variant=\"h6\" sx={{ display: 'flex', alignItems: 'center' }}>\n                        <span style={{ marginRight: 8, fontSize: '1.2em' }}>{config.icon}</span>\n                        {sectionType}\n                      </Typography>\n                      <Badge badgeContent={sectionResults.length} color=\"primary\" />\n                      <Tooltip title={config.description}>\n                        <InfoIcon sx={{ color: 'text.secondary', fontSize: 16 }} />\n                      </Tooltip>\n                    </Stack>\n                  </AccordionSummary>\n                  \n                  <AccordionDetails>\n                    {sectionResults.map((result, index) => {\n                      const resultId = `${sectionType}-${index}`;\n                      const isTextExpanded = expandedTexts.has(resultId);\n                      const displayText = isTextExpanded ? result.text : truncateText(result.text);\n\n                      return (\n                        <CitationCard key={index} elevation={1}>\n                          <CardContent>\n                            {/* Result Header */}\n                            <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"flex-start\" sx={{ mb: 2 }}>\n                              <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n                                <BookIcon sx={{ color: config.color }} />\n                                <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                                  {result.metadata.book_title}\n                                </Typography>\n                                <ScoreChip\n                                  score={result.score}\n                                  label={`${(result.score * 100).toFixed(1)}%`}\n                                  size=\"small\"\n                                />\n                              </Stack>\n                              \n                              <Tooltip title={isTextExpanded ? \"Show less\" : \"Show full text\"}>\n                                <IconButton\n                                  size=\"small\"\n                                  onClick={() => handleTextToggle(resultId)}\n                                >\n                                  {isTextExpanded ? <HideIcon /> : <ViewIcon />}\n                                </IconButton>\n                              </Tooltip>\n                            </Stack>\n\n                            {/* Citation Metadata */}\n                            <Stack direction=\"row\" spacing={2} sx={{ mb: 2 }}>\n                              <Chip label={`Chapter: ${result.metadata.chapter}`} size=\"small\" variant=\"outlined\" />\n                              <Chip label={`Page: ${result.metadata.page_number}`} size=\"small\" variant=\"outlined\" />\n                              <Chip label={`Author: ${result.metadata.author}`} size=\"small\" variant=\"outlined\" />\n                            </Stack>\n\n                            <Divider sx={{ my: 2 }} />\n\n                            {/* Result Text */}\n                            <Typography variant=\"body1\" sx={{ lineHeight: 1.6 }}>\n                              {displayText}\n                            </Typography>\n\n                            {/* Text Stats */}\n                            <Stack direction=\"row\" spacing={2} sx={{ mt: 2 }}>\n                              <Typography variant=\"caption\" color=\"text.secondary\">\n                                {result.metadata.word_count} words\n                              </Typography>\n                              <Typography variant=\"caption\" color=\"text.secondary\">\n                                {result.metadata.char_count} characters\n                              </Typography>\n                            </Stack>\n                          </CardContent>\n                        </CitationCard>\n                      );\n                    })}\n                  </AccordionDetails>\n                </SectionAccordion>\n              );\n            })}\n\n            {/* No Results Message */}\n            {Object.keys(results.grouped_results).length === 0 && (\n              <Paper sx={{ p: 4, textAlign: 'center' }}>\n                <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                  No results found\n                </Typography>\n                <Typography variant=\"body1\" color=\"text.secondary\">\n                  Try using different keywords or broader medical terms.\n                </Typography>\n              </Paper>\n            )}\n          </ResultsContainer>\n        )}\n      </Box>\n    </Container>\n  );\n};\n\nexport default TopicViewer;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,SAAS,QACJ,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,aAAa,IAAIC,WAAW,EAC5BC,UAAU,IAAIC,QAAQ,EACtBC,aAAa,IAAIC,QAAQ,EACzBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,sBAAsB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAuBA;AACA,MAAMC,eAAe,GAAGH,MAAM,CAAC7B,KAAK,CAAC,CAAC,CAAC;EAAEiC;AAAM,CAAC,MAAM;EACpDC,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;EACzBC,YAAY,EAAEH,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;EAC9BE,UAAU,EAAE,mDAAmD;EAC/DC,KAAK,EAAE;AACT,CAAC,CAAC,CAAC;AAACC,EAAA,GALEP,eAAe;AAOrB,MAAMQ,gBAAgB,GAAGX,MAAM,CAACjC,GAAG,CAAC,CAAC,CAAC;EAAEqC;AAAM,CAAC,MAAM;EACnDQ,SAAS,EAAER,KAAK,CAACE,OAAO,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC;AAACO,GAAA,GAFEF,gBAAgB;AAItB,MAAMG,gBAAgB,GAAGd,MAAM,CAAC5B,SAAS,CAAC,CAAC,CAAC;EAAEgC;AAAM,CAAC,MAAM;EACzDG,YAAY,EAAEH,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;EAC9B,UAAU,EAAE;IACVS,OAAO,EAAE;EACX,CAAC;EACDC,SAAS,EAAEZ,KAAK,CAACa,OAAO,CAAC,CAAC;AAC5B,CAAC,CAAC,CAAC;AAACC,GAAA,GANEJ,gBAAgB;AAQtB,MAAMK,YAAY,GAAGnB,MAAM,CAACtB,IAAI,CAAC,CAAC,CAAC;EAAE0B;AAAM,CAAC,MAAM;EAChDG,YAAY,EAAEH,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;EAC9Bc,MAAM,EAAE,aAAahB,KAAK,CAACiB,OAAO,CAACC,OAAO,EAAE;EAC5C,SAAS,EAAE;IACTN,SAAS,EAAEZ,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;IAC3BM,SAAS,EAAE,kBAAkB;IAC7BC,UAAU,EAAE;EACd;AACF,CAAC,CAAC,CAAC;AAACC,GAAA,GAREN,YAAY;AAUlB,MAAMO,SAAS,GAAG1B,MAAM,CAACzB,IAAI,CAAC,CAAoB,CAAC;EAAE6B,KAAK;EAAEuB;AAAM,CAAC,MAAM;EACvEC,eAAe,EAAED,KAAK,GAAG,GAAG,GAAGvB,KAAK,CAACiB,OAAO,CAACQ,OAAO,CAACC,IAAI,GACxCH,KAAK,GAAG,GAAG,GAAGvB,KAAK,CAACiB,OAAO,CAACU,OAAO,CAACD,IAAI,GACxC1B,KAAK,CAACiB,OAAO,CAACW,KAAK,CAACF,IAAI;EACzCrB,KAAK,EAAE,OAAO;EACdwB,UAAU,EAAE;AACd,CAAC,CAAC,CAAC;;AAEH;AAAAC,GAAA,GARMR,SAAS;AASf,MAAMS,aAAa,GAAG;EACpB,WAAW,EAAE;IAAEC,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAAkC,CAAC;EAC7F,WAAW,EAAE;IAAED,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAAsC,CAAC;EACjG,WAAW,EAAE;IAAED,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAAqC,CAAC;EAChG,SAAS,EAAE;IAAED,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAAoC,CAAC;EAC7F,iBAAiB,EAAE;IAAED,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAAkC,CAAC;EACnG,OAAO,EAAE;IAAED,IAAI,EAAE,IAAI;IAAE3B,KAAK,EAAE,SAAS;IAAE4B,WAAW,EAAE;EAA8B;AACtF,CAAC;AAED,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6E,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAA6B,IAAI,CAAC;EACxE,MAAM,CAAC+E,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,KAAK,EAAEc,QAAQ,CAAC,GAAGjF,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAc,IAAIoF,GAAG,CAAC,CAAC,CAAC;EAChF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtF,QAAQ,CAAc,IAAIoF,GAAG,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMG,YAAY,GAAGtF,WAAW,CAAC,YAAY;IAC3C,IAAI,CAAC0E,KAAK,CAACa,IAAI,CAAC,CAAC,EAAE;MACjBP,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEAD,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IACdH,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsB,EAAE;QACnDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBtB,KAAK,EAAEA,KAAK,CAACa,IAAI,CAAC,CAAC;UACnBU,KAAK,EAAE,EAAE;UACTC,eAAe,EAAE;QACnB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACV,QAAQ,CAACW,EAAE,EAAE;QAChB,IAAIX,QAAQ,CAACY,MAAM,KAAK,GAAG,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAC,mDAAmD,CAAC;QACtE;QACA,MAAM,IAAIA,KAAK,CAAC,kBAAkBb,QAAQ,CAACc,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMC,IAAyB,GAAG,MAAMf,QAAQ,CAACgB,IAAI,CAAC,CAAC;MACvD3B,UAAU,CAAC0B,IAAI,CAAC;;MAEhB;MACA,MAAME,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAACK,eAAe,CAAC;MAC7D1B,mBAAmB,CAAC,IAAIC,GAAG,CAACsB,mBAAmB,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjE,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ9B,QAAQ,CAAC8B,GAAG,YAAYT,KAAK,GAAGS,GAAG,CAACC,OAAO,GAAG,kCAAkC,CAAC;IACnF,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMsC,mBAAmB,GAAGhH,WAAW,CAAEiH,OAAe,IAAK;IAC3D/B,mBAAmB,CAACgC,IAAI,IAAI;MAC1B,MAAMC,MAAM,GAAG,IAAIhC,GAAG,CAAC+B,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACH,OAAO,CAAC,EAAE;QACvBE,MAAM,CAACE,MAAM,CAACJ,OAAO,CAAC;MACxB,CAAC,MAAM;QACLE,MAAM,CAACG,GAAG,CAACL,OAAO,CAAC;MACrB;MACA,OAAOE,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,gBAAgB,GAAGvH,WAAW,CAAEwH,QAAgB,IAAK;IACzDnC,gBAAgB,CAAC6B,IAAI,IAAI;MACvB,MAAMC,MAAM,GAAG,IAAIhC,GAAG,CAAC+B,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACI,QAAQ,CAAC,EAAE;QACxBL,MAAM,CAACE,MAAM,CAACG,QAAQ,CAAC;MACzB,CAAC,MAAM;QACLL,MAAM,CAACG,GAAG,CAACE,QAAQ,CAAC;MACtB;MACA,OAAOL,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,YAAY,GAAGA,CAACC,IAAY,EAAEC,SAAiB,GAAG,GAAG,KAAa;IACtE,IAAID,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;;EAED;EACA,MAAMG,cAAc,GAAIC,KAA0B,IAAK;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzB1C,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,oBACElD,OAAA,CAACjB,SAAS;IAAC8G,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtB9F,OAAA,CAACnC,GAAG;MAACkI,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBAEjB9F,OAAA,CAAChC,UAAU;QAACiI,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,KAAK,EAAC,QAAQ;QAACL,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBAChF9F,OAAA,CAACT,WAAW;UAACwG,EAAE,EAAE;YAAEO,EAAE,EAAE,CAAC;YAAEC,aAAa,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wBAEzD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb3G,OAAA,CAACC,eAAe;QAAC2G,SAAS,EAAE,CAAE;QAAAd,QAAA,gBAC5B9F,OAAA,CAAChC,UAAU;UAACiI,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAL,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3G,OAAA,CAAChC,UAAU;UAACiI,OAAO,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEQ,OAAO,EAAE;UAAI,CAAE;UAAAf,QAAA,EAAC;QAGzD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3G,OAAA,CAAClB,KAAK;UAACgI,SAAS,EAAC,KAAK;UAAC1G,OAAO,EAAE,CAAE;UAAC2G,UAAU,EAAC,QAAQ;UAAAjB,QAAA,gBACpD9F,OAAA,CAAClC,SAAS;YACRkJ,SAAS;YACTf,OAAO,EAAC,UAAU;YAClBgB,WAAW,EAAC,kEAAkE;YAC9EC,KAAK,EAAE5E,KAAM;YACb6E,QAAQ,EAAGC,CAAC,IAAK7E,QAAQ,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CI,UAAU,EAAE5B,cAAe;YAC3B6B,QAAQ,EAAE7E,OAAQ;YAClBqD,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1BrE,eAAe,EAAE,0BAA0B;gBAC3C,YAAY,EAAE;kBAAE8F,WAAW,EAAE;gBAA2B,CAAC;gBACzD,kBAAkB,EAAE;kBAAEA,WAAW,EAAE;gBAA2B,CAAC;gBAC/D,wBAAwB,EAAE;kBAAEA,WAAW,EAAE;gBAAQ;cACnD,CAAC;cACD,uBAAuB,EAAE;gBAAEjH,KAAK,EAAE;cAAQ,CAAC;cAC3C,oCAAoC,EAAE;gBAAEA,KAAK,EAAE;cAA2B;YAC5E;UAAE;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF3G,OAAA,CAACjC,MAAM;YACLkI,OAAO,EAAC,WAAW;YACnBwB,OAAO,EAAEvE,YAAa;YACtBqE,QAAQ,EAAE7E,OAAO,IAAI,CAACJ,KAAK,CAACa,IAAI,CAAC,CAAE;YACnCuE,SAAS,EAAEhF,OAAO,gBAAG1C,OAAA,CAACzB,gBAAgB;cAACoJ,IAAI,EAAE,EAAG;cAACpH,KAAK,EAAC;YAAS;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3G,OAAA,CAACb,UAAU;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrFZ,EAAE,EAAE;cACF6B,QAAQ,EAAE,GAAG;cACblG,eAAe,EAAE,0BAA0B;cAC3C,SAAS,EAAE;gBAAEA,eAAe,EAAE;cAA2B;YAC3D,CAAE;YAAAoE,QAAA,EAEDpD,OAAO,GAAG,cAAc,GAAG;UAAQ;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,EAGjB7E,KAAK,iBACJ9B,OAAA,CAAC1B,KAAK;QAACuJ,QAAQ,EAAC,OAAO;QAAC9B,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,EACnChE;MAAK;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAnE,OAAO,iBACNxC,OAAA,CAACS,gBAAgB;QAAAqF,QAAA,gBAEf9F,OAAA,CAAC/B,KAAK;UAAC8H,EAAE,EAAE;YAAE+B,CAAC,EAAE,CAAC;YAAEzB,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACzB9F,OAAA,CAAChC,UAAU;YAACiI,OAAO,EAAC,IAAI;YAACE,YAAY;YAAAL,QAAA,GAAC,uBAChB,EAACtD,OAAO,CAACF,KAAK,EAAC,IACrC;UAAA;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3G,OAAA,CAAChC,UAAU;YAACiI,OAAO,EAAC,OAAO;YAAC1F,KAAK,EAAC,gBAAgB;YAAC4F,YAAY;YAAAL,QAAA,EAC5DtD,OAAO,CAACmC;UAAO;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEZnE,OAAO,CAACuF,WAAW,CAACvC,MAAM,GAAG,CAAC,iBAC7BxF,OAAA,CAACnC,GAAG;YAACkI,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAAlC,QAAA,gBACjB9F,OAAA,CAAChC,UAAU;cAACiI,OAAO,EAAC,OAAO;cAAC1F,KAAK,EAAC,gBAAgB;cAAC4F,YAAY;cAAAL,QAAA,EAAC;YAEhE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3G,OAAA,CAAClB,KAAK;cAACgI,SAAS,EAAC,KAAK;cAAC1G,OAAO,EAAE,CAAE;cAAC6H,QAAQ,EAAC,MAAM;cAAAnC,QAAA,EAC/CtD,OAAO,CAACuF,WAAW,CAACG,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,kBACzCpI,OAAA,CAAC3B,IAAI;gBAEHgK,KAAK,EAAEF,UAAW;gBAClBR,IAAI,EAAC,OAAO;gBACZ1B,OAAO,EAAC,UAAU;gBAClBwB,OAAO,EAAEA,CAAA,KAAMlF,QAAQ,CAAC4F,UAAU,CAAE;gBACpCpC,EAAE,EAAE;kBAAEM,EAAE,EAAE;gBAAE;cAAE,GALT+B,KAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGPrC,MAAM,CAACgE,OAAO,CAAC9F,OAAO,CAACgC,eAAe,CAAC,CAAC0D,GAAG,CAAC,CAAC,CAACK,WAAW,EAAEC,cAAc,CAAC,KAAK;UAC9E,MAAMC,MAAM,GAAGxG,aAAa,CAACsG,WAAW,CAA+B,IAAItG,aAAa,CAACyG,KAAK;UAC9F,MAAMC,UAAU,GAAG9F,gBAAgB,CAACmC,GAAG,CAACuD,WAAW,CAAC;UAEpD,oBACEvI,OAAA,CAACY,gBAAgB;YAEfgI,QAAQ,EAAED,UAAW;YACrBxB,QAAQ,EAAEA,CAAA,KAAMvC,mBAAmB,CAAC2D,WAAW,CAAE;YAAAzC,QAAA,gBAEjD9F,OAAA,CAAC7B,gBAAgB;cAAC0K,UAAU,eAAE7I,OAAA,CAACf,cAAc;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAb,QAAA,eAC/C9F,OAAA,CAAClB,KAAK;gBAACgI,SAAS,EAAC,KAAK;gBAACC,UAAU,EAAC,QAAQ;gBAAC3G,OAAO,EAAE,CAAE;gBAAC2F,EAAE,EAAE;kBAAE+C,KAAK,EAAE;gBAAO,CAAE;gBAAAhD,QAAA,gBAC3E9F,OAAA,CAAChC,UAAU;kBAACiI,OAAO,EAAC,IAAI;kBAACF,EAAE,EAAE;oBAAElF,OAAO,EAAE,MAAM;oBAAEkG,UAAU,EAAE;kBAAS,CAAE;kBAAAjB,QAAA,gBACrE9F,OAAA;oBAAM+I,KAAK,EAAE;sBAAEC,WAAW,EAAE,CAAC;sBAAEC,QAAQ,EAAE;oBAAQ,CAAE;oBAAAnD,QAAA,EAAE2C,MAAM,CAACvG;kBAAI;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACvE4B,WAAW;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb3G,OAAA,CAACnB,KAAK;kBAACqK,YAAY,EAAEV,cAAc,CAAChD,MAAO;kBAACjF,KAAK,EAAC;gBAAS;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9D3G,OAAA,CAACpB,OAAO;kBAACuK,KAAK,EAAEV,MAAM,CAACtG,WAAY;kBAAA2D,QAAA,eACjC9F,OAAA,CAACH,QAAQ;oBAACkG,EAAE,EAAE;sBAAExF,KAAK,EAAE,gBAAgB;sBAAE0I,QAAQ,EAAE;oBAAG;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEnB3G,OAAA,CAAC5B,gBAAgB;cAAA0H,QAAA,EACd0C,cAAc,CAACN,GAAG,CAAC,CAACkB,MAAM,EAAEhB,KAAK,KAAK;gBACrC,MAAMhD,QAAQ,GAAG,GAAGmD,WAAW,IAAIH,KAAK,EAAE;gBAC1C,MAAMiB,cAAc,GAAGrG,aAAa,CAACgC,GAAG,CAACI,QAAQ,CAAC;gBAClD,MAAMkE,WAAW,GAAGD,cAAc,GAAGD,MAAM,CAAC9D,IAAI,GAAGD,YAAY,CAAC+D,MAAM,CAAC9D,IAAI,CAAC;gBAE5E,oBACEtF,OAAA,CAACiB,YAAY;kBAAa2F,SAAS,EAAE,CAAE;kBAAAd,QAAA,eACrC9F,OAAA,CAACvB,WAAW;oBAAAqH,QAAA,gBAEV9F,OAAA,CAAClB,KAAK;sBAACgI,SAAS,EAAC,KAAK;sBAACyC,cAAc,EAAC,eAAe;sBAACxC,UAAU,EAAC,YAAY;sBAAChB,EAAE,EAAE;wBAAEM,EAAE,EAAE;sBAAE,CAAE;sBAAAP,QAAA,gBAC1F9F,OAAA,CAAClB,KAAK;wBAACgI,SAAS,EAAC,KAAK;wBAAC1G,OAAO,EAAE,CAAE;wBAAC2G,UAAU,EAAC,QAAQ;wBAAAjB,QAAA,gBACpD9F,OAAA,CAACX,QAAQ;0BAAC0G,EAAE,EAAE;4BAAExF,KAAK,EAAEkI,MAAM,CAAClI;0BAAM;wBAAE;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACzC3G,OAAA,CAAChC,UAAU;0BAACiI,OAAO,EAAC,WAAW;0BAAClE,UAAU,EAAC,MAAM;0BAAA+D,QAAA,EAC9CsD,MAAM,CAACI,QAAQ,CAACC;wBAAU;0BAAAjD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACb3G,OAAA,CAACwB,SAAS;0BACRC,KAAK,EAAE2H,MAAM,CAAC3H,KAAM;0BACpB4G,KAAK,EAAE,GAAG,CAACe,MAAM,CAAC3H,KAAK,GAAG,GAAG,EAAEiI,OAAO,CAAC,CAAC,CAAC,GAAI;0BAC7C/B,IAAI,EAAC;wBAAO;0BAAAnB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC,eAER3G,OAAA,CAACpB,OAAO;wBAACuK,KAAK,EAAEE,cAAc,GAAG,WAAW,GAAG,gBAAiB;wBAAAvD,QAAA,eAC9D9F,OAAA,CAACrB,UAAU;0BACTgJ,IAAI,EAAC,OAAO;0BACZF,OAAO,EAAEA,CAAA,KAAMtC,gBAAgB,CAACC,QAAQ,CAAE;0BAAAU,QAAA,EAEzCuD,cAAc,gBAAGrJ,OAAA,CAACL,QAAQ;4BAAA6G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAG3G,OAAA,CAACP,QAAQ;4BAAA+G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eAGR3G,OAAA,CAAClB,KAAK;sBAACgI,SAAS,EAAC,KAAK;sBAAC1G,OAAO,EAAE,CAAE;sBAAC2F,EAAE,EAAE;wBAAEM,EAAE,EAAE;sBAAE,CAAE;sBAAAP,QAAA,gBAC/C9F,OAAA,CAAC3B,IAAI;wBAACgK,KAAK,EAAE,YAAYe,MAAM,CAACI,QAAQ,CAACG,OAAO,EAAG;wBAAChC,IAAI,EAAC,OAAO;wBAAC1B,OAAO,EAAC;sBAAU;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtF3G,OAAA,CAAC3B,IAAI;wBAACgK,KAAK,EAAE,SAASe,MAAM,CAACI,QAAQ,CAACI,WAAW,EAAG;wBAACjC,IAAI,EAAC,OAAO;wBAAC1B,OAAO,EAAC;sBAAU;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACvF3G,OAAA,CAAC3B,IAAI;wBAACgK,KAAK,EAAE,WAAWe,MAAM,CAACI,QAAQ,CAACK,MAAM,EAAG;wBAAClC,IAAI,EAAC,OAAO;wBAAC1B,OAAO,EAAC;sBAAU;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eAER3G,OAAA,CAACtB,OAAO;sBAACqH,EAAE,EAAE;wBAAE+D,EAAE,EAAE;sBAAE;oBAAE;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAG1B3G,OAAA,CAAChC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACF,EAAE,EAAE;wBAAEgE,UAAU,EAAE;sBAAI,CAAE;sBAAAjE,QAAA,EACjDwD;oBAAW;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eAGb3G,OAAA,CAAClB,KAAK;sBAACgI,SAAS,EAAC,KAAK;sBAAC1G,OAAO,EAAE,CAAE;sBAAC2F,EAAE,EAAE;wBAAEiC,EAAE,EAAE;sBAAE,CAAE;sBAAAlC,QAAA,gBAC/C9F,OAAA,CAAChC,UAAU;wBAACiI,OAAO,EAAC,SAAS;wBAAC1F,KAAK,EAAC,gBAAgB;wBAAAuF,QAAA,GACjDsD,MAAM,CAACI,QAAQ,CAACQ,UAAU,EAAC,QAC9B;sBAAA;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb3G,OAAA,CAAChC,UAAU;wBAACiI,OAAO,EAAC,SAAS;wBAAC1F,KAAK,EAAC,gBAAgB;wBAAAuF,QAAA,GACjDsD,MAAM,CAACI,QAAQ,CAACS,UAAU,EAAC,aAC9B;sBAAA;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC,GAjDGyB,KAAK;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAkDV,CAAC;cAEnB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA,GA7Ed4B,WAAW;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8EA,CAAC;QAEvB,CAAC,CAAC,EAGDrC,MAAM,CAACC,IAAI,CAAC/B,OAAO,CAACgC,eAAe,CAAC,CAACgB,MAAM,KAAK,CAAC,iBAChDxF,OAAA,CAAC/B,KAAK;UAAC8H,EAAE,EAAE;YAAE+B,CAAC,EAAE,CAAC;YAAEoC,SAAS,EAAE;UAAS,CAAE;UAAApE,QAAA,gBACvC9F,OAAA,CAAChC,UAAU;YAACiI,OAAO,EAAC,IAAI;YAAC1F,KAAK,EAAC,gBAAgB;YAAC4F,YAAY;YAAAL,QAAA,EAAC;UAE7D;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3G,OAAA,CAAChC,UAAU;YAACiI,OAAO,EAAC,OAAO;YAAC1F,KAAK,EAAC,gBAAgB;YAAAuF,QAAA,EAAC;UAEnD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACe,CACnB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACtE,EAAA,CArSID,WAAqB;AAAA+H,GAAA,GAArB/H,WAAqB;AAuS3B,eAAeA,WAAW;AAAC,IAAA5B,EAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAO,GAAA,EAAAS,GAAA,EAAAmI,GAAA;AAAAC,YAAA,CAAA5J,EAAA;AAAA4J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}