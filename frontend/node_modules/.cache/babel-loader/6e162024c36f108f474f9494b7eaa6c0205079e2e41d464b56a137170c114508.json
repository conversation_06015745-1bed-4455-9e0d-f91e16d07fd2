{"ast": null, "code": "/**\n * TypeScript type definitions for the Medical Preparation Platform\n */\n\n// User types\n\n// Search types\nexport let TopicCategory = /*#__PURE__*/function (TopicCategory) {\n  TopicCategory[\"DIAGNOSIS\"] = \"diagnosis\";\n  TopicCategory[\"TREATMENT\"] = \"treatment\";\n  TopicCategory[\"PROGNOSIS\"] = \"prognosis\";\n  TopicCategory[\"PATHOPHYSIOLOGY\"] = \"pathophysiology\";\n  TopicCategory[\"EPIDEMIOLOGY\"] = \"epidemiology\";\n  TopicCategory[\"PREVENTION\"] = \"prevention\";\n  TopicCategory[\"GENERAL\"] = \"general\";\n  return TopicCategory;\n}({});\n\n// Q&A types\n\n// Book types\nexport let ProcessingStatus = /*#__PURE__*/function (ProcessingStatus) {\n  ProcessingStatus[\"PENDING\"] = \"pending\";\n  ProcessingStatus[\"PROCESSING\"] = \"processing\";\n  ProcessingStatus[\"COMPLETED\"] = \"completed\";\n  ProcessingStatus[\"FAILED\"] = \"failed\";\n  return ProcessingStatus;\n}({});\n\n// Admin types\n\n// API types\n\n// UI types\n\n// Form types\n\n// Component props types", "map": {"version": 3, "names": ["TopicCategory", "ProcessingStatus"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/types/index.ts"], "sourcesContent": ["/**\n * TypeScript type definitions for the Medical Preparation Platform\n */\n\n// User types\nexport interface User {\n  id: string;\n  email: string;\n  full_name: string;\n  role: 'admin' | 'user';\n  is_active: boolean;\n  is_verified: boolean;\n  institution?: string;\n  specialization?: string;\n  year_of_study?: number;\n  preferences?: Record<string, any>;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface SignupRequest {\n  email: string;\n  password: string;\n  full_name: string;\n  institution?: string;\n  specialization?: string;\n  year_of_study?: number;\n}\n\nexport interface AuthResponse {\n  access_token: string;\n  token_type: string;\n  expires_in: number;\n  user: User;\n}\n\n// Search types\nexport enum TopicCategory {\n  DIAGNOSIS = 'diagnosis',\n  TREATMENT = 'treatment',\n  PROGNOSIS = 'prognosis',\n  PATHOPHYSIOLOGY = 'pathophysiology',\n  EPIDEMIOLOGY = 'epidemiology',\n  PREVENTION = 'prevention',\n  GENERAL = 'general',\n}\n\nexport interface SearchRequest {\n  query: string;\n  limit?: number;\n  score_threshold?: number;\n  filters?: SearchFilters;\n}\n\nexport interface SearchFilters {\n  book_ids?: string[];\n  topic_categories?: TopicCategory[];\n  medical_topics?: string[];\n  page_range?: { min: number; max: number };\n  word_count_range?: { min: number; max: number };\n}\n\nexport interface SearchResult {\n  chunk_id: string;\n  book_id: string;\n  content: string;\n  score: number;\n  page_number?: number;\n  chapter_title?: string;\n  topic_category?: TopicCategory;\n  medical_topics: string[];\n  word_count: number;\n  char_count: number;\n  book_title: string;\n  book_authors: string[];\n  book_publisher?: string;\n  book_publication_year?: number;\n}\n\nexport interface SearchResponse {\n  query: string;\n  total_results: number;\n  results: SearchResult[];\n  search_time_ms: number;\n  filters_applied?: SearchFilters;\n}\n\n// Q&A types\nexport interface QARequest {\n  question: string;\n  context_limit?: number;\n  include_citations?: boolean;\n  filters?: SearchFilters;\n}\n\nexport interface Citation {\n  chunk_id: string;\n  book_id: string;\n  book_title: string;\n  book_authors: string[];\n  page_number?: number;\n  chapter_title?: string;\n  cited_text: string;\n  context_before?: string;\n  context_after?: string;\n  relevance_score: number;\n  citation_order: number;\n}\n\nexport interface QAResponse {\n  question: string;\n  answer: string;\n  answer_id: string;\n  citations: Citation[];\n  confidence_score?: number;\n  response_time_ms: number;\n  model_used: string;\n  context_chunks_used: number;\n}\n\n// Book types\nexport enum ProcessingStatus {\n  PENDING = 'pending',\n  PROCESSING = 'processing',\n  COMPLETED = 'completed',\n  FAILED = 'failed',\n}\n\nexport interface Book {\n  id: string;\n  title: string;\n  authors: string[];\n  isbn?: string;\n  publisher?: string;\n  publication_year?: number;\n  edition?: string;\n  description?: string;\n  tags: string[];\n  language: string;\n  file_size: number;\n  processing_status: ProcessingStatus;\n  processing_error?: string;\n  total_pages?: number;\n  total_chunks: number;\n  created_at: string;\n  updated_at: string;\n  uploaded_by: string;\n}\n\nexport interface BookUploadRequest {\n  title: string;\n  authors: string[];\n  isbn?: string;\n  publisher?: string;\n  publication_year?: number;\n  edition?: string;\n  description?: string;\n  tags?: string[];\n  language?: string;\n}\n\n// Admin types\nexport interface SystemStats {\n  total_books: number;\n  total_chunks: number;\n  total_pages: number;\n  processing_stats: Record<string, number>;\n  storage_stats: Record<string, any>;\n  recent_activity: Array<{\n    type: string;\n    title: string;\n    status: string;\n    timestamp: string;\n  }>;\n}\n\nexport interface ProcessingStatusInfo {\n  book_id: string;\n  title: string;\n  processing_status: ProcessingStatus;\n  progress_percentage: number;\n  total_pages?: number;\n  total_chunks: number;\n  indexed_chunks: number;\n  processing_error?: string;\n  estimated_completion?: string;\n}\n\n// API types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message?: string;\n  data?: T;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  page_size: number;\n}\n\n// UI types\nexport interface NotificationState {\n  open: boolean;\n  message: string;\n  severity: 'success' | 'error' | 'warning' | 'info';\n}\n\nexport interface LoadingState {\n  isLoading: boolean;\n  message?: string;\n}\n\n// Form types\nexport interface FormErrors {\n  [key: string]: string | undefined;\n}\n\nexport interface SearchHistoryItem {\n  id: string;\n  query: string;\n  results_count: number;\n  search_type: string;\n  created_at: string;\n  response_time_ms?: number;\n}\n\nexport interface QAHistoryItem {\n  id: string;\n  question: string;\n  answer: string;\n  answer_id: string;\n  citations_count: number;\n  confidence_score?: number;\n  created_at: string;\n  response_time_ms?: number;\n}\n\n// Component props types\nexport interface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAdmin?: boolean;\n}\n\nexport interface SearchResultCardProps {\n  result: SearchResult;\n  onViewDetails: (result: SearchResult) => void;\n}\n\nexport interface CitationCardProps {\n  citation: Citation;\n  onViewDetails: (citation: Citation) => void;\n}\n\nexport interface BookCardProps {\n  book: Book;\n  onEdit?: (book: Book) => void;\n  onDelete?: (book: Book) => void;\n  onProcess?: (book: Book) => void;\n  showActions?: boolean;\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;;AAqCA;AACA,WAAYA,aAAa,0BAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAbA,aAAa;EAAA,OAAbA,aAAa;AAAA;;AAkDzB;;AAiCA;AACA,WAAYC,gBAAgB,0BAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAhBA,gBAAgB;EAAA,OAAhBA,gBAAgB;AAAA;;AAwC5B;;AA2BA;;AAeA;;AAYA;;AAyBA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}