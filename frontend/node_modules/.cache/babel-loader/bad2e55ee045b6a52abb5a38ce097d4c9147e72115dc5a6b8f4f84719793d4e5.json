{"ast": null, "code": "/**\n * The benefit of this function is to help developers get CSS var from theme without specifying the whole variable\n * and they does not need to remember the prefix (defined once).\n */\nexport default function createGetCssVar() {\n  let prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  function appendVar() {\n    for (var _len = arguments.length, vars = new Array(_len), _key = 0; _key < _len; _key++) {\n      vars[_key] = arguments[_key];\n    }\n    if (!vars.length) {\n      return '';\n    }\n    const value = vars[0];\n    if (typeof value === 'string' && !value.match(/(#|\\(|\\)|(-?(\\d*\\.)?\\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\\d*\\.)?\\d+)$|(\\d+ \\d+ \\d+)/)) {\n      return `, var(--${prefix ? `${prefix}-` : ''}${value}${appendVar(...vars.slice(1))})`;\n    }\n    return `, ${value}`;\n  }\n\n  // AdditionalVars makes `getCssVar` less strict, so it can be use like this `getCssVar('non-mui-variable')` without type error.\n  const getCssVar = function (field) {\n    for (var _len2 = arguments.length, fallbacks = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      fallbacks[_key2 - 1] = arguments[_key2];\n    }\n    return `var(--${prefix ? `${prefix}-` : ''}${field}${appendVar(...fallbacks)})`;\n  };\n  return getCssVar;\n}", "map": {"version": 3, "names": ["createGetCssVar", "prefix", "arguments", "length", "undefined", "appendVar", "_len", "vars", "Array", "_key", "value", "match", "slice", "getCssVar", "field", "_len2", "fallbacks", "_key2"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/cssVars/createGetCssVar.js"], "sourcesContent": ["/**\n * The benefit of this function is to help developers get CSS var from theme without specifying the whole variable\n * and they does not need to remember the prefix (defined once).\n */\nexport default function createGetCssVar(prefix = '') {\n  function appendVar(...vars) {\n    if (!vars.length) {\n      return '';\n    }\n    const value = vars[0];\n    if (typeof value === 'string' && !value.match(/(#|\\(|\\)|(-?(\\d*\\.)?\\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\\d*\\.)?\\d+)$|(\\d+ \\d+ \\d+)/)) {\n      return `, var(--${prefix ? `${prefix}-` : ''}${value}${appendVar(...vars.slice(1))})`;\n    }\n    return `, ${value}`;\n  }\n\n  // AdditionalVars makes `getCssVar` less strict, so it can be use like this `getCssVar('non-mui-variable')` without type error.\n  const getCssVar = (field, ...fallbacks) => {\n    return `var(--${prefix ? `${prefix}-` : ''}${field}${appendVar(...fallbacks)})`;\n  };\n  return getCssVar;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,eAAeA,CAAA,EAAc;EAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACjD,SAASG,SAASA,CAAA,EAAU;IAAA,SAAAC,IAAA,GAAAJ,SAAA,CAAAC,MAAA,EAANI,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAP,SAAA,CAAAO,IAAA;IAAA;IACxB,IAAI,CAACF,IAAI,CAACJ,MAAM,EAAE;MAChB,OAAO,EAAE;IACX;IACA,MAAMO,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC;IACrB,IAAI,OAAOG,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,6GAA6G,CAAC,EAAE;MAC5J,OAAO,WAAWV,MAAM,GAAG,GAAGA,MAAM,GAAG,GAAG,EAAE,GAAGS,KAAK,GAAGL,SAAS,CAAC,GAAGE,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;IACvF;IACA,OAAO,KAAKF,KAAK,EAAE;EACrB;;EAEA;EACA,MAAMG,SAAS,GAAG,SAAAA,CAACC,KAAK,EAAmB;IAAA,SAAAC,KAAA,GAAAb,SAAA,CAAAC,MAAA,EAAda,SAAS,OAAAR,KAAA,CAAAO,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;MAATD,SAAS,CAAAC,KAAA,QAAAf,SAAA,CAAAe,KAAA;IAAA;IACpC,OAAO,SAAShB,MAAM,GAAG,GAAGA,MAAM,GAAG,GAAG,EAAE,GAAGa,KAAK,GAAGT,SAAS,CAAC,GAAGW,SAAS,CAAC,GAAG;EACjF,CAAC;EACD,OAAOH,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}