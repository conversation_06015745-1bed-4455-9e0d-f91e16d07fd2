{"ast": null, "code": "export { default } from \"./appendOwnerState.js\";", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/utils/esm/appendOwnerState/index.js"], "sourcesContent": ["export { default } from \"./appendOwnerState.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}