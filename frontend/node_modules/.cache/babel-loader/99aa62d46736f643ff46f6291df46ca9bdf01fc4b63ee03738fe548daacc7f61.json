{"ast": null, "code": "import { apiClient } from './apiClient';\nclass AdminService {\n  // System Stats and Health\n  async getSystemStats() {\n    try {\n      var _response$recent_acti;\n      console.log('🔍 Fetching admin stats...');\n      const response = await apiClient.get('/admin/stats');\n      console.log('✅ Admin stats API success at', new Date().toISOString(), ':', response);\n\n      // Transform backend response to frontend format\n      const stats = {\n        totalBooks: response.total_books || 0,\n        totalUsers: response.total_users || 0,\n        totalSearches: response.total_searches || 0,\n        totalQuestions: response.total_questions || 0,\n        storageUsed: response.storage_used || 0,\n        storageLimit: response.storage_limit || 1000,\n        // Default limit in MB\n        activeProcessing: response.active_processing || 0,\n        systemHealth: response.system_health || 'healthy',\n        recentActivity: ((_response$recent_acti = response.recent_activity) === null || _response$recent_acti === void 0 ? void 0 : _response$recent_acti.map(activity => ({\n          id: Math.random().toString(),\n          type: activity.type === 'book_upload' ? 'upload' : 'upload',\n          description: `${activity.title} - ${activity.status}`,\n          timestamp: activity.timestamp,\n          user: 'Admin'\n        }))) || [],\n        popularBooks: [],\n        // Not available in backend response\n        userGrowth: 0,\n        searchGrowth: 0\n      };\n      console.log('📊 Transformed stats:', stats);\n      return stats;\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('❌ Admin stats API failed at', new Date().toISOString());\n      console.error('Error details:', error);\n      console.error('Error response:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      console.error('Error status:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n      throw new Error(`Failed to fetch admin stats: ${error.message}`);\n    }\n  }\n  async getSystemHealth() {\n    try {\n      var _backendData$services, _backendData$services2, _backendData$services3, _backendData$services4, _backendData$services5, _backendData$services6, _backendData$services7, _backendData$services8;\n      const backendData = await apiClient.get('/admin/health');\n      console.log('✅ Admin health API success:', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        status: backendData.status || 'healthy',\n        database: {\n          status: ((_backendData$services = backendData.services) === null || _backendData$services === void 0 ? void 0 : (_backendData$services2 = _backendData$services.database) === null || _backendData$services2 === void 0 ? void 0 : _backendData$services2.status) === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 45 // Default value\n        },\n        vector_db: {\n          status: ((_backendData$services3 = backendData.services) === null || _backendData$services3 === void 0 ? void 0 : (_backendData$services4 = _backendData$services3.indexing) === null || _backendData$services4 === void 0 ? void 0 : _backendData$services4.status) === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 0\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          // Default values\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15\n        },\n        services: {\n          embedding_service: ((_backendData$services5 = backendData.services) === null || _backendData$services5 === void 0 ? void 0 : (_backendData$services6 = _backendData$services5.indexing) === null || _backendData$services6 === void 0 ? void 0 : _backendData$services6.status) === 'healthy',\n          llm_service: ((_backendData$services7 = backendData.services) === null || _backendData$services7 === void 0 ? void 0 : (_backendData$services8 = _backendData$services7.indexing) === null || _backendData$services8 === void 0 ? void 0 : _backendData$services8.status) === 'healthy',\n          pdf_service: true // Assume PDF service is working\n        }\n      };\n    } catch (error) {\n      console.error('❌ Admin health API failed:', error);\n      // Return realistic health data based on what we know is working\n      return {\n        status: 'warning',\n        // Since vector DB is not running\n        database: {\n          status: 'connected',\n          // We know DB is working from other APIs\n          response_time: 45\n        },\n        vector_db: {\n          status: 'disconnected',\n          // Qdrant is not running\n          response_time: 0\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15\n        },\n        services: {\n          embedding_service: false,\n          // Requires vector DB\n          llm_service: false,\n          // Requires API keys\n          pdf_service: true // Basic PDF processing works\n        }\n      };\n    }\n  }\n\n  // Analytics\n  async getAnalytics(period = 'week') {\n    try {\n      var _searchAnalytics$most;\n      // Get system stats for basic counts\n      const systemStats = await this.getSystemStats();\n\n      // Get search analytics\n      const searchAnalytics = await apiClient.get('/search/analytics');\n\n      // Get books for popular books (using search count from books)\n      const booksData = await this.getBooks(1, 10);\n      return {\n        totalSearches: searchAnalytics.total_searches || 0,\n        totalQuestions: 0,\n        // Will need to add QA analytics endpoint\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        // Will need historical data for trends\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: booksData.books.slice(0, 5).map(book => ({\n          title: book.title,\n          authors: book.authors,\n          searchCount: book.searchCount || 0\n        })),\n        popularQueries: ((_searchAnalytics$most = searchAnalytics.most_common_queries) === null || _searchAnalytics$most === void 0 ? void 0 : _searchAnalytics$most.slice(0, 5)) || []\n      };\n    } catch (error) {\n      console.error('❌ Analytics API failed:', error);\n      // Return minimal real data on error\n      const systemStats = await this.getSystemStats();\n      return {\n        totalSearches: 0,\n        totalQuestions: 0,\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: [],\n        popularQueries: []\n      };\n    }\n  }\n\n  // Book Management\n  async getBooks(page = 1, limit = 10, search) {\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: limit.toString(),\n        ...(search && {\n          search\n        })\n      });\n      const backendData = await apiClient.get(`/admin/books?${params}`);\n\n      // Transform backend response to frontend format\n      console.log('✅ Admin books API success at', new Date().toISOString(), ':', backendData);\n      return {\n        books: backendData.books.map(book => ({\n          id: book.id,\n          title: book.title,\n          authors: Array.isArray(book.authors) ? book.authors.join(', ') : book.authors,\n          searchCount: 0,\n          // Not available in backend response\n          uploadDate: book.created_at,\n          status: book.processing_status,\n          fileSize: book.file_size,\n          language: book.language,\n          tags: book.tags || []\n        })),\n        total: backendData.total,\n        page: backendData.page,\n        totalPages: Math.ceil(backendData.total / backendData.page_size)\n      };\n    } catch (error) {\n      console.error('❌ Admin books API failed:', error);\n      // Return mock data for development\n      return {\n        books: [{\n          id: '1',\n          title: 'Gray\\'s Anatomy',\n          authors: 'Henry Gray',\n          searchCount: 245,\n          uploadDate: '2024-01-15',\n          status: 'completed',\n          fileSize: 50 * 1024 * 1024,\n          language: 'en',\n          tags: ['anatomy', 'medical', 'textbook']\n        }, {\n          id: '2',\n          title: 'Harrison\\'s Principles of Internal Medicine',\n          authors: 'Dennis Kasper',\n          searchCount: 198,\n          uploadDate: '2024-01-20',\n          status: 'processing',\n          fileSize: 75 * 1024 * 1024,\n          language: 'en',\n          tags: ['internal medicine', 'diagnosis']\n        }],\n        total: 156,\n        page: 1,\n        totalPages: 16\n      };\n    }\n  }\n  async uploadBook(file, data) {\n    const formData = new FormData();\n    formData.append('file', file);\n    Object.entries(data).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        formData.append(key, value.toString());\n      }\n    });\n    const response = await apiClient.post('/admin/books/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n  async deleteBook(bookId) {\n    const response = await apiClient.delete(`/admin/books/${bookId}`);\n    return response.data;\n  }\n  async updateBook(bookId, data) {\n    const response = await apiClient.put(`/admin/books/${bookId}`, data);\n    return response.data;\n  }\n  async getProcessingStatus() {\n    try {\n      // Note: This endpoint may not exist yet, using mock data for now\n      // const response = await apiClient.get('/admin/processing/status');\n      // return response.data;\n      throw new Error('Endpoint not implemented yet');\n    } catch (error) {\n      // Return mock data for development\n      return [{\n        book_id: '2',\n        status: 'processing',\n        progress: 65,\n        message: 'Extracting text from PDF...',\n        started_at: new Date().toISOString()\n      }, {\n        book_id: '3',\n        status: 'pending',\n        progress: 0,\n        message: 'Waiting in queue...',\n        started_at: new Date().toISOString()\n      }];\n    }\n  }\n  async reprocessBook(bookId) {\n    const response = await apiClient.post(`/admin/processing/reprocess/${bookId}`);\n    return response.data;\n  }\n  async cancelProcessing(bookId) {\n    const response = await apiClient.post(`/admin/processing/cancel/${bookId}`);\n    return response.data;\n  }\n\n  // User Management\n  async getUsers(page = 1, limit = 10, search) {\n    try {\n      console.log('🔍 Fetching users with params:', {\n        page,\n        limit,\n        search\n      });\n      const skip = (page - 1) * limit;\n      const params = new URLSearchParams({\n        skip: skip.toString(),\n        limit: limit.toString(),\n        ...(search && {\n          search\n        })\n      });\n      console.log('📡 Making request to:', `/users/?${params}`);\n      const response = await apiClient.get(`/users/?${params}`);\n      console.log('✅ Users API response:', response);\n\n      // Handle the response data properly (apiClient.get returns response.data directly)\n      const users = response.users || response; // Handle both paginated and direct array responses\n      const total = response.total || users.length;\n      const result = {\n        users: users.map(user => ({\n          id: user.id,\n          email: user.email,\n          full_name: user.full_name || user.name || 'Unknown',\n          role: user.role.toUpperCase(),\n          created_at: user.created_at,\n          last_login: user.last_login || user.created_at,\n          is_active: user.is_active\n        })),\n        total: total,\n        page: page,\n        totalPages: Math.ceil(total / limit)\n      };\n      console.log('👥 Transformed users:', result);\n      return result;\n    } catch (error) {\n      var _error$response3, _error$response4;\n      console.error('❌ Users API failed:', error);\n      console.error('Error details:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data);\n      console.error('Error status:', (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status);\n      throw new Error(`Failed to fetch users: ${error.message}`);\n    }\n  }\n  async updateUserRole(userId, role) {\n    // Note: Role update endpoint may need to be implemented\n    // For now, using activate/deactivate endpoints\n    throw new Error('Role update endpoint not implemented yet');\n  }\n  async toggleUserStatus(userId) {\n    // Use activate/deactivate endpoints\n    const response = await apiClient.put(`/users/${userId}/activate`);\n    return response.data;\n  }\n}\nexport const adminService = new AdminService();", "map": {"version": 3, "names": ["apiClient", "AdminService", "getSystemStats", "_response$recent_acti", "console", "log", "response", "get", "Date", "toISOString", "stats", "totalBooks", "total_books", "totalUsers", "total_users", "totalSearches", "total_searches", "totalQuestions", "total_questions", "storageUsed", "storage_used", "storageLimit", "storage_limit", "activeProcessing", "active_processing", "systemHealth", "system_health", "recentActivity", "recent_activity", "map", "activity", "id", "Math", "random", "toString", "type", "description", "title", "status", "timestamp", "user", "popularBooks", "userGrowth", "searchGrowth", "error", "_error$response", "_error$response2", "data", "Error", "message", "getSystemHealth", "_backendData$services", "_backendData$services2", "_backendData$services3", "_backendData$services4", "_backendData$services5", "_backendData$services6", "_backendData$services7", "_backendData$services8", "backendData", "database", "services", "response_time", "vector_db", "indexing", "storage", "available_space", "total_space", "usage_percentage", "embedding_service", "llm_service", "pdf_service", "getAnalytics", "period", "_searchAnalytics$most", "systemStats", "searchAnalytics", "booksData", "getBooks", "searchTrend", "questionTrend", "userTrend", "books", "slice", "book", "authors", "searchCount", "popularQueries", "most_common_queries", "page", "limit", "search", "params", "URLSearchParams", "page_size", "Array", "isArray", "join", "uploadDate", "created_at", "processing_status", "fileSize", "file_size", "language", "tags", "total", "totalPages", "ceil", "uploadBook", "file", "formData", "FormData", "append", "Object", "entries", "for<PERSON>ach", "key", "value", "undefined", "post", "headers", "deleteBook", "bookId", "delete", "updateBook", "put", "getProcessingStatus", "book_id", "progress", "started_at", "reprocessBook", "cancelProcessing", "getUsers", "skip", "users", "length", "result", "email", "full_name", "name", "role", "toUpperCase", "last_login", "is_active", "_error$response3", "_error$response4", "updateUserRole", "userId", "toggleUserStatus", "adminService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/adminService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\n\nexport interface SystemStats {\n  totalBooks: number;\n  totalUsers: number;\n  totalSearches: number;\n  totalQuestions: number;\n  storageUsed: number;\n  storageLimit: number;\n  activeProcessing: number;\n  systemHealth: 'healthy' | 'warning' | 'critical';\n  recentActivity: ActivityItem[];\n  popularBooks: BookItem[];\n  userGrowth: number;\n  searchGrowth: number;\n}\n\nexport interface ActivityItem {\n  id: string;\n  type: 'upload' | 'search' | 'question' | 'user_signup';\n  description: string;\n  timestamp: string;\n  user?: string;\n}\n\nexport interface BookItem {\n  id: string;\n  title: string;\n  authors: string;\n  searchCount: number;\n  uploadDate: string;\n  status: 'processing' | 'completed' | 'failed';\n  fileSize: number;\n  language: string;\n  tags: string[];\n}\n\nexport interface User {\n  id: string;\n  email: string;\n  full_name: string;\n  role: 'USER' | 'ADMIN';\n  created_at: string;\n  last_login: string;\n  is_active: boolean;\n}\n\nexport interface BookUploadData {\n  title: string;\n  authors: string;\n  isbn?: string;\n  publisher?: string;\n  publication_year?: number;\n  edition?: string;\n  description?: string;\n  tags?: string;\n  language: string;\n}\n\nexport interface ProcessingStatus {\n  book_id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  message: string;\n  started_at: string;\n  completed_at?: string;\n  error_message?: string;\n}\n\nexport interface SystemHealth {\n  status: 'healthy' | 'warning' | 'critical';\n  database: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  vector_db: {\n    status: 'connected' | 'disconnected';\n    response_time: number;\n  };\n  storage: {\n    available_space: number;\n    total_space: number;\n    usage_percentage: number;\n  };\n  services: {\n    embedding_service: boolean;\n    llm_service: boolean;\n    pdf_service: boolean;\n  };\n}\n\nclass AdminService {\n  // System Stats and Health\n  async getSystemStats(): Promise<SystemStats> {\n    try {\n      console.log('🔍 Fetching admin stats...');\n      const response = await apiClient.get('/admin/stats');\n      console.log('✅ Admin stats API success at', new Date().toISOString(), ':', response);\n\n      // Transform backend response to frontend format\n      const stats = {\n        totalBooks: response.total_books || 0,\n        totalUsers: response.total_users || 0,\n        totalSearches: response.total_searches || 0,\n        totalQuestions: response.total_questions || 0,\n        storageUsed: response.storage_used || 0,\n        storageLimit: response.storage_limit || 1000, // Default limit in MB\n        activeProcessing: response.active_processing || 0,\n        systemHealth: response.system_health || 'healthy',\n        recentActivity: response.recent_activity?.map((activity: any) => ({\n          id: Math.random().toString(),\n          type: activity.type === 'book_upload' ? 'upload' as const : 'upload' as const,\n          description: `${activity.title} - ${activity.status}`,\n          timestamp: activity.timestamp,\n          user: 'Admin',\n        })) || [],\n        popularBooks: [], // Not available in backend response\n        userGrowth: 0,\n        searchGrowth: 0,\n      };\n\n      console.log('📊 Transformed stats:', stats);\n      return stats;\n    } catch (error: any) {\n      console.error('❌ Admin stats API failed at', new Date().toISOString());\n      console.error('Error details:', error);\n      console.error('Error response:', error.response?.data);\n      console.error('Error status:', error.response?.status);\n      throw new Error(`Failed to fetch admin stats: ${error.message}`);\n    }\n  }\n\n  async getSystemHealth(): Promise<SystemHealth> {\n    try {\n      const backendData = await apiClient.get('/admin/health');\n      console.log('✅ Admin health API success:', backendData);\n\n      // Transform backend response to frontend format\n      return {\n        status: backendData.status || 'healthy',\n        database: {\n          status: backendData.services?.database?.status === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 45, // Default value\n        },\n        vector_db: {\n          status: backendData.services?.indexing?.status === 'healthy' ? 'connected' : 'disconnected',\n          response_time: 0,\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024, // Default values\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15,\n        },\n        services: {\n          embedding_service: backendData.services?.indexing?.status === 'healthy',\n          llm_service: backendData.services?.indexing?.status === 'healthy',\n          pdf_service: true, // Assume PDF service is working\n        },\n      };\n    } catch (error) {\n      console.error('❌ Admin health API failed:', error);\n      // Return realistic health data based on what we know is working\n      return {\n        status: 'warning', // Since vector DB is not running\n        database: {\n          status: 'connected', // We know DB is working from other APIs\n          response_time: 45,\n        },\n        vector_db: {\n          status: 'disconnected', // Qdrant is not running\n          response_time: 0,\n        },\n        storage: {\n          available_space: 85 * 1024 * 1024 * 1024,\n          total_space: 100 * 1024 * 1024 * 1024,\n          usage_percentage: 15,\n        },\n        services: {\n          embedding_service: false, // Requires vector DB\n          llm_service: false, // Requires API keys\n          pdf_service: true, // Basic PDF processing works\n        },\n      };\n    }\n  }\n\n  // Analytics\n  async getAnalytics(period: string = 'week'): Promise<{\n    totalSearches: number;\n    totalQuestions: number;\n    totalUsers: number;\n    totalBooks: number;\n    searchTrend: number;\n    questionTrend: number;\n    userTrend: number;\n    popularBooks: Array<{\n      title: string;\n      authors: string;\n      searchCount: number;\n    }>;\n    popularQueries: Array<{\n      query: string;\n      count: number;\n    }>;\n  }> {\n    try {\n      // Get system stats for basic counts\n      const systemStats = await this.getSystemStats();\n\n      // Get search analytics\n      const searchAnalytics = await apiClient.get('/search/analytics');\n\n      // Get books for popular books (using search count from books)\n      const booksData = await this.getBooks(1, 10);\n\n      return {\n        totalSearches: searchAnalytics.total_searches || 0,\n        totalQuestions: 0, // Will need to add QA analytics endpoint\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0, // Will need historical data for trends\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: booksData.books.slice(0, 5).map(book => ({\n          title: book.title,\n          authors: book.authors,\n          searchCount: book.searchCount || 0,\n        })),\n        popularQueries: searchAnalytics.most_common_queries?.slice(0, 5) || [],\n      };\n    } catch (error) {\n      console.error('❌ Analytics API failed:', error);\n      // Return minimal real data on error\n      const systemStats = await this.getSystemStats();\n      return {\n        totalSearches: 0,\n        totalQuestions: 0,\n        totalUsers: systemStats.totalUsers,\n        totalBooks: systemStats.totalBooks,\n        searchTrend: 0,\n        questionTrend: 0,\n        userTrend: 0,\n        popularBooks: [],\n        popularQueries: [],\n      };\n    }\n  }\n\n  // Book Management\n  async getBooks(page: number = 1, limit: number = 10, search?: string): Promise<{\n    books: BookItem[];\n    total: number;\n    page: number;\n    totalPages: number;\n  }> {\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        page_size: limit.toString(),\n        ...(search && { search }),\n      });\n      const backendData = await apiClient.get(`/admin/books?${params}`);\n\n      // Transform backend response to frontend format\n      console.log('✅ Admin books API success at', new Date().toISOString(), ':', backendData);\n      return {\n        books: backendData.books.map((book: any) => ({\n          id: book.id,\n          title: book.title,\n          authors: Array.isArray(book.authors) ? book.authors.join(', ') : book.authors,\n          searchCount: 0, // Not available in backend response\n          uploadDate: book.created_at,\n          status: book.processing_status,\n          fileSize: book.file_size,\n          language: book.language,\n          tags: book.tags || [],\n        })),\n        total: backendData.total,\n        page: backendData.page,\n        totalPages: Math.ceil(backendData.total / backendData.page_size),\n      };\n    } catch (error) {\n      console.error('❌ Admin books API failed:', error);\n      // Return mock data for development\n      return {\n        books: [\n          {\n            id: '1',\n            title: 'Gray\\'s Anatomy',\n            authors: 'Henry Gray',\n            searchCount: 245,\n            uploadDate: '2024-01-15',\n            status: 'completed',\n            fileSize: 50 * 1024 * 1024,\n            language: 'en',\n            tags: ['anatomy', 'medical', 'textbook'],\n          },\n          {\n            id: '2',\n            title: 'Harrison\\'s Principles of Internal Medicine',\n            authors: 'Dennis Kasper',\n            searchCount: 198,\n            uploadDate: '2024-01-20',\n            status: 'processing',\n            fileSize: 75 * 1024 * 1024,\n            language: 'en',\n            tags: ['internal medicine', 'diagnosis'],\n          },\n        ],\n        total: 156,\n        page: 1,\n        totalPages: 16,\n      };\n    }\n  }\n\n  async uploadBook(file: File, data: BookUploadData): Promise<{ book_id: string; message: string }> {\n    const formData = new FormData();\n    formData.append('file', file);\n    Object.entries(data).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        formData.append(key, value.toString());\n      }\n    });\n\n    const response = await apiClient.post('/admin/books/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  }\n\n  async deleteBook(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/admin/books/${bookId}`);\n    return response.data;\n  }\n\n  async updateBook(bookId: string, data: Partial<BookUploadData>): Promise<BookItem> {\n    const response = await apiClient.put(`/admin/books/${bookId}`, data);\n    return response.data;\n  }\n\n  async getProcessingStatus(): Promise<ProcessingStatus[]> {\n    try {\n      // Note: This endpoint may not exist yet, using mock data for now\n      // const response = await apiClient.get('/admin/processing/status');\n      // return response.data;\n      throw new Error('Endpoint not implemented yet');\n    } catch (error) {\n      // Return mock data for development\n      return [\n        {\n          book_id: '2',\n          status: 'processing',\n          progress: 65,\n          message: 'Extracting text from PDF...',\n          started_at: new Date().toISOString(),\n        },\n        {\n          book_id: '3',\n          status: 'pending',\n          progress: 0,\n          message: 'Waiting in queue...',\n          started_at: new Date().toISOString(),\n        },\n      ];\n    }\n  }\n\n  async reprocessBook(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.post(`/admin/processing/reprocess/${bookId}`);\n    return response.data;\n  }\n\n  async cancelProcessing(bookId: string): Promise<{ message: string }> {\n    const response = await apiClient.post(`/admin/processing/cancel/${bookId}`);\n    return response.data;\n  }\n\n  // User Management\n  async getUsers(page: number = 1, limit: number = 10, search?: string): Promise<{\n    users: User[];\n    total: number;\n    page: number;\n    totalPages: number;\n  }> {\n    try {\n      console.log('🔍 Fetching users with params:', { page, limit, search });\n      const skip = (page - 1) * limit;\n      const params = new URLSearchParams({\n        skip: skip.toString(),\n        limit: limit.toString(),\n        ...(search && { search }),\n      });\n\n      console.log('📡 Making request to:', `/users/?${params}`);\n      const response = await apiClient.get(`/users/?${params}`);\n      console.log('✅ Users API response:', response);\n\n      // Handle the response data properly (apiClient.get returns response.data directly)\n      const users = response.users || response; // Handle both paginated and direct array responses\n      const total = response.total || users.length;\n\n      const result = {\n        users: users.map((user: any) => ({\n          id: user.id,\n          email: user.email,\n          full_name: user.full_name || user.name || 'Unknown',\n          role: user.role.toUpperCase(),\n          created_at: user.created_at,\n          last_login: user.last_login || user.created_at,\n          is_active: user.is_active,\n        })),\n        total: total,\n        page: page,\n        totalPages: Math.ceil(total / limit),\n      };\n\n      console.log('👥 Transformed users:', result);\n      return result;\n    } catch (error: any) {\n      console.error('❌ Users API failed:', error);\n      console.error('Error details:', error.response?.data);\n      console.error('Error status:', error.response?.status);\n      throw new Error(`Failed to fetch users: ${error.message}`);\n    }\n  }\n\n  async updateUserRole(userId: string, role: 'USER' | 'ADMIN'): Promise<User> {\n    // Note: Role update endpoint may need to be implemented\n    // For now, using activate/deactivate endpoints\n    throw new Error('Role update endpoint not implemented yet');\n  }\n\n  async toggleUserStatus(userId: string): Promise<User> {\n    // Use activate/deactivate endpoints\n    const response = await apiClient.put(`/users/${userId}/activate`);\n    return response.data;\n  }\n}\n\nexport const adminService = new AdminService();\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AA2FvC,MAAMC,YAAY,CAAC;EACjB;EACA,MAAMC,cAAcA,CAAA,EAAyB;IAC3C,IAAI;MAAA,IAAAC,qBAAA;MACFC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMC,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,cAAc,CAAC;MACpDH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAIG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEH,QAAQ,CAAC;;MAEpF;MACA,MAAMI,KAAK,GAAG;QACZC,UAAU,EAAEL,QAAQ,CAACM,WAAW,IAAI,CAAC;QACrCC,UAAU,EAAEP,QAAQ,CAACQ,WAAW,IAAI,CAAC;QACrCC,aAAa,EAAET,QAAQ,CAACU,cAAc,IAAI,CAAC;QAC3CC,cAAc,EAAEX,QAAQ,CAACY,eAAe,IAAI,CAAC;QAC7CC,WAAW,EAAEb,QAAQ,CAACc,YAAY,IAAI,CAAC;QACvCC,YAAY,EAAEf,QAAQ,CAACgB,aAAa,IAAI,IAAI;QAAE;QAC9CC,gBAAgB,EAAEjB,QAAQ,CAACkB,iBAAiB,IAAI,CAAC;QACjDC,YAAY,EAAEnB,QAAQ,CAACoB,aAAa,IAAI,SAAS;QACjDC,cAAc,EAAE,EAAAxB,qBAAA,GAAAG,QAAQ,CAACsB,eAAe,cAAAzB,qBAAA,uBAAxBA,qBAAA,CAA0B0B,GAAG,CAAEC,QAAa,KAAM;UAChEC,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC5BC,IAAI,EAAEL,QAAQ,CAACK,IAAI,KAAK,aAAa,GAAG,QAAQ,GAAY,QAAiB;UAC7EC,WAAW,EAAE,GAAGN,QAAQ,CAACO,KAAK,MAAMP,QAAQ,CAACQ,MAAM,EAAE;UACrDC,SAAS,EAAET,QAAQ,CAACS,SAAS;UAC7BC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC,KAAI,EAAE;QACTC,YAAY,EAAE,EAAE;QAAE;QAClBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC;MAEDvC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,KAAK,CAAC;MAC3C,OAAOA,KAAK;IACd,CAAC,CAAC,OAAOkC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA;MACnB1C,OAAO,CAACwC,KAAK,CAAC,6BAA6B,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MACtEL,OAAO,CAACwC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCxC,OAAO,CAACwC,KAAK,CAAC,iBAAiB,GAAAC,eAAA,GAAED,KAAK,CAACtC,QAAQ,cAAAuC,eAAA,uBAAdA,eAAA,CAAgBE,IAAI,CAAC;MACtD3C,OAAO,CAACwC,KAAK,CAAC,eAAe,GAAAE,gBAAA,GAAEF,KAAK,CAACtC,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBR,MAAM,CAAC;MACtD,MAAM,IAAIU,KAAK,CAAC,gCAAgCJ,KAAK,CAACK,OAAO,EAAE,CAAC;IAClE;EACF;EAEA,MAAMC,eAAeA,CAAA,EAA0B;IAC7C,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,MAAMC,WAAW,GAAG,MAAM3D,SAAS,CAACO,GAAG,CAAC,eAAe,CAAC;MACxDH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEsD,WAAW,CAAC;;MAEvD;MACA,OAAO;QACLrB,MAAM,EAAEqB,WAAW,CAACrB,MAAM,IAAI,SAAS;QACvCsB,QAAQ,EAAE;UACRtB,MAAM,EAAE,EAAAa,qBAAA,GAAAQ,WAAW,CAACE,QAAQ,cAAAV,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsBS,QAAQ,cAAAR,sBAAA,uBAA9BA,sBAAA,CAAgCd,MAAM,MAAK,SAAS,GAAG,WAAW,GAAG,cAAc;UAC3FwB,aAAa,EAAE,EAAE,CAAE;QACrB,CAAC;QACDC,SAAS,EAAE;UACTzB,MAAM,EAAE,EAAAe,sBAAA,GAAAM,WAAW,CAACE,QAAQ,cAAAR,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBW,QAAQ,cAAAV,sBAAA,uBAA9BA,sBAAA,CAAgChB,MAAM,MAAK,SAAS,GAAG,WAAW,GAAG,cAAc;UAC3FwB,aAAa,EAAE;QACjB,CAAC;QACDG,OAAO,EAAE;UACPC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UAAE;UAC1CC,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACrCC,gBAAgB,EAAE;QACpB,CAAC;QACDP,QAAQ,EAAE;UACRQ,iBAAiB,EAAE,EAAAd,sBAAA,GAAAI,WAAW,CAACE,QAAQ,cAAAN,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBS,QAAQ,cAAAR,sBAAA,uBAA9BA,sBAAA,CAAgClB,MAAM,MAAK,SAAS;UACvEgC,WAAW,EAAE,EAAAb,sBAAA,GAAAE,WAAW,CAACE,QAAQ,cAAAJ,sBAAA,wBAAAC,sBAAA,GAApBD,sBAAA,CAAsBO,QAAQ,cAAAN,sBAAA,uBAA9BA,sBAAA,CAAgCpB,MAAM,MAAK,SAAS;UACjEiC,WAAW,EAAE,IAAI,CAAE;QACrB;MACF,CAAC;IACH,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,OAAO;QACLN,MAAM,EAAE,SAAS;QAAE;QACnBsB,QAAQ,EAAE;UACRtB,MAAM,EAAE,WAAW;UAAE;UACrBwB,aAAa,EAAE;QACjB,CAAC;QACDC,SAAS,EAAE;UACTzB,MAAM,EAAE,cAAc;UAAE;UACxBwB,aAAa,EAAE;QACjB,CAAC;QACDG,OAAO,EAAE;UACPC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACxCC,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;UACrCC,gBAAgB,EAAE;QACpB,CAAC;QACDP,QAAQ,EAAE;UACRQ,iBAAiB,EAAE,KAAK;UAAE;UAC1BC,WAAW,EAAE,KAAK;UAAE;UACpBC,WAAW,EAAE,IAAI,CAAE;QACrB;MACF,CAAC;IACH;EACF;;EAEA;EACA,MAAMC,YAAYA,CAACC,MAAc,GAAG,MAAM,EAiBvC;IACD,IAAI;MAAA,IAAAC,qBAAA;MACF;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAACzE,cAAc,CAAC,CAAC;;MAE/C;MACA,MAAM0E,eAAe,GAAG,MAAM5E,SAAS,CAACO,GAAG,CAAC,mBAAmB,CAAC;;MAEhE;MACA,MAAMsE,SAAS,GAAG,MAAM,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;MAE5C,OAAO;QACL/D,aAAa,EAAE6D,eAAe,CAAC5D,cAAc,IAAI,CAAC;QAClDC,cAAc,EAAE,CAAC;QAAE;QACnBJ,UAAU,EAAE8D,WAAW,CAAC9D,UAAU;QAClCF,UAAU,EAAEgE,WAAW,CAAChE,UAAU;QAClCoE,WAAW,EAAE,CAAC;QAAE;QAChBC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZxC,YAAY,EAAEoC,SAAS,CAACK,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtD,GAAG,CAACuD,IAAI,KAAK;UACrD/C,KAAK,EAAE+C,IAAI,CAAC/C,KAAK;UACjBgD,OAAO,EAAED,IAAI,CAACC,OAAO;UACrBC,WAAW,EAAEF,IAAI,CAACE,WAAW,IAAI;QACnC,CAAC,CAAC,CAAC;QACHC,cAAc,EAAE,EAAAb,qBAAA,GAAAE,eAAe,CAACY,mBAAmB,cAAAd,qBAAA,uBAAnCA,qBAAA,CAAqCS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI;MACtE,CAAC;IACH,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACA,MAAM+B,WAAW,GAAG,MAAM,IAAI,CAACzE,cAAc,CAAC,CAAC;MAC/C,OAAO;QACLa,aAAa,EAAE,CAAC;QAChBE,cAAc,EAAE,CAAC;QACjBJ,UAAU,EAAE8D,WAAW,CAAC9D,UAAU;QAClCF,UAAU,EAAEgE,WAAW,CAAChE,UAAU;QAClCoE,WAAW,EAAE,CAAC;QACdC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZxC,YAAY,EAAE,EAAE;QAChB8C,cAAc,EAAE;MAClB,CAAC;IACH;EACF;;EAEA;EACA,MAAMT,QAAQA,CAACW,IAAY,GAAG,CAAC,EAAEC,KAAa,GAAG,EAAE,EAAEC,MAAe,EAKjE;IACD,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCJ,IAAI,EAAEA,IAAI,CAACvD,QAAQ,CAAC,CAAC;QACrB4D,SAAS,EAAEJ,KAAK,CAACxD,QAAQ,CAAC,CAAC;QAC3B,IAAIyD,MAAM,IAAI;UAAEA;QAAO,CAAC;MAC1B,CAAC,CAAC;MACF,MAAMhC,WAAW,GAAG,MAAM3D,SAAS,CAACO,GAAG,CAAC,gBAAgBqF,MAAM,EAAE,CAAC;;MAEjE;MACAxF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAIG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEkD,WAAW,CAAC;MACvF,OAAO;QACLuB,KAAK,EAAEvB,WAAW,CAACuB,KAAK,CAACrD,GAAG,CAAEuD,IAAS,KAAM;UAC3CrD,EAAE,EAAEqD,IAAI,CAACrD,EAAE;UACXM,KAAK,EAAE+C,IAAI,CAAC/C,KAAK;UACjBgD,OAAO,EAAEU,KAAK,CAACC,OAAO,CAACZ,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI,CAACC,OAAO,CAACY,IAAI,CAAC,IAAI,CAAC,GAAGb,IAAI,CAACC,OAAO;UAC7EC,WAAW,EAAE,CAAC;UAAE;UAChBY,UAAU,EAAEd,IAAI,CAACe,UAAU;UAC3B7D,MAAM,EAAE8C,IAAI,CAACgB,iBAAiB;UAC9BC,QAAQ,EAAEjB,IAAI,CAACkB,SAAS;UACxBC,QAAQ,EAAEnB,IAAI,CAACmB,QAAQ;UACvBC,IAAI,EAAEpB,IAAI,CAACoB,IAAI,IAAI;QACrB,CAAC,CAAC,CAAC;QACHC,KAAK,EAAE9C,WAAW,CAAC8C,KAAK;QACxBhB,IAAI,EAAE9B,WAAW,CAAC8B,IAAI;QACtBiB,UAAU,EAAE1E,IAAI,CAAC2E,IAAI,CAAChD,WAAW,CAAC8C,KAAK,GAAG9C,WAAW,CAACmC,SAAS;MACjE,CAAC;IACH,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdxC,OAAO,CAACwC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA,OAAO;QACLsC,KAAK,EAAE,CACL;UACEnD,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,iBAAiB;UACxBgD,OAAO,EAAE,YAAY;UACrBC,WAAW,EAAE,GAAG;UAChBY,UAAU,EAAE,YAAY;UACxB5D,MAAM,EAAE,WAAW;UACnB+D,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BE,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU;QACzC,CAAC,EACD;UACEzE,EAAE,EAAE,GAAG;UACPM,KAAK,EAAE,6CAA6C;UACpDgD,OAAO,EAAE,eAAe;UACxBC,WAAW,EAAE,GAAG;UAChBY,UAAU,EAAE,YAAY;UACxB5D,MAAM,EAAE,YAAY;UACpB+D,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;UAC1BE,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,CAAC,mBAAmB,EAAE,WAAW;QACzC,CAAC,CACF;QACDC,KAAK,EAAE,GAAG;QACVhB,IAAI,EAAE,CAAC;QACPiB,UAAU,EAAE;MACd,CAAC;IACH;EACF;EAEA,MAAME,UAAUA,CAACC,IAAU,EAAE9D,IAAoB,EAAiD;IAChG,MAAM+D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7BI,MAAM,CAACC,OAAO,CAACnE,IAAI,CAAC,CAACoE,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;MAC7C,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACzCP,QAAQ,CAACE,MAAM,CAACI,GAAG,EAAEC,KAAK,CAACnF,QAAQ,CAAC,CAAC,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,MAAM5B,QAAQ,GAAG,MAAMN,SAAS,CAACuH,IAAI,CAAC,qBAAqB,EAAET,QAAQ,EAAE;MACrEU,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOlH,QAAQ,CAACyC,IAAI;EACtB;EAEA,MAAM0E,UAAUA,CAACC,MAAc,EAAgC;IAC7D,MAAMpH,QAAQ,GAAG,MAAMN,SAAS,CAAC2H,MAAM,CAAC,gBAAgBD,MAAM,EAAE,CAAC;IACjE,OAAOpH,QAAQ,CAACyC,IAAI;EACtB;EAEA,MAAM6E,UAAUA,CAACF,MAAc,EAAE3E,IAA6B,EAAqB;IACjF,MAAMzC,QAAQ,GAAG,MAAMN,SAAS,CAAC6H,GAAG,CAAC,gBAAgBH,MAAM,EAAE,EAAE3E,IAAI,CAAC;IACpE,OAAOzC,QAAQ,CAACyC,IAAI;EACtB;EAEA,MAAM+E,mBAAmBA,CAAA,EAAgC;IACvD,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAI9E,KAAK,CAAC,8BAA8B,CAAC;IACjD,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd;MACA,OAAO,CACL;QACEmF,OAAO,EAAE,GAAG;QACZzF,MAAM,EAAE,YAAY;QACpB0F,QAAQ,EAAE,EAAE;QACZ/E,OAAO,EAAE,6BAA6B;QACtCgF,UAAU,EAAE,IAAIzH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,EACD;QACEsH,OAAO,EAAE,GAAG;QACZzF,MAAM,EAAE,SAAS;QACjB0F,QAAQ,EAAE,CAAC;QACX/E,OAAO,EAAE,qBAAqB;QAC9BgF,UAAU,EAAE,IAAIzH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC,CACF;IACH;EACF;EAEA,MAAMyH,aAAaA,CAACR,MAAc,EAAgC;IAChE,MAAMpH,QAAQ,GAAG,MAAMN,SAAS,CAACuH,IAAI,CAAC,+BAA+BG,MAAM,EAAE,CAAC;IAC9E,OAAOpH,QAAQ,CAACyC,IAAI;EACtB;EAEA,MAAMoF,gBAAgBA,CAACT,MAAc,EAAgC;IACnE,MAAMpH,QAAQ,GAAG,MAAMN,SAAS,CAACuH,IAAI,CAAC,4BAA4BG,MAAM,EAAE,CAAC;IAC3E,OAAOpH,QAAQ,CAACyC,IAAI;EACtB;;EAEA;EACA,MAAMqF,QAAQA,CAAC3C,IAAY,GAAG,CAAC,EAAEC,KAAa,GAAG,EAAE,EAAEC,MAAe,EAKjE;IACD,IAAI;MACFvF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAAEoF,IAAI;QAAEC,KAAK;QAAEC;MAAO,CAAC,CAAC;MACtE,MAAM0C,IAAI,GAAG,CAAC5C,IAAI,GAAG,CAAC,IAAIC,KAAK;MAC/B,MAAME,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCwC,IAAI,EAAEA,IAAI,CAACnG,QAAQ,CAAC,CAAC;QACrBwD,KAAK,EAAEA,KAAK,CAACxD,QAAQ,CAAC,CAAC;QACvB,IAAIyD,MAAM,IAAI;UAAEA;QAAO,CAAC;MAC1B,CAAC,CAAC;MAEFvF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,WAAWuF,MAAM,EAAE,CAAC;MACzD,MAAMtF,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,WAAWqF,MAAM,EAAE,CAAC;MACzDxF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;;MAE9C;MACA,MAAMgI,KAAK,GAAGhI,QAAQ,CAACgI,KAAK,IAAIhI,QAAQ,CAAC,CAAC;MAC1C,MAAMmG,KAAK,GAAGnG,QAAQ,CAACmG,KAAK,IAAI6B,KAAK,CAACC,MAAM;MAE5C,MAAMC,MAAM,GAAG;QACbF,KAAK,EAAEA,KAAK,CAACzG,GAAG,CAAEW,IAAS,KAAM;UAC/BT,EAAE,EAAES,IAAI,CAACT,EAAE;UACX0G,KAAK,EAAEjG,IAAI,CAACiG,KAAK;UACjBC,SAAS,EAAElG,IAAI,CAACkG,SAAS,IAAIlG,IAAI,CAACmG,IAAI,IAAI,SAAS;UACnDC,IAAI,EAAEpG,IAAI,CAACoG,IAAI,CAACC,WAAW,CAAC,CAAC;UAC7B1C,UAAU,EAAE3D,IAAI,CAAC2D,UAAU;UAC3B2C,UAAU,EAAEtG,IAAI,CAACsG,UAAU,IAAItG,IAAI,CAAC2D,UAAU;UAC9C4C,SAAS,EAAEvG,IAAI,CAACuG;QAClB,CAAC,CAAC,CAAC;QACHtC,KAAK,EAAEA,KAAK;QACZhB,IAAI,EAAEA,IAAI;QACViB,UAAU,EAAE1E,IAAI,CAAC2E,IAAI,CAACF,KAAK,GAAGf,KAAK;MACrC,CAAC;MAEDtF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmI,MAAM,CAAC;MAC5C,OAAOA,MAAM;IACf,CAAC,CAAC,OAAO5F,KAAU,EAAE;MAAA,IAAAoG,gBAAA,EAAAC,gBAAA;MACnB7I,OAAO,CAACwC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CxC,OAAO,CAACwC,KAAK,CAAC,gBAAgB,GAAAoG,gBAAA,GAAEpG,KAAK,CAACtC,QAAQ,cAAA0I,gBAAA,uBAAdA,gBAAA,CAAgBjG,IAAI,CAAC;MACrD3C,OAAO,CAACwC,KAAK,CAAC,eAAe,GAAAqG,gBAAA,GAAErG,KAAK,CAACtC,QAAQ,cAAA2I,gBAAA,uBAAdA,gBAAA,CAAgB3G,MAAM,CAAC;MACtD,MAAM,IAAIU,KAAK,CAAC,0BAA0BJ,KAAK,CAACK,OAAO,EAAE,CAAC;IAC5D;EACF;EAEA,MAAMiG,cAAcA,CAACC,MAAc,EAAEP,IAAsB,EAAiB;IAC1E;IACA;IACA,MAAM,IAAI5F,KAAK,CAAC,0CAA0C,CAAC;EAC7D;EAEA,MAAMoG,gBAAgBA,CAACD,MAAc,EAAiB;IACpD;IACA,MAAM7I,QAAQ,GAAG,MAAMN,SAAS,CAAC6H,GAAG,CAAC,UAAUsB,MAAM,WAAW,CAAC;IACjE,OAAO7I,QAAQ,CAACyC,IAAI;EACtB;AACF;AAEA,OAAO,MAAMsG,YAAY,GAAG,IAAIpJ,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}