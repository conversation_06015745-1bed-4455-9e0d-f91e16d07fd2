{"ast": null, "code": "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport clamp from '@mui/utils/clamp';\n\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value, min = 0, max = 1) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(`MUI: The value provided ${value} is out of range [${min}, ${max}].`);\n    }\n  }\n  return clamp(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nexport function hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(`.{1,${color.length >= 6 ? 2 : 1}}`, 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (color.length !== color.trim().length) {\n      console.error(`MUI: The color: \"${color}\" is invalid. Make sure the color input doesn't contain leading/trailing space.`);\n    }\n  }\n  return colors ? `rgb${colors.length === 4 ? 'a' : ''}(${colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', ')})` : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? `0${hex}` : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nexport function decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (!['rgb', 'rgba', 'hsl', 'hsla', 'color'].includes(type)) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported \\`${color}\\` color.\\n` + 'The following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().' : _formatErrorMessage(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (!['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].includes(colorSpace)) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: unsupported \\`${colorSpace}\\` color space.\\n` + 'The following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.' : _formatErrorMessage(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nexport const colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.includes('hsl') && idx !== 0 ? `${val}%` : val).join(' ');\n};\nexport const private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexport function recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.includes('rgb')) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.includes('hsl')) {\n    values[1] = `${values[1]}%`;\n    values[2] = `${values[2]}%`;\n  }\n  if (type.includes('color')) {\n    values = `${colorSpace} ${values.join(' ')}`;\n  } else {\n    values = `${values.join(', ')}`;\n  }\n  return `${type}(${values})`;\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nexport function rgbToHex(color) {\n  // Idempotent\n  if (color.startsWith('#')) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return `#${values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join('')}`;\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nexport function hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nexport function getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nexport function getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = `/${value}`;\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nexport function private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.includes('rgb') || color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.includes('rgb')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function emphasize(color, coefficient = 0.15) {\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nexport function private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nexport function blend(background, overlay, opacity, gamma = 1.0) {\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}", "map": {"version": 3, "names": ["_formatErrorMessage", "clamp", "clampWrapper", "value", "min", "max", "process", "env", "NODE_ENV", "console", "error", "hexToRgb", "color", "slice", "re", "RegExp", "length", "colors", "match", "map", "n", "trim", "index", "parseInt", "Math", "round", "join", "intToHex", "int", "hex", "toString", "decomposeColor", "type", "char<PERSON>t", "marker", "indexOf", "substring", "includes", "Error", "values", "colorSpace", "split", "shift", "parseFloat", "colorChannel", "decomposedColor", "val", "idx", "private_safeColorChannel", "warning", "warn", "recomposeColor", "i", "rgbToHex", "startsWith", "hslToRgb", "h", "s", "l", "a", "f", "k", "rgb", "push", "getLuminance", "Number", "toFixed", "getContrastRatio", "foreground", "background", "lumA", "lumB", "alpha", "private_safeAlpha", "darken", "coefficient", "private_safeDarken", "lighten", "private_safeLighten", "emphasize", "private_safeEmphasize", "blend", "overlay", "opacity", "gamma", "blendChannel", "b", "o", "backgroundColor", "overlayColor"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/colorManipulator/colorManipulator.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\n/* eslint-disable @typescript-eslint/naming-convention */\nimport clamp from '@mui/utils/clamp';\n\n/**\n * Returns a number whose value is limited to the given range.\n * @param {number} value The value to be clamped\n * @param {number} min The lower boundary of the output range\n * @param {number} max The upper boundary of the output range\n * @returns {number} A number in the range [min, max]\n */\nfunction clampWrapper(value, min = 0, max = 1) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (value < min || value > max) {\n      console.error(`MUI: The value provided ${value} is out of range [${min}, ${max}].`);\n    }\n  }\n  return clamp(value, min, max);\n}\n\n/**\n * Converts a color from CSS hex format to CSS rgb format.\n * @param {string} color - Hex color, i.e. #nnn or #nnnnnn\n * @returns {string} A CSS rgb color string\n */\nexport function hexToRgb(color) {\n  color = color.slice(1);\n  const re = new RegExp(`.{1,${color.length >= 6 ? 2 : 1}}`, 'g');\n  let colors = color.match(re);\n  if (colors && colors[0].length === 1) {\n    colors = colors.map(n => n + n);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (color.length !== color.trim().length) {\n      console.error(`MUI: The color: \"${color}\" is invalid. Make sure the color input doesn't contain leading/trailing space.`);\n    }\n  }\n  return colors ? `rgb${colors.length === 4 ? 'a' : ''}(${colors.map((n, index) => {\n    return index < 3 ? parseInt(n, 16) : Math.round(parseInt(n, 16) / 255 * 1000) / 1000;\n  }).join(', ')})` : '';\n}\nfunction intToHex(int) {\n  const hex = int.toString(16);\n  return hex.length === 1 ? `0${hex}` : hex;\n}\n\n/**\n * Returns an object with the type and values of a color.\n *\n * Note: Does not support rgb % values.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {object} - A MUI color object: {type: string, values: number[]}\n */\nexport function decomposeColor(color) {\n  // Idempotent\n  if (color.type) {\n    return color;\n  }\n  if (color.charAt(0) === '#') {\n    return decomposeColor(hexToRgb(color));\n  }\n  const marker = color.indexOf('(');\n  const type = color.substring(0, marker);\n  if (!['rgb', 'rgba', 'hsl', 'hsla', 'color'].includes(type)) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported \\`${color}\\` color.\\n` + 'The following formats are supported: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().' : _formatErrorMessage(9, color));\n  }\n  let values = color.substring(marker + 1, color.length - 1);\n  let colorSpace;\n  if (type === 'color') {\n    values = values.split(' ');\n    colorSpace = values.shift();\n    if (values.length === 4 && values[3].charAt(0) === '/') {\n      values[3] = values[3].slice(1);\n    }\n    if (!['srgb', 'display-p3', 'a98-rgb', 'prophoto-rgb', 'rec-2020'].includes(colorSpace)) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: unsupported \\`${colorSpace}\\` color space.\\n` + 'The following color spaces are supported: srgb, display-p3, a98-rgb, prophoto-rgb, rec-2020.' : _formatErrorMessage(10, colorSpace));\n    }\n  } else {\n    values = values.split(',');\n  }\n  values = values.map(value => parseFloat(value));\n  return {\n    type,\n    values,\n    colorSpace\n  };\n}\n\n/**\n * Returns a channel created from the input color.\n *\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {string} - The channel for the color, that can be used in rgba or hsla colors\n */\nexport const colorChannel = color => {\n  const decomposedColor = decomposeColor(color);\n  return decomposedColor.values.slice(0, 3).map((val, idx) => decomposedColor.type.includes('hsl') && idx !== 0 ? `${val}%` : val).join(' ');\n};\nexport const private_safeColorChannel = (color, warning) => {\n  try {\n    return colorChannel(color);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n};\n\n/**\n * Converts a color object with type and values to a string.\n * @param {object} color - Decomposed color\n * @param {string} color.type - One of: 'rgb', 'rgba', 'hsl', 'hsla', 'color'\n * @param {array} color.values - [n,n,n] or [n,n,n,n]\n * @returns {string} A CSS color string\n */\nexport function recomposeColor(color) {\n  const {\n    type,\n    colorSpace\n  } = color;\n  let {\n    values\n  } = color;\n  if (type.includes('rgb')) {\n    // Only convert the first 3 values to int (i.e. not alpha)\n    values = values.map((n, i) => i < 3 ? parseInt(n, 10) : n);\n  } else if (type.includes('hsl')) {\n    values[1] = `${values[1]}%`;\n    values[2] = `${values[2]}%`;\n  }\n  if (type.includes('color')) {\n    values = `${colorSpace} ${values.join(' ')}`;\n  } else {\n    values = `${values.join(', ')}`;\n  }\n  return `${type}(${values})`;\n}\n\n/**\n * Converts a color from CSS rgb format to CSS hex format.\n * @param {string} color - RGB color, i.e. rgb(n, n, n)\n * @returns {string} A CSS rgb color string, i.e. #nnnnnn\n */\nexport function rgbToHex(color) {\n  // Idempotent\n  if (color.startsWith('#')) {\n    return color;\n  }\n  const {\n    values\n  } = decomposeColor(color);\n  return `#${values.map((n, i) => intToHex(i === 3 ? Math.round(255 * n) : n)).join('')}`;\n}\n\n/**\n * Converts a color from hsl format to rgb format.\n * @param {string} color - HSL color values\n * @returns {string} rgb color values\n */\nexport function hslToRgb(color) {\n  color = decomposeColor(color);\n  const {\n    values\n  } = color;\n  const h = values[0];\n  const s = values[1] / 100;\n  const l = values[2] / 100;\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  let type = 'rgb';\n  const rgb = [Math.round(f(0) * 255), Math.round(f(8) * 255), Math.round(f(4) * 255)];\n  if (color.type === 'hsla') {\n    type += 'a';\n    rgb.push(values[3]);\n  }\n  return recomposeColor({\n    type,\n    values: rgb\n  });\n}\n/**\n * The relative brightness of any point in a color space,\n * normalized to 0 for darkest black and 1 for lightest white.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @returns {number} The relative brightness of the color in the range 0 - 1\n */\nexport function getLuminance(color) {\n  color = decomposeColor(color);\n  let rgb = color.type === 'hsl' || color.type === 'hsla' ? decomposeColor(hslToRgb(color)).values : color.values;\n  rgb = rgb.map(val => {\n    if (color.type !== 'color') {\n      val /= 255; // normalized\n    }\n    return val <= 0.03928 ? val / 12.92 : ((val + 0.055) / 1.055) ** 2.4;\n  });\n\n  // Truncate at 3 digits\n  return Number((0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2]).toFixed(3));\n}\n\n/**\n * Calculates the contrast ratio between two colors.\n *\n * Formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n * @param {string} foreground - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @param {string} background - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla()\n * @returns {number} A contrast ratio value in the range 0 - 21.\n */\nexport function getContrastRatio(foreground, background) {\n  const lumA = getLuminance(foreground);\n  const lumB = getLuminance(background);\n  return (Math.max(lumA, lumB) + 0.05) / (Math.min(lumA, lumB) + 0.05);\n}\n\n/**\n * Sets the absolute transparency of a color.\n * Any existing alpha values are overwritten.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} value - value to set the alpha channel to in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function alpha(color, value) {\n  color = decomposeColor(color);\n  value = clampWrapper(value);\n  if (color.type === 'rgb' || color.type === 'hsl') {\n    color.type += 'a';\n  }\n  if (color.type === 'color') {\n    color.values[3] = `/${value}`;\n  } else {\n    color.values[3] = value;\n  }\n  return recomposeColor(color);\n}\nexport function private_safeAlpha(color, value, warning) {\n  try {\n    return alpha(color, value);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darkens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function darken(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] *= 1 - coefficient;\n  } else if (color.type.includes('rgb') || color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] *= 1 - coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeDarken(color, coefficient, warning) {\n  try {\n    return darken(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Lightens a color.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function lighten(color, coefficient) {\n  color = decomposeColor(color);\n  coefficient = clampWrapper(coefficient);\n  if (color.type.includes('hsl')) {\n    color.values[2] += (100 - color.values[2]) * coefficient;\n  } else if (color.type.includes('rgb')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (255 - color.values[i]) * coefficient;\n    }\n  } else if (color.type.includes('color')) {\n    for (let i = 0; i < 3; i += 1) {\n      color.values[i] += (1 - color.values[i]) * coefficient;\n    }\n  }\n  return recomposeColor(color);\n}\nexport function private_safeLighten(color, coefficient, warning) {\n  try {\n    return lighten(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Darken or lighten a color, depending on its luminance.\n * Light colors are darkened, dark colors are lightened.\n * @param {string} color - CSS color, i.e. one of: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color()\n * @param {number} coefficient=0.15 - multiplier in the range 0 - 1\n * @returns {string} A CSS color string. Hex input values are returned as rgb\n */\nexport function emphasize(color, coefficient = 0.15) {\n  return getLuminance(color) > 0.5 ? darken(color, coefficient) : lighten(color, coefficient);\n}\nexport function private_safeEmphasize(color, coefficient, warning) {\n  try {\n    return emphasize(color, coefficient);\n  } catch (error) {\n    if (warning && process.env.NODE_ENV !== 'production') {\n      console.warn(warning);\n    }\n    return color;\n  }\n}\n\n/**\n * Blend a transparent overlay color with a background color, resulting in a single\n * RGB color.\n * @param {string} background - CSS color\n * @param {string} overlay - CSS color\n * @param {number} opacity - Opacity multiplier in the range 0 - 1\n * @param {number} [gamma=1.0] - Gamma correction factor. For gamma-correct blending, 2.2 is usual.\n */\nexport function blend(background, overlay, opacity, gamma = 1.0) {\n  const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n  const backgroundColor = decomposeColor(background);\n  const overlayColor = decomposeColor(overlay);\n  const rgb = [blendChannel(backgroundColor.values[0], overlayColor.values[0]), blendChannel(backgroundColor.values[1], overlayColor.values[1]), blendChannel(backgroundColor.values[2], overlayColor.values[2])];\n  return recomposeColor({\n    type: 'rgb',\n    values: rgb\n  });\n}"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE;AACA,OAAOC,KAAK,MAAM,kBAAkB;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAE;EAC7C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIL,KAAK,GAAGC,GAAG,IAAID,KAAK,GAAGE,GAAG,EAAE;MAC9BI,OAAO,CAACC,KAAK,CAAC,2BAA2BP,KAAK,qBAAqBC,GAAG,KAAKC,GAAG,IAAI,CAAC;IACrF;EACF;EACA,OAAOJ,KAAK,CAACE,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,QAAQA,CAACC,KAAK,EAAE;EAC9BA,KAAK,GAAGA,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;EACtB,MAAMC,EAAE,GAAG,IAAIC,MAAM,CAAC,OAAOH,KAAK,CAACI,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/D,IAAIC,MAAM,GAAGL,KAAK,CAACM,KAAK,CAACJ,EAAE,CAAC;EAC5B,IAAIG,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACD,MAAM,KAAK,CAAC,EAAE;IACpCC,MAAM,GAAGA,MAAM,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC;EACjC;EACA,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAII,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACS,IAAI,CAAC,CAAC,CAACL,MAAM,EAAE;MACxCP,OAAO,CAACC,KAAK,CAAC,oBAAoBE,KAAK,iFAAiF,CAAC;IAC3H;EACF;EACA,OAAOK,MAAM,GAAG,MAAMA,MAAM,CAACD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIC,MAAM,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEE,KAAK,KAAK;IAC/E,OAAOA,KAAK,GAAG,CAAC,GAAGC,QAAQ,CAACH,CAAC,EAAE,EAAE,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACF,QAAQ,CAACH,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI;EACtF,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,EAAE;AACvB;AACA,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,GAAG,GAAGD,GAAG,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC5B,OAAOD,GAAG,CAACb,MAAM,KAAK,CAAC,GAAG,IAAIa,GAAG,EAAE,GAAGA,GAAG;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACnB,KAAK,EAAE;EACpC;EACA,IAAIA,KAAK,CAACoB,IAAI,EAAE;IACd,OAAOpB,KAAK;EACd;EACA,IAAIA,KAAK,CAACqB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC3B,OAAOF,cAAc,CAACpB,QAAQ,CAACC,KAAK,CAAC,CAAC;EACxC;EACA,MAAMsB,MAAM,GAAGtB,KAAK,CAACuB,OAAO,CAAC,GAAG,CAAC;EACjC,MAAMH,IAAI,GAAGpB,KAAK,CAACwB,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC;EACvC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACG,QAAQ,CAACL,IAAI,CAAC,EAAE;IAC3D,MAAM,IAAIM,KAAK,CAAChC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,sBAAsBI,KAAK,aAAa,GAAG,4FAA4F,GAAGZ,mBAAmB,CAAC,CAAC,EAAEY,KAAK,CAAC,CAAC;EAClO;EACA,IAAI2B,MAAM,GAAG3B,KAAK,CAACwB,SAAS,CAACF,MAAM,GAAG,CAAC,EAAEtB,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;EAC1D,IAAIwB,UAAU;EACd,IAAIR,IAAI,KAAK,OAAO,EAAE;IACpBO,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC1BD,UAAU,GAAGD,MAAM,CAACG,KAAK,CAAC,CAAC;IAC3B,IAAIH,MAAM,CAACvB,MAAM,KAAK,CAAC,IAAIuB,MAAM,CAAC,CAAC,CAAC,CAACN,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACtDM,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC1B,KAAK,CAAC,CAAC,CAAC;IAChC;IACA,IAAI,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,CAAC,CAACwB,QAAQ,CAACG,UAAU,CAAC,EAAE;MACvF,MAAM,IAAIF,KAAK,CAAChC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,sBAAsBgC,UAAU,mBAAmB,GAAG,8FAA8F,GAAGxC,mBAAmB,CAAC,EAAE,EAAEwC,UAAU,CAAC,CAAC;IACrP;EACF,CAAC,MAAM;IACLD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAC5B;EACAF,MAAM,GAAGA,MAAM,CAACpB,GAAG,CAAChB,KAAK,IAAIwC,UAAU,CAACxC,KAAK,CAAC,CAAC;EAC/C,OAAO;IACL6B,IAAI;IACJO,MAAM;IACNC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,YAAY,GAAGhC,KAAK,IAAI;EACnC,MAAMiC,eAAe,GAAGd,cAAc,CAACnB,KAAK,CAAC;EAC7C,OAAOiC,eAAe,CAACN,MAAM,CAAC1B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACM,GAAG,CAAC,CAAC2B,GAAG,EAAEC,GAAG,KAAKF,eAAe,CAACb,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,IAAIU,GAAG,KAAK,CAAC,GAAG,GAAGD,GAAG,GAAG,GAAGA,GAAG,CAAC,CAACpB,IAAI,CAAC,GAAG,CAAC;AAC5I,CAAC;AACD,OAAO,MAAMsB,wBAAwB,GAAGA,CAACpC,KAAK,EAAEqC,OAAO,KAAK;EAC1D,IAAI;IACF,OAAOL,YAAY,CAAChC,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOF,KAAK,EAAE;IACd,IAAIuC,OAAO,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACyC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOrC,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuC,cAAcA,CAACvC,KAAK,EAAE;EACpC,MAAM;IACJoB,IAAI;IACJQ;EACF,CAAC,GAAG5B,KAAK;EACT,IAAI;IACF2B;EACF,CAAC,GAAG3B,KAAK;EACT,IAAIoB,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;IACxB;IACAE,MAAM,GAAGA,MAAM,CAACpB,GAAG,CAAC,CAACC,CAAC,EAAEgC,CAAC,KAAKA,CAAC,GAAG,CAAC,GAAG7B,QAAQ,CAACH,CAAC,EAAE,EAAE,CAAC,GAAGA,CAAC,CAAC;EAC5D,CAAC,MAAM,IAAIY,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC/BE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG;IAC3BA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG;EAC7B;EACA,IAAIP,IAAI,CAACK,QAAQ,CAAC,OAAO,CAAC,EAAE;IAC1BE,MAAM,GAAG,GAAGC,UAAU,IAAID,MAAM,CAACb,IAAI,CAAC,GAAG,CAAC,EAAE;EAC9C,CAAC,MAAM;IACLa,MAAM,GAAG,GAAGA,MAAM,CAACb,IAAI,CAAC,IAAI,CAAC,EAAE;EACjC;EACA,OAAO,GAAGM,IAAI,IAAIO,MAAM,GAAG;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,QAAQA,CAACzC,KAAK,EAAE;EAC9B;EACA,IAAIA,KAAK,CAAC0C,UAAU,CAAC,GAAG,CAAC,EAAE;IACzB,OAAO1C,KAAK;EACd;EACA,MAAM;IACJ2B;EACF,CAAC,GAAGR,cAAc,CAACnB,KAAK,CAAC;EACzB,OAAO,IAAI2B,MAAM,CAACpB,GAAG,CAAC,CAACC,CAAC,EAAEgC,CAAC,KAAKzB,QAAQ,CAACyB,CAAC,KAAK,CAAC,GAAG5B,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGL,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,EAAE;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,QAAQA,CAAC3C,KAAK,EAAE;EAC9BA,KAAK,GAAGmB,cAAc,CAACnB,KAAK,CAAC;EAC7B,MAAM;IACJ2B;EACF,CAAC,GAAG3B,KAAK;EACT,MAAM4C,CAAC,GAAGjB,MAAM,CAAC,CAAC,CAAC;EACnB,MAAMkB,CAAC,GAAGlB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMmB,CAAC,GAAGnB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;EACzB,MAAMoB,CAAC,GAAGF,CAAC,GAAGjC,IAAI,CAACpB,GAAG,CAACsD,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;EAChC,MAAME,CAAC,GAAGA,CAACxC,CAAC,EAAEyC,CAAC,GAAG,CAACzC,CAAC,GAAGoC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAKE,CAAC,GAAGC,CAAC,GAAGnC,IAAI,CAACnB,GAAG,CAACmB,IAAI,CAACpB,GAAG,CAACyD,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvF,IAAI7B,IAAI,GAAG,KAAK;EAChB,MAAM8B,GAAG,GAAG,CAACtC,IAAI,CAACC,KAAK,CAACmC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEpC,IAAI,CAACC,KAAK,CAACmC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEpC,IAAI,CAACC,KAAK,CAACmC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;EACpF,IAAIhD,KAAK,CAACoB,IAAI,KAAK,MAAM,EAAE;IACzBA,IAAI,IAAI,GAAG;IACX8B,GAAG,CAACC,IAAI,CAACxB,MAAM,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,OAAOY,cAAc,CAAC;IACpBnB,IAAI;IACJO,MAAM,EAAEuB;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAACpD,KAAK,EAAE;EAClCA,KAAK,GAAGmB,cAAc,CAACnB,KAAK,CAAC;EAC7B,IAAIkD,GAAG,GAAGlD,KAAK,CAACoB,IAAI,KAAK,KAAK,IAAIpB,KAAK,CAACoB,IAAI,KAAK,MAAM,GAAGD,cAAc,CAACwB,QAAQ,CAAC3C,KAAK,CAAC,CAAC,CAAC2B,MAAM,GAAG3B,KAAK,CAAC2B,MAAM;EAC/GuB,GAAG,GAAGA,GAAG,CAAC3C,GAAG,CAAC2B,GAAG,IAAI;IACnB,IAAIlC,KAAK,CAACoB,IAAI,KAAK,OAAO,EAAE;MAC1Bc,GAAG,IAAI,GAAG,CAAC,CAAC;IACd;IACA,OAAOA,GAAG,IAAI,OAAO,GAAGA,GAAG,GAAG,KAAK,GAAG,CAAC,CAACA,GAAG,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG;EACtE,CAAC,CAAC;;EAEF;EACA,OAAOmB,MAAM,CAAC,CAAC,MAAM,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,GAAG,CAAC,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACvD,MAAMC,IAAI,GAAGN,YAAY,CAACI,UAAU,CAAC;EACrC,MAAMG,IAAI,GAAGP,YAAY,CAACK,UAAU,CAAC;EACrC,OAAO,CAAC7C,IAAI,CAACnB,GAAG,CAACiE,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,KAAK/C,IAAI,CAACpB,GAAG,CAACkE,IAAI,EAAEC,IAAI,CAAC,GAAG,IAAI,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAC5D,KAAK,EAAET,KAAK,EAAE;EAClCS,KAAK,GAAGmB,cAAc,CAACnB,KAAK,CAAC;EAC7BT,KAAK,GAAGD,YAAY,CAACC,KAAK,CAAC;EAC3B,IAAIS,KAAK,CAACoB,IAAI,KAAK,KAAK,IAAIpB,KAAK,CAACoB,IAAI,KAAK,KAAK,EAAE;IAChDpB,KAAK,CAACoB,IAAI,IAAI,GAAG;EACnB;EACA,IAAIpB,KAAK,CAACoB,IAAI,KAAK,OAAO,EAAE;IAC1BpB,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,GAAG,IAAIpC,KAAK,EAAE;EAC/B,CAAC,MAAM;IACLS,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,GAAGpC,KAAK;EACzB;EACA,OAAOgD,cAAc,CAACvC,KAAK,CAAC;AAC9B;AACA,OAAO,SAAS6D,iBAAiBA,CAAC7D,KAAK,EAAET,KAAK,EAAE8C,OAAO,EAAE;EACvD,IAAI;IACF,OAAOuB,KAAK,CAAC5D,KAAK,EAAET,KAAK,CAAC;EAC5B,CAAC,CAAC,OAAOO,KAAK,EAAE;IACd,IAAIuC,OAAO,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACyC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOrC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS8D,MAAMA,CAAC9D,KAAK,EAAE+D,WAAW,EAAE;EACzC/D,KAAK,GAAGmB,cAAc,CAACnB,KAAK,CAAC;EAC7B+D,WAAW,GAAGzE,YAAY,CAACyE,WAAW,CAAC;EACvC,IAAI/D,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC9BzB,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGoC,WAAW;EACpC,CAAC,MAAM,IAAI/D,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,IAAIzB,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,OAAO,CAAC,EAAE;IACrE,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BxC,KAAK,CAAC2B,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,GAAGuB,WAAW;IACpC;EACF;EACA,OAAOxB,cAAc,CAACvC,KAAK,CAAC;AAC9B;AACA,OAAO,SAASgE,kBAAkBA,CAAChE,KAAK,EAAE+D,WAAW,EAAE1B,OAAO,EAAE;EAC9D,IAAI;IACF,OAAOyB,MAAM,CAAC9D,KAAK,EAAE+D,WAAW,CAAC;EACnC,CAAC,CAAC,OAAOjE,KAAK,EAAE;IACd,IAAIuC,OAAO,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACyC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOrC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiE,OAAOA,CAACjE,KAAK,EAAE+D,WAAW,EAAE;EAC1C/D,KAAK,GAAGmB,cAAc,CAACnB,KAAK,CAAC;EAC7B+D,WAAW,GAAGzE,YAAY,CAACyE,WAAW,CAAC;EACvC,IAAI/D,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC9BzB,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG3B,KAAK,CAAC2B,MAAM,CAAC,CAAC,CAAC,IAAIoC,WAAW;EAC1D,CAAC,MAAM,IAAI/D,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,KAAK,CAAC,EAAE;IACrC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BxC,KAAK,CAAC2B,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,GAAG,GAAGxC,KAAK,CAAC2B,MAAM,CAACa,CAAC,CAAC,IAAIuB,WAAW;IAC1D;EACF,CAAC,MAAM,IAAI/D,KAAK,CAACoB,IAAI,CAACK,QAAQ,CAAC,OAAO,CAAC,EAAE;IACvC,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7BxC,KAAK,CAAC2B,MAAM,CAACa,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGxC,KAAK,CAAC2B,MAAM,CAACa,CAAC,CAAC,IAAIuB,WAAW;IACxD;EACF;EACA,OAAOxB,cAAc,CAACvC,KAAK,CAAC;AAC9B;AACA,OAAO,SAASkE,mBAAmBA,CAAClE,KAAK,EAAE+D,WAAW,EAAE1B,OAAO,EAAE;EAC/D,IAAI;IACF,OAAO4B,OAAO,CAACjE,KAAK,EAAE+D,WAAW,CAAC;EACpC,CAAC,CAAC,OAAOjE,KAAK,EAAE;IACd,IAAIuC,OAAO,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACyC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOrC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmE,SAASA,CAACnE,KAAK,EAAE+D,WAAW,GAAG,IAAI,EAAE;EACnD,OAAOX,YAAY,CAACpD,KAAK,CAAC,GAAG,GAAG,GAAG8D,MAAM,CAAC9D,KAAK,EAAE+D,WAAW,CAAC,GAAGE,OAAO,CAACjE,KAAK,EAAE+D,WAAW,CAAC;AAC7F;AACA,OAAO,SAASK,qBAAqBA,CAACpE,KAAK,EAAE+D,WAAW,EAAE1B,OAAO,EAAE;EACjE,IAAI;IACF,OAAO8B,SAAS,CAACnE,KAAK,EAAE+D,WAAW,CAAC;EACtC,CAAC,CAAC,OAAOjE,KAAK,EAAE;IACd,IAAIuC,OAAO,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACpDC,OAAO,CAACyC,IAAI,CAACD,OAAO,CAAC;IACvB;IACA,OAAOrC,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqE,KAAKA,CAACZ,UAAU,EAAEa,OAAO,EAAEC,OAAO,EAAEC,KAAK,GAAG,GAAG,EAAE;EAC/D,MAAMC,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK/D,IAAI,CAACC,KAAK,CAAC,CAAC6D,CAAC,KAAK,CAAC,GAAGF,KAAK,CAAC,IAAI,CAAC,GAAGD,OAAO,CAAC,GAAGI,CAAC,KAAK,CAAC,GAAGH,KAAK,CAAC,GAAGD,OAAO,KAAKC,KAAK,CAAC;EACnH,MAAMI,eAAe,GAAGzD,cAAc,CAACsC,UAAU,CAAC;EAClD,MAAMoB,YAAY,GAAG1D,cAAc,CAACmD,OAAO,CAAC;EAC5C,MAAMpB,GAAG,GAAG,CAACuB,YAAY,CAACG,eAAe,CAACjD,MAAM,CAAC,CAAC,CAAC,EAAEkD,YAAY,CAAClD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE8C,YAAY,CAACG,eAAe,CAACjD,MAAM,CAAC,CAAC,CAAC,EAAEkD,YAAY,CAAClD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE8C,YAAY,CAACG,eAAe,CAACjD,MAAM,CAAC,CAAC,CAAC,EAAEkD,YAAY,CAAClD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/M,OAAOY,cAAc,CAAC;IACpBnB,IAAI,EAAE,KAAK;IACXO,MAAM,EAAEuB;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}