{"ast": null, "code": "/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj) {\n  let additionalPropertiesToCheck = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter() {\n  let additionalPropertiesToCheck = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return _ref => {\n    let [, value] = _ref;\n    return value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n  };\n}", "map": {"version": 3, "names": ["hasCorrectMainProperty", "obj", "main", "checkSimplePaletteColorValues", "additionalPropertiesToCheck", "arguments", "length", "undefined", "value", "hasOwnProperty", "createSimplePaletteValueFilter", "_ref"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/material/esm/utils/createSimplePaletteValueFilter.js"], "sourcesContent": ["/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj, additionalPropertiesToCheck = []) {\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter(additionalPropertiesToCheck = []) {\n  return ([, value]) => value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,sBAAsBA,CAACC,GAAG,EAAE;EACnC,OAAO,OAAOA,GAAG,CAACC,IAAI,KAAK,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACF,GAAG,EAAoC;EAAA,IAAlCG,2BAA2B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC1E,IAAI,CAACL,sBAAsB,CAACC,GAAG,CAAC,EAAE;IAChC,OAAO,KAAK;EACd;EACA,KAAK,MAAMO,KAAK,IAAIJ,2BAA2B,EAAE;IAC/C,IAAI,CAACH,GAAG,CAACQ,cAAc,CAACD,KAAK,CAAC,IAAI,OAAOP,GAAG,CAACO,KAAK,CAAC,KAAK,QAAQ,EAAE;MAChE,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,8BAA8BA,CAAA,EAAmC;EAAA,IAAlCN,2BAA2B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACrF,OAAOM,IAAA;IAAA,IAAC,GAAGH,KAAK,CAAC,GAAAG,IAAA;IAAA,OAAKH,KAAK,IAAIL,6BAA6B,CAACK,KAAK,EAAEJ,2BAA2B,CAAC;EAAA;AAClG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}