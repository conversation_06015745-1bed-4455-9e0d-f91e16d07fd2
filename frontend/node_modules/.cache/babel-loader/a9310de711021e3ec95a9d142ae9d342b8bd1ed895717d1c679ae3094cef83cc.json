{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx\",\n  _s = $RefreshSig$();\n/**\n * Citation viewer component for displaying detailed citation information\n */\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, Chip, Card, CardContent, IconButton, Tooltip, CircularProgress, Alert, Paper } from '@mui/material';\nimport { Close as CloseIcon, MenuBook as BookIcon, Link as LinkIcon, Star as StarIcon, ContentCopy as CopyIcon } from '@mui/icons-material';\nimport { useNotification } from '../../contexts/NotificationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CitationViewer = ({\n  citation,\n  open,\n  onClose\n}) => {\n  _s();\n  const [citationDetails, setCitationDetails] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const {\n    showSuccess,\n    showError\n  } = useNotification();\n  useEffect(() => {\n    if (open && citation) {\n      fetchCitationDetails();\n    }\n  }, [open, citation]);\n  const fetchCitationDetails = async () => {\n    if (!citation) return;\n    setIsLoading(true);\n    setError(null);\n    try {\n      // For now, we'll create mock detailed data since the citation ID might not be available\n      // In a real implementation, you would call: await qaService.getCitationDetails(citation.id);\n\n      const mockDetails = {\n        citation: {\n          id: citation.chunk_id,\n          cited_text: citation.cited_text,\n          relevance_score: citation.relevance_score,\n          citation_order: citation.citation_order\n        },\n        chunk: {\n          id: citation.chunk_id,\n          content: citation.cited_text,\n          page_number: citation.page_number || 0,\n          chapter_title: citation.chapter_title,\n          topic_category: 'general'\n        },\n        book: {\n          id: citation.book_id,\n          title: citation.book_title,\n          authors: citation.book_authors,\n          publisher: 'Medical Publisher',\n          publication_year: 2023\n        },\n        surrounding_chunks: [{\n          content: 'Previous context chunk that provides additional background information...',\n          chunk_index: 1\n        }, {\n          content: 'Following context chunk that continues the discussion...',\n          chunk_index: 3\n        }]\n      };\n      setCitationDetails(mockDetails);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message || 'Failed to load citation details';\n      setError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleCopyText = text => {\n    navigator.clipboard.writeText(text);\n    showSuccess('Text copied to clipboard');\n  };\n  const formatCitation = () => {\n    if (!citationDetails) return '';\n    const {\n      book,\n      chunk\n    } = citationDetails;\n    return `${book.authors.join(', ')}. ${book.title}. ${book.publisher}, ${book.publication_year}. Page ${chunk.page_number}.`;\n  };\n  if (!citation) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        minHeight: '60vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(BookIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Citation Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onClose,\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      dividers: true,\n      children: isLoading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this) : citationDetails ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          variant: \"outlined\",\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              color: \"primary\",\n              children: citationDetails.book.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: citationDetails.book.authors.join(', ')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mt: 1\n              },\n              children: [citationDetails.book.publisher && /*#__PURE__*/_jsxDEV(Chip, {\n                label: citationDetails.book.publisher,\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this), citationDetails.book.publication_year && /*#__PURE__*/_jsxDEV(Chip, {\n                label: citationDetails.book.publication_year,\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `Page ${citationDetails.chunk.page_number}`,\n                size: \"small\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Citation Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              icon: /*#__PURE__*/_jsxDEV(StarIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 25\n              }, this),\n              label: `${(citationDetails.citation.relevance_score * 100).toFixed(1)}% relevance`,\n              color: \"secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Citation #${citationDetails.citation.citation_order}`,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), citationDetails.chunk.chapter_title && /*#__PURE__*/_jsxDEV(Chip, {\n              label: citationDetails.chunk.chapter_title,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cited Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Copy text\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleCopyText(citationDetails.chunk.content),\n                children: /*#__PURE__*/_jsxDEV(CopyIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              backgroundColor: 'grey.50'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                lineHeight: 1.7\n              },\n              children: citationDetails.chunk.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), citationDetails.surrounding_chunks.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Surrounding Context\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 2\n            },\n            children: citationDetails.surrounding_chunks.map((chunk, index) => /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                backgroundColor: 'grey.25'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  py: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: [\"Context Chunk \", chunk.chunk_index]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: chunk.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Formatted Citation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              backgroundColor: 'primary.50'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontStyle: 'italic',\n                  flex: 1\n                },\n                children: formatCitation()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Copy citation\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleCopyText(formatCitation()),\n                  children: /*#__PURE__*/_jsxDEV(CopyIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), citationDetails && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 24\n        }, this),\n        onClick: () => {\n          // In a real implementation, this would navigate to the full book/chapter view\n          showSuccess('Book view feature coming soon!');\n        },\n        children: \"View in Book\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(CitationViewer, \"+Rr6b7NuWcovFhQ89O/tMQutWJo=\", false, function () {\n  return [useNotification];\n});\n_c = CitationViewer;\nexport default CitationViewer;\nvar _c;\n$RefreshReg$(_c, \"CitationViewer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "Chip", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Paper", "Close", "CloseIcon", "MenuBook", "BookIcon", "Link", "LinkIcon", "Star", "StarIcon", "ContentCopy", "CopyIcon", "useNotification", "jsxDEV", "_jsxDEV", "Citation<PERSON>iewer", "citation", "open", "onClose", "_s", "citationDetails", "setCitationDetails", "isLoading", "setIsLoading", "error", "setError", "showSuccess", "showError", "fetchCitationDetails", "mockDetails", "id", "chunk_id", "cited_text", "relevance_score", "citation_order", "chunk", "content", "page_number", "chapter_title", "topic_category", "book", "book_id", "title", "book_title", "authors", "book_authors", "publisher", "publication_year", "surrounding_chunks", "chunk_index", "_error$response", "_error$response$data", "errorMessage", "response", "data", "detail", "message", "handleCopyText", "text", "navigator", "clipboard", "writeText", "formatCitation", "join", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "minHeight", "children", "display", "justifyContent", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "size", "dividers", "py", "severity", "flexDirection", "gutterBottom", "mt", "label", "mb", "icon", "toFixed", "p", "backgroundColor", "lineHeight", "length", "map", "index", "fontStyle", "flex", "startIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/components/Citations/CitationViewer.tsx"], "sourcesContent": ["/**\n * Citation viewer component for displaying detailed citation information\n */\nimport React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON>,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  Chip,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip,\n  CircularProgress,\n  Alert,\n  Paper,\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  MenuBook as BookIcon,\n  Link as LinkIcon,\n  Star as StarIcon,\n  ContentCopy as CopyIcon,\n} from '@mui/icons-material';\nimport { qaService } from '../../services/qaService';\nimport { useNotification } from '../../contexts/NotificationContext';\nimport { Citation } from '../../types';\n\ninterface CitationViewerProps {\n  citation: Citation | null;\n  open: boolean;\n  onClose: () => void;\n}\n\ninterface CitationDetails {\n  citation: {\n    id: string;\n    cited_text: string;\n    relevance_score: number;\n    citation_order: number;\n  };\n  chunk: {\n    id: string;\n    content: string;\n    page_number: number;\n    chapter_title?: string;\n    topic_category?: string;\n  };\n  book: {\n    id: string;\n    title: string;\n    authors: string[];\n    publisher?: string;\n    publication_year?: number;\n  };\n  surrounding_chunks: Array<{\n    content: string;\n    chunk_index: number;\n  }>;\n}\n\nconst CitationViewer: React.FC<CitationViewerProps> = ({ citation, open, onClose }) => {\n  const [citationDetails, setCitationDetails] = useState<CitationDetails | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { showSuccess, showError } = useNotification();\n\n  useEffect(() => {\n    if (open && citation) {\n      fetchCitationDetails();\n    }\n  }, [open, citation]);\n\n  const fetchCitationDetails = async () => {\n    if (!citation) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // For now, we'll create mock detailed data since the citation ID might not be available\n      // In a real implementation, you would call: await qaService.getCitationDetails(citation.id);\n      \n      const mockDetails: CitationDetails = {\n        citation: {\n          id: citation.chunk_id,\n          cited_text: citation.cited_text,\n          relevance_score: citation.relevance_score,\n          citation_order: citation.citation_order,\n        },\n        chunk: {\n          id: citation.chunk_id,\n          content: citation.cited_text,\n          page_number: citation.page_number || 0,\n          chapter_title: citation.chapter_title,\n          topic_category: 'general',\n        },\n        book: {\n          id: citation.book_id,\n          title: citation.book_title,\n          authors: citation.book_authors,\n          publisher: 'Medical Publisher',\n          publication_year: 2023,\n        },\n        surrounding_chunks: [\n          {\n            content: 'Previous context chunk that provides additional background information...',\n            chunk_index: 1,\n          },\n          {\n            content: 'Following context chunk that continues the discussion...',\n            chunk_index: 3,\n          },\n        ],\n      };\n\n      setCitationDetails(mockDetails);\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Failed to load citation details';\n      setError(errorMessage);\n      showError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCopyText = (text: string) => {\n    navigator.clipboard.writeText(text);\n    showSuccess('Text copied to clipboard');\n  };\n\n  const formatCitation = () => {\n    if (!citationDetails) return '';\n    \n    const { book, chunk } = citationDetails;\n    return `${book.authors.join(', ')}. ${book.title}. ${book.publisher}, ${book.publication_year}. Page ${chunk.page_number}.`;\n  };\n\n  if (!citation) return null;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      PaperProps={{\n        sx: { minHeight: '60vh' }\n      }}\n    >\n      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <BookIcon color=\"primary\" />\n          <Typography variant=\"h6\">Citation Details</Typography>\n        </Box>\n        <IconButton onClick={onClose} size=\"small\">\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent dividers>\n        {isLoading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\">{error}</Alert>\n        ) : citationDetails ? (\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>\n            {/* Book Information */}\n            <Card variant=\"outlined\">\n              <CardContent>\n                <Typography variant=\"h6\" gutterBottom color=\"primary\">\n                  {citationDetails.book.title}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {citationDetails.book.authors.join(', ')}\n                </Typography>\n                <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>\n                  {citationDetails.book.publisher && (\n                    <Chip label={citationDetails.book.publisher} size=\"small\" variant=\"outlined\" />\n                  )}\n                  {citationDetails.book.publication_year && (\n                    <Chip label={citationDetails.book.publication_year} size=\"small\" variant=\"outlined\" />\n                  )}\n                  <Chip \n                    label={`Page ${citationDetails.chunk.page_number}`} \n                    size=\"small\" \n                    color=\"primary\" \n                  />\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Citation Metadata */}\n            <Box>\n              <Typography variant=\"h6\" gutterBottom>\n                Citation Information\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n                <Chip \n                  icon={<StarIcon />}\n                  label={`${(citationDetails.citation.relevance_score * 100).toFixed(1)}% relevance`}\n                  color=\"secondary\"\n                />\n                <Chip \n                  label={`Citation #${citationDetails.citation.citation_order}`}\n                  variant=\"outlined\"\n                />\n                {citationDetails.chunk.chapter_title && (\n                  <Chip \n                    label={citationDetails.chunk.chapter_title}\n                    variant=\"outlined\"\n                  />\n                )}\n              </Box>\n            </Box>\n\n            {/* Main Content */}\n            <Box>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                <Typography variant=\"h6\">\n                  Cited Content\n                </Typography>\n                <Tooltip title=\"Copy text\">\n                  <IconButton \n                    size=\"small\" \n                    onClick={() => handleCopyText(citationDetails.chunk.content)}\n                  >\n                    <CopyIcon />\n                  </IconButton>\n                </Tooltip>\n              </Box>\n              <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>\n                <Typography variant=\"body1\" sx={{ lineHeight: 1.7 }}>\n                  {citationDetails.chunk.content}\n                </Typography>\n              </Paper>\n            </Box>\n\n            {/* Surrounding Context */}\n            {citationDetails.surrounding_chunks.length > 0 && (\n              <Box>\n                <Typography variant=\"h6\" gutterBottom>\n                  Surrounding Context\n                </Typography>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                  {citationDetails.surrounding_chunks.map((chunk, index) => (\n                    <Card key={index} variant=\"outlined\" sx={{ backgroundColor: 'grey.25' }}>\n                      <CardContent sx={{ py: 2 }}>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          Context Chunk {chunk.chunk_index}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          {chunk.content}\n                        </Typography>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </Box>\n              </Box>\n            )}\n\n            {/* Formatted Citation */}\n            <Box>\n              <Typography variant=\"h6\" gutterBottom>\n                Formatted Citation\n              </Typography>\n              <Paper sx={{ p: 2, backgroundColor: 'primary.50' }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n                  <Typography variant=\"body2\" sx={{ fontStyle: 'italic', flex: 1 }}>\n                    {formatCitation()}\n                  </Typography>\n                  <Tooltip title=\"Copy citation\">\n                    <IconButton \n                      size=\"small\" \n                      onClick={() => handleCopyText(formatCitation())}\n                    >\n                      <CopyIcon />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n              </Paper>\n            </Box>\n          </Box>\n        ) : null}\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={onClose}>Close</Button>\n        {citationDetails && (\n          <Button \n            variant=\"contained\" \n            startIcon={<LinkIcon />}\n            onClick={() => {\n              // In a real implementation, this would navigate to the full book/chapter view\n              showSuccess('Book view feature coming soon!');\n            }}\n          >\n            View in Book\n          </Button>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CitationViewer;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,QACA,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,QAAQ,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,QAAQ,QAClB,qBAAqB;AAE5B,SAASC,eAAe,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoCrE,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAyB,IAAI,CAAC;EACpF,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM;IAAEyC,WAAW;IAAEC;EAAU,CAAC,GAAGf,eAAe,CAAC,CAAC;EAEpD1B,SAAS,CAAC,MAAM;IACd,IAAI+B,IAAI,IAAID,QAAQ,EAAE;MACpBY,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACX,IAAI,EAAED,QAAQ,CAAC,CAAC;EAEpB,MAAMY,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACZ,QAAQ,EAAE;IAEfO,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA;;MAEA,MAAMI,WAA4B,GAAG;QACnCb,QAAQ,EAAE;UACRc,EAAE,EAAEd,QAAQ,CAACe,QAAQ;UACrBC,UAAU,EAAEhB,QAAQ,CAACgB,UAAU;UAC/BC,eAAe,EAAEjB,QAAQ,CAACiB,eAAe;UACzCC,cAAc,EAAElB,QAAQ,CAACkB;QAC3B,CAAC;QACDC,KAAK,EAAE;UACLL,EAAE,EAAEd,QAAQ,CAACe,QAAQ;UACrBK,OAAO,EAAEpB,QAAQ,CAACgB,UAAU;UAC5BK,WAAW,EAAErB,QAAQ,CAACqB,WAAW,IAAI,CAAC;UACtCC,aAAa,EAAEtB,QAAQ,CAACsB,aAAa;UACrCC,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE;UACJV,EAAE,EAAEd,QAAQ,CAACyB,OAAO;UACpBC,KAAK,EAAE1B,QAAQ,CAAC2B,UAAU;UAC1BC,OAAO,EAAE5B,QAAQ,CAAC6B,YAAY;UAC9BC,SAAS,EAAE,mBAAmB;UAC9BC,gBAAgB,EAAE;QACpB,CAAC;QACDC,kBAAkB,EAAE,CAClB;UACEZ,OAAO,EAAE,2EAA2E;UACpFa,WAAW,EAAE;QACf,CAAC,EACD;UACEb,OAAO,EAAE,0DAA0D;UACnEa,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;MAED5B,kBAAkB,CAACQ,WAAW,CAAC;IACjC,CAAC,CAAC,OAAOL,KAAU,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAA1B,KAAK,CAAC6B,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAI/B,KAAK,CAACgC,OAAO,IAAI,iCAAiC;MACvG/B,QAAQ,CAAC2B,YAAY,CAAC;MACtBzB,SAAS,CAACyB,YAAY,CAAC;IACzB,CAAC,SAAS;MACR7B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,cAAc,GAAIC,IAAY,IAAK;IACvCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC;IACnChC,WAAW,CAAC,0BAA0B,CAAC;EACzC,CAAC;EAED,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC1C,eAAe,EAAE,OAAO,EAAE;IAE/B,MAAM;MAAEoB,IAAI;MAAEL;IAAM,CAAC,GAAGf,eAAe;IACvC,OAAO,GAAGoB,IAAI,CAACI,OAAO,CAACmB,IAAI,CAAC,IAAI,CAAC,KAAKvB,IAAI,CAACE,KAAK,KAAKF,IAAI,CAACM,SAAS,KAAKN,IAAI,CAACO,gBAAgB,UAAUZ,KAAK,CAACE,WAAW,GAAG;EAC7H,CAAC;EAED,IAAI,CAACrB,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEF,OAAA,CAAC3B,MAAM;IACL8B,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjB8C,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO;IAC1B,CAAE;IAAAC,QAAA,gBAEFvD,OAAA,CAAC1B,WAAW;MAAC+E,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,gBAC1FvD,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzDvD,OAAA,CAACT,QAAQ;UAACqE,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BhE,OAAA,CAACtB,UAAU;UAACuF,OAAO,EAAC,IAAI;UAAAV,QAAA,EAAC;QAAgB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACNhE,OAAA,CAACjB,UAAU;QAACmF,OAAO,EAAE9D,OAAQ;QAAC+D,IAAI,EAAC,OAAO;QAAAZ,QAAA,eACxCvD,OAAA,CAACX,SAAS;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdhE,OAAA,CAACzB,aAAa;MAAC6F,QAAQ;MAAAb,QAAA,EACpB/C,SAAS,gBACRR,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,eAC5DvD,OAAA,CAACf,gBAAgB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJtD,KAAK,gBACPV,OAAA,CAACd,KAAK;QAACoF,QAAQ,EAAC,OAAO;QAAAf,QAAA,EAAE7C;MAAK;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,GACrC1D,eAAe,gBACjBN,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEG,OAAO,EAAE,MAAM;UAAEe,aAAa,EAAE,QAAQ;UAAEZ,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAE5DvD,OAAA,CAACnB,IAAI;UAACoF,OAAO,EAAC,UAAU;UAAAV,QAAA,eACtBvD,OAAA,CAAClB,WAAW;YAAAyE,QAAA,gBACVvD,OAAA,CAACtB,UAAU;cAACuF,OAAO,EAAC,IAAI;cAACO,YAAY;cAACZ,KAAK,EAAC,SAAS;cAAAL,QAAA,EAClDjD,eAAe,CAACoB,IAAI,CAACE;YAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACbhE,OAAA,CAACtB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,gBAAgB;cAACY,YAAY;cAAAjB,QAAA,EAC5DjD,eAAe,CAACoB,IAAI,CAACI,OAAO,CAACmB,IAAI,CAAC,IAAI;YAAC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbhE,OAAA,CAACrB,GAAG;cAAC0E,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEG,GAAG,EAAE,CAAC;gBAAEc,EAAE,EAAE;cAAE,CAAE;cAAAlB,QAAA,GACzCjD,eAAe,CAACoB,IAAI,CAACM,SAAS,iBAC7BhC,OAAA,CAACpB,IAAI;gBAAC8F,KAAK,EAAEpE,eAAe,CAACoB,IAAI,CAACM,SAAU;gBAACmC,IAAI,EAAC,OAAO;gBAACF,OAAO,EAAC;cAAU;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC/E,EACA1D,eAAe,CAACoB,IAAI,CAACO,gBAAgB,iBACpCjC,OAAA,CAACpB,IAAI;gBAAC8F,KAAK,EAAEpE,eAAe,CAACoB,IAAI,CAACO,gBAAiB;gBAACkC,IAAI,EAAC,OAAO;gBAACF,OAAO,EAAC;cAAU;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACtF,eACDhE,OAAA,CAACpB,IAAI;gBACH8F,KAAK,EAAE,QAAQpE,eAAe,CAACe,KAAK,CAACE,WAAW,EAAG;gBACnD4C,IAAI,EAAC,OAAO;gBACZP,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPhE,OAAA,CAACrB,GAAG;UAAA4E,QAAA,gBACFvD,OAAA,CAACtB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAACO,YAAY;YAAAjB,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhE,OAAA,CAACrB,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE,CAAC;cAAEgB,EAAE,EAAE;YAAE,CAAE;YAAApB,QAAA,gBAC1CvD,OAAA,CAACpB,IAAI;cACHgG,IAAI,eAAE5E,OAAA,CAACL,QAAQ;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBU,KAAK,EAAE,GAAG,CAACpE,eAAe,CAACJ,QAAQ,CAACiB,eAAe,GAAG,GAAG,EAAE0D,OAAO,CAAC,CAAC,CAAC,aAAc;cACnFjB,KAAK,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACFhE,OAAA,CAACpB,IAAI;cACH8F,KAAK,EAAE,aAAapE,eAAe,CAACJ,QAAQ,CAACkB,cAAc,EAAG;cAC9D6C,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACD1D,eAAe,CAACe,KAAK,CAACG,aAAa,iBAClCxB,OAAA,CAACpB,IAAI;cACH8F,KAAK,EAAEpE,eAAe,CAACe,KAAK,CAACG,aAAc;cAC3CyC,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhE,OAAA,CAACrB,GAAG;UAAA4E,QAAA,gBACFvD,OAAA,CAACrB,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAApB,QAAA,gBACzFvD,OAAA,CAACtB,UAAU;cAACuF,OAAO,EAAC,IAAI;cAAAV,QAAA,EAAC;YAEzB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhE,OAAA,CAAChB,OAAO;cAAC4C,KAAK,EAAC,WAAW;cAAA2B,QAAA,eACxBvD,OAAA,CAACjB,UAAU;gBACToF,IAAI,EAAC,OAAO;gBACZD,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACrC,eAAe,CAACe,KAAK,CAACC,OAAO,CAAE;gBAAAiC,QAAA,eAE7DvD,OAAA,CAACH,QAAQ;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACNhE,OAAA,CAACb,KAAK;YAACkE,EAAE,EAAE;cAAEyB,CAAC,EAAE,CAAC;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAxB,QAAA,eAC9CvD,OAAA,CAACtB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACZ,EAAE,EAAE;gBAAE2B,UAAU,EAAE;cAAI,CAAE;cAAAzB,QAAA,EACjDjD,eAAe,CAACe,KAAK,CAACC;YAAO;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EAGL1D,eAAe,CAAC4B,kBAAkB,CAAC+C,MAAM,GAAG,CAAC,iBAC5CjF,OAAA,CAACrB,GAAG;UAAA4E,QAAA,gBACFvD,OAAA,CAACtB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAACO,YAAY;YAAAjB,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhE,OAAA,CAACrB,GAAG;YAAC0E,EAAE,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEe,aAAa,EAAE,QAAQ;cAAEZ,GAAG,EAAE;YAAE,CAAE;YAAAJ,QAAA,EAC3DjD,eAAe,CAAC4B,kBAAkB,CAACgD,GAAG,CAAC,CAAC7D,KAAK,EAAE8D,KAAK,kBACnDnF,OAAA,CAACnB,IAAI;cAAaoF,OAAO,EAAC,UAAU;cAACZ,EAAE,EAAE;gBAAE0B,eAAe,EAAE;cAAU,CAAE;cAAAxB,QAAA,eACtEvD,OAAA,CAAClB,WAAW;gBAACuE,EAAE,EAAE;kBAAEgB,EAAE,EAAE;gBAAE,CAAE;gBAAAd,QAAA,gBACzBvD,OAAA,CAACtB,UAAU;kBAACuF,OAAO,EAAC,OAAO;kBAACL,KAAK,EAAC,gBAAgB;kBAACY,YAAY;kBAAAjB,QAAA,GAAC,gBAChD,EAAClC,KAAK,CAACc,WAAW;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACbhE,OAAA,CAACtB,UAAU;kBAACuF,OAAO,EAAC,OAAO;kBAAAV,QAAA,EACxBlC,KAAK,CAACC;gBAAO;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GARLmB,KAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDhE,OAAA,CAACrB,GAAG;UAAA4E,QAAA,gBACFvD,OAAA,CAACtB,UAAU;YAACuF,OAAO,EAAC,IAAI;YAACO,YAAY;YAAAjB,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhE,OAAA,CAACb,KAAK;YAACkE,EAAE,EAAE;cAAEyB,CAAC,EAAE,CAAC;cAAEC,eAAe,EAAE;YAAa,CAAE;YAAAxB,QAAA,eACjDvD,OAAA,CAACrB,GAAG;cAAC0E,EAAE,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAa,CAAE;cAAAH,QAAA,gBACtFvD,OAAA,CAACtB,UAAU;gBAACuF,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAE+B,SAAS,EAAE,QAAQ;kBAAEC,IAAI,EAAE;gBAAE,CAAE;gBAAA9B,QAAA,EAC9DP,cAAc,CAAC;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACbhE,OAAA,CAAChB,OAAO;gBAAC4C,KAAK,EAAC,eAAe;gBAAA2B,QAAA,eAC5BvD,OAAA,CAACjB,UAAU;kBACToF,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACK,cAAc,CAAC,CAAC,CAAE;kBAAAO,QAAA,eAEhDvD,OAAA,CAACH,QAAQ;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEhBhE,OAAA,CAACxB,aAAa;MAAA+E,QAAA,gBACZvD,OAAA,CAACvB,MAAM;QAACyF,OAAO,EAAE9D,OAAQ;QAAAmD,QAAA,EAAC;MAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACvC1D,eAAe,iBACdN,OAAA,CAACvB,MAAM;QACLwF,OAAO,EAAC,WAAW;QACnBqB,SAAS,eAAEtF,OAAA,CAACP,QAAQ;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBE,OAAO,EAAEA,CAAA,KAAM;UACb;UACAtD,WAAW,CAAC,gCAAgC,CAAC;QAC/C,CAAE;QAAA2C,QAAA,EACH;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC3D,EAAA,CApPIJ,cAA6C;EAAA,QAIdH,eAAe;AAAA;AAAAyF,EAAA,GAJ9CtF,cAA6C;AAsPnD,eAAeA,cAAc;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}