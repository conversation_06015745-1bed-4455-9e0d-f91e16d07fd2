{"ast": null, "code": "/**\n * Authentication service for API calls\n */\nimport { apiClient } from './apiClient';\nclass AuthService {\n  setToken(token) {\n    apiClient.setAuthToken(token);\n  }\n  clearToken() {\n    apiClient.clearAuthToken();\n  }\n  async login(credentials) {\n    const formData = new FormData();\n    formData.append('username', credentials.email);\n    formData.append('password', credentials.password);\n    return apiClient.post('/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      },\n      skipAuth: true\n    });\n  }\n  async signup(userData) {\n    const response = await this.api.post('/auth/signup', userData);\n    return response.data;\n  }\n  async logout() {\n    try {\n      await this.api.post('/auth/logout');\n    } catch (error) {\n      // Ignore logout errors\n      console.warn('Logout request failed:', error);\n    } finally {\n      this.clearToken();\n    }\n  }\n  async getCurrentUser() {\n    const response = await this.api.get('/users/me');\n    return response.data;\n  }\n  async updateProfile(userData) {\n    const response = await this.api.put('/users/me', userData);\n    return response.data;\n  }\n  async changePassword(currentPassword, newPassword) {\n    const response = await this.api.post('/users/me/change-password', {\n      current_password: currentPassword,\n      new_password: newPassword\n    });\n    return response.data;\n  }\n  async updatePreferences(preferences) {\n    const response = await this.api.put('/users/me/preferences', preferences);\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck() {\n    const response = await this.api.get('/health');\n    return response.data;\n  }\n}\nexport const authService = new AuthService();", "map": {"version": 3, "names": ["apiClient", "AuthService", "setToken", "token", "setAuthToken", "clearToken", "clearAuthToken", "login", "credentials", "formData", "FormData", "append", "email", "password", "post", "headers", "<PERSON><PERSON><PERSON>", "signup", "userData", "response", "api", "data", "logout", "error", "console", "warn", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "updatePreferences", "preferences", "healthCheck", "authService"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/services/authService.ts"], "sourcesContent": ["/**\n * Authentication service for API calls\n */\nimport { apiClient } from './apiClient';\nimport { User, AuthResponse, LoginRequest, SignupRequest, ApiResponse } from '../types';\n\nclass AuthService {\n  setToken(token: string) {\n    apiClient.setAuthToken(token);\n  }\n\n  clearToken() {\n    apiClient.clearAuthToken();\n  }\n\n  async login(credentials: LoginRequest): Promise<AuthResponse> {\n    const formData = new FormData();\n    formData.append('username', credentials.email);\n    formData.append('password', credentials.password);\n\n    return apiClient.post<AuthResponse>('/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n      skipAuth: true,\n    });\n  }\n\n  async signup(userData: SignupRequest): Promise<ApiResponse> {\n    const response = await this.api.post('/auth/signup', userData);\n    return response.data;\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.api.post('/auth/logout');\n    } catch (error) {\n      // Ignore logout errors\n      console.warn('Logout request failed:', error);\n    } finally {\n      this.clearToken();\n    }\n  }\n\n  async getCurrentUser(): Promise<User> {\n    const response = await this.api.get('/users/me');\n    return response.data;\n  }\n\n  async updateProfile(userData: Partial<User>): Promise<User> {\n    const response = await this.api.put('/users/me', userData);\n    return response.data;\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {\n    const response = await this.api.post('/users/me/change-password', {\n      current_password: currentPassword,\n      new_password: newPassword,\n    });\n    return response.data;\n  }\n\n  async updatePreferences(preferences: Record<string, any>): Promise<ApiResponse> {\n    const response = await this.api.put('/users/me/preferences', preferences);\n    return response.data;\n  }\n\n  // Health check\n  async healthCheck(): Promise<any> {\n    const response = await this.api.get('/health');\n    return response.data;\n  }\n}\n\nexport const authService = new AuthService();\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,aAAa;AAGvC,MAAMC,WAAW,CAAC;EAChBC,QAAQA,CAACC,KAAa,EAAE;IACtBH,SAAS,CAACI,YAAY,CAACD,KAAK,CAAC;EAC/B;EAEAE,UAAUA,CAAA,EAAG;IACXL,SAAS,CAACM,cAAc,CAAC,CAAC;EAC5B;EAEA,MAAMC,KAAKA,CAACC,WAAyB,EAAyB;IAC5D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACI,KAAK,CAAC;IAC9CH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,WAAW,CAACK,QAAQ,CAAC;IAEjD,OAAOb,SAAS,CAACc,IAAI,CAAe,aAAa,EAAEL,QAAQ,EAAE;MAC3DM,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,MAAMC,MAAMA,CAACC,QAAuB,EAAwB;IAC1D,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,cAAc,EAAEI,QAAQ,CAAC;IAC9D,OAAOC,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAM,IAAI,CAACF,GAAG,CAACN,IAAI,CAAC,cAAc,CAAC;IACrC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACd;MACAC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEF,KAAK,CAAC;IAC/C,CAAC,SAAS;MACR,IAAI,CAAClB,UAAU,CAAC,CAAC;IACnB;EACF;EAEA,MAAMqB,cAAcA,CAAA,EAAkB;IACpC,MAAMP,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACO,GAAG,CAAC,WAAW,CAAC;IAChD,OAAOR,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMO,aAAaA,CAACV,QAAuB,EAAiB;IAC1D,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACS,GAAG,CAAC,WAAW,EAAEX,QAAQ,CAAC;IAC1D,OAAOC,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMS,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAwB;IACvF,MAAMb,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,2BAA2B,EAAE;MAChEmB,gBAAgB,EAAEF,eAAe;MACjCG,YAAY,EAAEF;IAChB,CAAC,CAAC;IACF,OAAOb,QAAQ,CAACE,IAAI;EACtB;EAEA,MAAMc,iBAAiBA,CAACC,WAAgC,EAAwB;IAC9E,MAAMjB,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACS,GAAG,CAAC,uBAAuB,EAAEO,WAAW,CAAC;IACzE,OAAOjB,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMgB,WAAWA,CAAA,EAAiB;IAChC,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACC,GAAG,CAACO,GAAG,CAAC,SAAS,CAAC;IAC9C,OAAOR,QAAQ,CAACE,IAAI;EACtB;AACF;AAEA,OAAO,MAAMiB,WAAW,GAAG,IAAIrC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}