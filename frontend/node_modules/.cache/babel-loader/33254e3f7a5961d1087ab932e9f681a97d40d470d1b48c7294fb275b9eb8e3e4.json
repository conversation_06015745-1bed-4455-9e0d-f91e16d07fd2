{"ast": null, "code": "export * from \"./enums.js\";\nexport * from \"./modifiers/index.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { popperGenerator, detectOverflow, createPopper as createPopperBase } from \"./createPopper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper } from \"./popper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\";", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "createPopper", "createPopperBase", "createPopperLite"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@popperjs/core/lib/index.js"], "sourcesContent": ["export * from \"./enums.js\";\nexport * from \"./modifiers/index.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { popperGenerator, detectOverflow, createPopper as createPopperBase } from \"./createPopper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper } from \"./popper.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\";"], "mappings": "AAAA,cAAc,YAAY;AAC1B,cAAc,sBAAsB,CAAC,CAAC;;AAEtC,SAASA,eAAe,EAAEC,cAAc,EAAEC,YAAY,IAAIC,gBAAgB,QAAQ,mBAAmB,CAAC,CAAC;;AAEvG,SAASD,YAAY,QAAQ,aAAa,CAAC,CAAC;;AAE5C,SAASA,YAAY,IAAIE,gBAAgB,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}