{"ast": null, "code": "export { default } from \"./Container.js\";\nexport { default as containerClasses } from \"./containerClasses.js\";\nexport * from \"./containerClasses.js\";", "map": {"version": 3, "names": ["default", "containerClasses"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/node_modules/@mui/system/esm/Container/index.js"], "sourcesContent": ["export { default } from \"./Container.js\";\nexport { default as containerClasses } from \"./containerClasses.js\";\nexport * from \"./containerClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,uBAAuB;AACnE,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}