{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, TextField, Button, Grid2 as Grid, Alert, LinearProgress, Chip, IconButton, Paper, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { CloudUpload as UploadIcon, Delete as DeleteIcon, InsertDriveFile as FileIcon, ArrowBack as BackIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { adminService } from '../../services/adminService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookUpload = () => {\n  _s();\n  const navigate = useNavigate();\n  const [files, setFiles] = useState([]);\n  const [formData, setFormData] = useState({\n    title: '',\n    authors: '',\n    isbn: '',\n    publisher: '',\n    publication_year: undefined,\n    edition: '',\n    description: '',\n    tags: '',\n    language: 'en'\n  });\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const handleFileSelect = event => {\n    const selectedFiles = Array.from(event.target.files || []);\n    const newFiles = selectedFiles.filter(file => file.type === 'application/pdf').map(file => ({\n      file,\n      id: Math.random().toString(36).substr(2, 9),\n      progress: 0,\n      status: 'pending'\n    }));\n    if (selectedFiles.length > newFiles.length) {\n      setError('Only PDF files are allowed');\n    } else {\n      setError(null);\n    }\n    setFiles(prev => [...prev, ...newFiles]);\n  };\n  const handleFileRemove = fileId => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n  };\n  const handleInputChange = field => event => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: field === 'publication_year' ? value ? parseInt(value) : undefined : value\n    }));\n  };\n  const validateForm = () => {\n    if (!formData.title.trim()) {\n      setError('Title is required');\n      return false;\n    }\n    if (!formData.authors.trim()) {\n      setError('Authors are required');\n      return false;\n    }\n    if (files.length === 0) {\n      setError('At least one PDF file is required');\n      return false;\n    }\n    return true;\n  };\n  const handleUpload = async () => {\n    if (!validateForm()) return;\n    setUploading(true);\n    setError(null);\n    setSuccess(null);\n    try {\n      for (const fileItem of files) {\n        if (fileItem.status === 'completed') continue;\n\n        // Update file status to uploading\n        setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n          ...f,\n          status: 'uploading',\n          progress: 0\n        } : f));\n        try {\n          // Simulate progress updates\n          const progressInterval = setInterval(() => {\n            setFiles(prev => prev.map(f => f.id === fileItem.id && f.progress < 90 ? {\n              ...f,\n              progress: f.progress + 10\n            } : f));\n          }, 200);\n          const result = await adminService.uploadBook(fileItem.file, formData);\n          clearInterval(progressInterval);\n\n          // Update file status to completed\n          setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n            ...f,\n            status: 'completed',\n            progress: 100\n          } : f));\n          setSuccess(`Book \"${formData.title}\" uploaded successfully!`);\n        } catch (err) {\n          // Update file status to error\n          setFiles(prev => prev.map(f => f.id === fileItem.id ? {\n            ...f,\n            status: 'error',\n            error: err.message\n          } : f));\n          throw err;\n        }\n      }\n\n      // Reset form after successful upload\n      setTimeout(() => {\n        setFormData({\n          title: '',\n          authors: '',\n          isbn: '',\n          publisher: '',\n          publication_year: undefined,\n          edition: '',\n          description: '',\n          tags: '',\n          language: 'en'\n        });\n        setFiles([]);\n      }, 2000);\n    } catch (err) {\n      setError(err.message || 'Upload failed');\n    } finally {\n      setUploading(false);\n    }\n  };\n  const getFileStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'uploading':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const formatFileSize = bytes => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: () => navigate('/admin/books'),\n        sx: {\n          mr: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(BackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: \"Upload Medical Book\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setError(null),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 3\n      },\n      onClose: () => setSuccess(null),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Upload PDF Files\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                border: '2px dashed',\n                borderColor: 'primary.main',\n                borderRadius: 2,\n                p: 3,\n                textAlign: 'center',\n                cursor: 'pointer',\n                '&:hover': {\n                  backgroundColor: 'action.hover'\n                }\n              },\n              onClick: () => {\n                var _document$getElementB;\n                return (_document$getElementB = document.getElementById('file-input')) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n              },\n              children: [/*#__PURE__*/_jsxDEV(UploadIcon, {\n                sx: {\n                  fontSize: 48,\n                  color: 'primary.main',\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Drop PDF files here or click to browse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                children: \"Only PDF files are supported\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"file-input\",\n                type: \"file\",\n                multiple: true,\n                accept: \".pdf\",\n                style: {\n                  display: 'none'\n                },\n                onChange: handleFileSelect\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: [\"Selected Files (\", files.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: files.map(fileItem => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  children: [/*#__PURE__*/_jsxDEV(FileIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'primary.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: fileItem.file.name,\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        children: formatFileSize(fileItem.file.size)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1,\n                          mt: 0.5\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          label: fileItem.status,\n                          size: \"small\",\n                          color: getFileStatusColor(fileItem.status)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 292,\n                          columnNumber: 33\n                        }, this), fileItem.status === 'uploading' && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                          variant: \"determinate\",\n                          value: fileItem.progress,\n                          sx: {\n                            flexGrow: 1,\n                            height: 4\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 298,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 31\n                      }, this), fileItem.error && /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"error\",\n                        children: fileItem.error\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      edge: \"end\",\n                      onClick: () => handleFileRemove(fileItem.id),\n                      disabled: fileItem.status === 'uploading',\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this)]\n                }, fileItem.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Book Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Title *\",\n                  value: formData.title,\n                  onChange: handleInputChange('title'),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Authors *\",\n                  value: formData.authors,\n                  onChange: handleInputChange('authors'),\n                  placeholder: \"Comma-separated list of authors\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"ISBN\",\n                  value: formData.isbn,\n                  onChange: handleInputChange('isbn')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Publication Year\",\n                  type: \"number\",\n                  value: formData.publication_year || '',\n                  onChange: handleInputChange('publication_year')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Publisher\",\n                  value: formData.publisher,\n                  onChange: handleInputChange('publisher')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Edition\",\n                  value: formData.edition,\n                  onChange: handleInputChange('edition')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Tags\",\n                  value: formData.tags,\n                  onChange: handleInputChange('tags'),\n                  placeholder: \"Comma-separated tags (e.g., anatomy, cardiology, textbook)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Description\",\n                  value: formData.description,\n                  onChange: handleInputChange('description'),\n                  multiline: true,\n                  rows: 3,\n                  placeholder: \"Brief description of the book content\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 3,\n                display: 'flex',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                onClick: handleUpload,\n                disabled: uploading || files.length === 0,\n                startIcon: /*#__PURE__*/_jsxDEV(UploadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 30\n                }, this),\n                fullWidth: true,\n                children: uploading ? 'Uploading...' : 'Upload Book'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => navigate('/admin/books'),\n                disabled: uploading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(BookUpload, \"xmIFP2/tYtxQcKaPKy1Yy2oX84k=\", false, function () {\n  return [useNavigate];\n});\n_c = BookUpload;\nexport default BookUpload;\nvar _c;\n$RefreshReg$(_c, \"BookUpload\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Grid2", "Grid", "<PERSON><PERSON>", "LinearProgress", "Chip", "IconButton", "Paper", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "CloudUpload", "UploadIcon", "Delete", "DeleteIcon", "InsertDriveFile", "FileIcon", "ArrowBack", "BackIcon", "useNavigate", "adminService", "jsxDEV", "_jsxDEV", "BookUpload", "_s", "navigate", "files", "setFiles", "formData", "setFormData", "title", "authors", "isbn", "publisher", "publication_year", "undefined", "edition", "description", "tags", "language", "uploading", "setUploading", "error", "setError", "success", "setSuccess", "handleFileSelect", "event", "selectedFiles", "Array", "from", "target", "newFiles", "filter", "file", "type", "map", "id", "Math", "random", "toString", "substr", "progress", "status", "length", "prev", "handleFileRemove", "fileId", "f", "handleInputChange", "field", "value", "parseInt", "validateForm", "trim", "handleUpload", "fileItem", "progressInterval", "setInterval", "result", "uploadBook", "clearInterval", "err", "message", "setTimeout", "getFileStatusColor", "formatFileSize", "bytes", "sizes", "i", "floor", "log", "round", "pow", "children", "sx", "display", "alignItems", "mb", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "severity", "onClose", "container", "spacing", "xs", "md", "gutterBottom", "border", "borderColor", "borderRadius", "p", "textAlign", "cursor", "backgroundColor", "_document$getElementB", "document", "getElementById", "click", "fontSize", "color", "multiple", "accept", "style", "onChange", "mt", "dense", "divider", "primary", "name", "secondary", "size", "gap", "label", "flexGrow", "height", "edge", "disabled", "fullWidth", "required", "placeholder", "sm", "multiline", "rows", "startIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MedPrep/frontend/src/pages/admin/BookUpload.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Grid2 as <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  LinearProgress,\n  Chip,\n  IconButton,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n} from '@mui/material';\nimport {\n  CloudUpload as UploadIcon,\n  Delete as DeleteIcon,\n  InsertDriveFile as FileIcon,\n  ArrowBack as BackIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { adminService } from '../../services/adminService';\n\ninterface BookUploadData {\n  title: string;\n  authors: string;\n  isbn?: string;\n  publisher?: string;\n  publication_year?: number;\n  edition?: string;\n  description?: string;\n  tags?: string;\n  language: string;\n}\n\ninterface UploadFile {\n  file: File;\n  id: string;\n  progress: number;\n  status: 'pending' | 'uploading' | 'completed' | 'error';\n  error?: string;\n}\n\nconst BookUpload: React.FC = () => {\n  const navigate = useNavigate();\n  const [files, setFiles] = useState<UploadFile[]>([]);\n  const [formData, setFormData] = useState<BookUploadData>({\n    title: '',\n    authors: '',\n    isbn: '',\n    publisher: '',\n    publication_year: undefined,\n    edition: '',\n    description: '',\n    tags: '',\n    language: 'en',\n  });\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFiles = Array.from(event.target.files || []);\n    const newFiles: UploadFile[] = selectedFiles\n      .filter(file => file.type === 'application/pdf')\n      .map(file => ({\n        file,\n        id: Math.random().toString(36).substr(2, 9),\n        progress: 0,\n        status: 'pending',\n      }));\n\n    if (selectedFiles.length > newFiles.length) {\n      setError('Only PDF files are allowed');\n    } else {\n      setError(null);\n    }\n\n    setFiles(prev => [...prev, ...newFiles]);\n  };\n\n  const handleFileRemove = (fileId: string) => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n  };\n\n  const handleInputChange = (field: keyof BookUploadData) => (\n    event: React.ChangeEvent<HTMLInputElement>\n  ) => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: field === 'publication_year' ? (value ? parseInt(value) : undefined) : value,\n    }));\n  };\n\n  const validateForm = (): boolean => {\n    if (!formData.title.trim()) {\n      setError('Title is required');\n      return false;\n    }\n    if (!formData.authors.trim()) {\n      setError('Authors are required');\n      return false;\n    }\n    if (files.length === 0) {\n      setError('At least one PDF file is required');\n      return false;\n    }\n    return true;\n  };\n\n  const handleUpload = async () => {\n    if (!validateForm()) return;\n\n    setUploading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      for (const fileItem of files) {\n        if (fileItem.status === 'completed') continue;\n\n        // Update file status to uploading\n        setFiles(prev => prev.map(f => \n          f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f\n        ));\n\n        try {\n          // Simulate progress updates\n          const progressInterval = setInterval(() => {\n            setFiles(prev => prev.map(f => \n              f.id === fileItem.id && f.progress < 90 \n                ? { ...f, progress: f.progress + 10 } \n                : f\n            ));\n          }, 200);\n\n          const result = await adminService.uploadBook(fileItem.file, formData);\n\n          clearInterval(progressInterval);\n\n          // Update file status to completed\n          setFiles(prev => prev.map(f => \n            f.id === fileItem.id \n              ? { ...f, status: 'completed', progress: 100 } \n              : f\n          ));\n\n          setSuccess(`Book \"${formData.title}\" uploaded successfully!`);\n        } catch (err: any) {\n          // Update file status to error\n          setFiles(prev => prev.map(f => \n            f.id === fileItem.id \n              ? { ...f, status: 'error', error: err.message } \n              : f\n          ));\n          throw err;\n        }\n      }\n\n      // Reset form after successful upload\n      setTimeout(() => {\n        setFormData({\n          title: '',\n          authors: '',\n          isbn: '',\n          publisher: '',\n          publication_year: undefined,\n          edition: '',\n          description: '',\n          tags: '',\n          language: 'en',\n        });\n        setFiles([]);\n      }, 2000);\n\n    } catch (err: any) {\n      setError(err.message || 'Upload failed');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const getFileStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'uploading':\n        return 'warning';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <IconButton onClick={() => navigate('/admin/books')} sx={{ mr: 1 }}>\n          <BackIcon />\n        </IconButton>\n        <Typography variant=\"h4\">\n          Upload Medical Book\n        </Typography>\n      </Box>\n\n      {/* Alerts */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }} onClose={() => setError(null)}>\n          {error}\n        </Alert>\n      )}\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>\n          {success}\n        </Alert>\n      )}\n\n      <Grid container spacing={3}>\n        {/* File Upload Section */}\n        <Grid xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Upload PDF Files\n              </Typography>\n\n              {/* File Drop Zone */}\n              <Paper\n                sx={{\n                  border: '2px dashed',\n                  borderColor: 'primary.main',\n                  borderRadius: 2,\n                  p: 3,\n                  textAlign: 'center',\n                  cursor: 'pointer',\n                  '&:hover': {\n                    backgroundColor: 'action.hover',\n                  },\n                }}\n                onClick={() => document.getElementById('file-input')?.click()}\n              >\n                <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />\n                <Typography variant=\"h6\" gutterBottom>\n                  Drop PDF files here or click to browse\n                </Typography>\n                <Typography variant=\"body2\" color=\"textSecondary\">\n                  Only PDF files are supported\n                </Typography>\n                <input\n                  id=\"file-input\"\n                  type=\"file\"\n                  multiple\n                  accept=\".pdf\"\n                  style={{ display: 'none' }}\n                  onChange={handleFileSelect}\n                />\n              </Paper>\n\n              {/* File List */}\n              {files.length > 0 && (\n                <Box sx={{ mt: 2 }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Selected Files ({files.length})\n                  </Typography>\n                  <List dense>\n                    {files.map((fileItem) => (\n                      <ListItem key={fileItem.id} divider>\n                        <FileIcon sx={{ mr: 1, color: 'primary.main' }} />\n                        <ListItemText\n                          primary={fileItem.file.name}\n                          secondary={\n                            <Box>\n                              <Typography variant=\"caption\" display=\"block\">\n                                {formatFileSize(fileItem.file.size)}\n                              </Typography>\n                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>\n                                <Chip\n                                  label={fileItem.status}\n                                  size=\"small\"\n                                  color={getFileStatusColor(fileItem.status) as any}\n                                />\n                                {fileItem.status === 'uploading' && (\n                                  <LinearProgress\n                                    variant=\"determinate\"\n                                    value={fileItem.progress}\n                                    sx={{ flexGrow: 1, height: 4 }}\n                                  />\n                                )}\n                              </Box>\n                              {fileItem.error && (\n                                <Typography variant=\"caption\" color=\"error\">\n                                  {fileItem.error}\n                                </Typography>\n                              )}\n                            </Box>\n                          }\n                        />\n                        <ListItemSecondaryAction>\n                          <IconButton\n                            edge=\"end\"\n                            onClick={() => handleFileRemove(fileItem.id)}\n                            disabled={fileItem.status === 'uploading'}\n                          >\n                            <DeleteIcon />\n                          </IconButton>\n                        </ListItemSecondaryAction>\n                      </ListItem>\n                    ))}\n                  </List>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Book Information Form */}\n        <Grid xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                Book Information\n              </Typography>\n\n              <Grid container spacing={2}>\n                <Grid xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Title *\"\n                    value={formData.title}\n                    onChange={handleInputChange('title')}\n                    required\n                  />\n                </Grid>\n                <Grid xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Authors *\"\n                    value={formData.authors}\n                    onChange={handleInputChange('authors')}\n                    placeholder=\"Comma-separated list of authors\"\n                    required\n                  />\n                </Grid>\n                <Grid xs={12} sm={6}>\n                  <TextField\n                    fullWidth\n                    label=\"ISBN\"\n                    value={formData.isbn}\n                    onChange={handleInputChange('isbn')}\n                  />\n                </Grid>\n                <Grid xs={12} sm={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Publication Year\"\n                    type=\"number\"\n                    value={formData.publication_year || ''}\n                    onChange={handleInputChange('publication_year')}\n                  />\n                </Grid>\n                <Grid xs={12} sm={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Publisher\"\n                    value={formData.publisher}\n                    onChange={handleInputChange('publisher')}\n                  />\n                </Grid>\n                <Grid xs={12} sm={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Edition\"\n                    value={formData.edition}\n                    onChange={handleInputChange('edition')}\n                  />\n                </Grid>\n                <Grid xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Tags\"\n                    value={formData.tags}\n                    onChange={handleInputChange('tags')}\n                    placeholder=\"Comma-separated tags (e.g., anatomy, cardiology, textbook)\"\n                  />\n                </Grid>\n                <Grid xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Description\"\n                    value={formData.description}\n                    onChange={handleInputChange('description')}\n                    multiline\n                    rows={3}\n                    placeholder=\"Brief description of the book content\"\n                  />\n                </Grid>\n              </Grid>\n\n              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>\n                <Button\n                  variant=\"contained\"\n                  onClick={handleUpload}\n                  disabled={uploading || files.length === 0}\n                  startIcon={<UploadIcon />}\n                  fullWidth\n                >\n                  {uploading ? 'Uploading...' : 'Upload Book'}\n                </Button>\n                <Button\n                  variant=\"outlined\"\n                  onClick={() => navigate('/admin/books')}\n                  disabled={uploading}\n                >\n                  Cancel\n                </Button>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BookUpload;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,KAAK,IAAIC,IAAI,EACbC,KAAK,EACLC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,eAAe,IAAIC,QAAQ,EAC3BC,SAAS,IAAIC,QAAQ,QAChB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB3D,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAe,EAAE,CAAC;EACpD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAiB;IACvDqC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,EAAE;IACbC,gBAAgB,EAAEC,SAAS;IAC3BC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAgB,IAAI,CAAC;EAE3D,MAAMqD,gBAAgB,GAAIC,KAA0C,IAAK;IACvE,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACH,KAAK,CAACI,MAAM,CAACzB,KAAK,IAAI,EAAE,CAAC;IAC1D,MAAM0B,QAAsB,GAAGJ,aAAa,CACzCK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,CAAC,CAC/CC,GAAG,CAACF,IAAI,KAAK;MACZA,IAAI;MACJG,EAAE,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3CC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IAEL,IAAIf,aAAa,CAACgB,MAAM,GAAGZ,QAAQ,CAACY,MAAM,EAAE;MAC1CrB,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,MAAM;MACLA,QAAQ,CAAC,IAAI,CAAC;IAChB;IAEAhB,QAAQ,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGb,QAAQ,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMc,gBAAgB,GAAIC,MAAc,IAAK;IAC3CxC,QAAQ,CAACsC,IAAI,IAAIA,IAAI,CAACZ,MAAM,CAACe,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKU,MAAM,CAAC,CAAC;EACrD,CAAC;EAED,MAAME,iBAAiB,GAAIC,KAA2B,IACpDvB,KAA0C,IACvC;IACH,MAAMwB,KAAK,GAAGxB,KAAK,CAACI,MAAM,CAACoB,KAAK;IAChC1C,WAAW,CAACoC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACK,KAAK,GAAGA,KAAK,KAAK,kBAAkB,GAAIC,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,GAAGpC,SAAS,GAAIoC;IAClF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAe;IAClC,IAAI,CAAC7C,QAAQ,CAACE,KAAK,CAAC4C,IAAI,CAAC,CAAC,EAAE;MAC1B/B,QAAQ,CAAC,mBAAmB,CAAC;MAC7B,OAAO,KAAK;IACd;IACA,IAAI,CAACf,QAAQ,CAACG,OAAO,CAAC2C,IAAI,CAAC,CAAC,EAAE;MAC5B/B,QAAQ,CAAC,sBAAsB,CAAC;MAChC,OAAO,KAAK;IACd;IACA,IAAIjB,KAAK,CAACsC,MAAM,KAAK,CAAC,EAAE;MACtBrB,QAAQ,CAAC,mCAAmC,CAAC;MAC7C,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMgC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACF,YAAY,CAAC,CAAC,EAAE;IAErBhC,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,KAAK,MAAM+B,QAAQ,IAAIlD,KAAK,EAAE;QAC5B,IAAIkD,QAAQ,CAACb,MAAM,KAAK,WAAW,EAAE;;QAErC;QACApC,QAAQ,CAACsC,IAAI,IAAIA,IAAI,CAACT,GAAG,CAACY,CAAC,IACzBA,CAAC,CAACX,EAAE,KAAKmB,QAAQ,CAACnB,EAAE,GAAG;UAAE,GAAGW,CAAC;UAAEL,MAAM,EAAE,WAAW;UAAED,QAAQ,EAAE;QAAE,CAAC,GAAGM,CACtE,CAAC,CAAC;QAEF,IAAI;UACF;UACA,MAAMS,gBAAgB,GAAGC,WAAW,CAAC,MAAM;YACzCnD,QAAQ,CAACsC,IAAI,IAAIA,IAAI,CAACT,GAAG,CAACY,CAAC,IACzBA,CAAC,CAACX,EAAE,KAAKmB,QAAQ,CAACnB,EAAE,IAAIW,CAAC,CAACN,QAAQ,GAAG,EAAE,GACnC;cAAE,GAAGM,CAAC;cAAEN,QAAQ,EAAEM,CAAC,CAACN,QAAQ,GAAG;YAAG,CAAC,GACnCM,CACN,CAAC,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;UAEP,MAAMW,MAAM,GAAG,MAAM3D,YAAY,CAAC4D,UAAU,CAACJ,QAAQ,CAACtB,IAAI,EAAE1B,QAAQ,CAAC;UAErEqD,aAAa,CAACJ,gBAAgB,CAAC;;UAE/B;UACAlD,QAAQ,CAACsC,IAAI,IAAIA,IAAI,CAACT,GAAG,CAACY,CAAC,IACzBA,CAAC,CAACX,EAAE,KAAKmB,QAAQ,CAACnB,EAAE,GAChB;YAAE,GAAGW,CAAC;YAAEL,MAAM,EAAE,WAAW;YAAED,QAAQ,EAAE;UAAI,CAAC,GAC5CM,CACN,CAAC,CAAC;UAEFvB,UAAU,CAAC,SAASjB,QAAQ,CAACE,KAAK,0BAA0B,CAAC;QAC/D,CAAC,CAAC,OAAOoD,GAAQ,EAAE;UACjB;UACAvD,QAAQ,CAACsC,IAAI,IAAIA,IAAI,CAACT,GAAG,CAACY,CAAC,IACzBA,CAAC,CAACX,EAAE,KAAKmB,QAAQ,CAACnB,EAAE,GAChB;YAAE,GAAGW,CAAC;YAAEL,MAAM,EAAE,OAAO;YAAErB,KAAK,EAAEwC,GAAG,CAACC;UAAQ,CAAC,GAC7Cf,CACN,CAAC,CAAC;UACF,MAAMc,GAAG;QACX;MACF;;MAEA;MACAE,UAAU,CAAC,MAAM;QACfvD,WAAW,CAAC;UACVC,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,IAAI,EAAE,EAAE;UACRC,SAAS,EAAE,EAAE;UACbC,gBAAgB,EAAEC,SAAS;UAC3BC,OAAO,EAAE,EAAE;UACXC,WAAW,EAAE,EAAE;UACfC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFZ,QAAQ,CAAC,EAAE,CAAC;MACd,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOuD,GAAQ,EAAE;MACjBvC,QAAQ,CAACuC,GAAG,CAACC,OAAO,IAAI,eAAe,CAAC;IAC1C,CAAC,SAAS;MACR1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4C,kBAAkB,GAAItB,MAAc,IAAK;IAC7C,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMuB,cAAc,GAAIC,KAAa,IAAK;IACxC,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,IAAID,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAME,CAAC,GAAG/B,IAAI,CAACgC,KAAK,CAAChC,IAAI,CAACiC,GAAG,CAACJ,KAAK,CAAC,GAAG7B,IAAI,CAACiC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,OAAOjC,IAAI,CAACkC,KAAK,CAACL,KAAK,GAAG7B,IAAI,CAACmC,GAAG,CAAC,IAAI,EAAEJ,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGD,KAAK,CAACC,CAAC,CAAC;EAC3E,CAAC;EAED,oBACEnE,OAAA,CAAC5B,GAAG;IAAAoG,QAAA,gBAEFxE,OAAA,CAAC5B,GAAG;MAACqG,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxDxE,OAAA,CAACjB,UAAU;QAAC8F,OAAO,EAAEA,CAAA,KAAM1E,QAAQ,CAAC,cAAc,CAAE;QAACsE,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eACjExE,OAAA,CAACJ,QAAQ;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACblF,OAAA,CAAC3B,UAAU;QAAC8G,OAAO,EAAC,IAAI;QAAAX,QAAA,EAAC;MAEzB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL9D,KAAK,iBACJpB,OAAA,CAACpB,KAAK;MAACwG,QAAQ,EAAC,OAAO;MAACX,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAACS,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,IAAI,CAAE;MAAAmD,QAAA,EAClEpD;IAAK;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EACA5D,OAAO,iBACNtB,OAAA,CAACpB,KAAK;MAACwG,QAAQ,EAAC,SAAS;MAACX,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAACS,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAAC,IAAI,CAAE;MAAAiD,QAAA,EACtElD;IAAO;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAEDlF,OAAA,CAACrB,IAAI;MAAC2G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAf,QAAA,gBAEzBxE,OAAA,CAACrB,IAAI;QAAC6G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAClBxE,OAAA,CAAC1B,IAAI;UAAAkG,QAAA,eACHxE,OAAA,CAACzB,WAAW;YAAAiG,QAAA,gBACVxE,OAAA,CAAC3B,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACO,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAGblF,OAAA,CAAChB,KAAK;cACJyF,EAAE,EAAE;gBACFkB,MAAM,EAAE,YAAY;gBACpBC,WAAW,EAAE,cAAc;gBAC3BC,YAAY,EAAE,CAAC;gBACfC,CAAC,EAAE,CAAC;gBACJC,SAAS,EAAE,QAAQ;gBACnBC,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBACTC,eAAe,EAAE;gBACnB;cACF,CAAE;cACFpB,OAAO,EAAEA,CAAA;gBAAA,IAAAqB,qBAAA;gBAAA,QAAAA,qBAAA,GAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,cAAAF,qBAAA,uBAArCA,qBAAA,CAAuCG,KAAK,CAAC,CAAC;cAAA,CAAC;cAAA7B,QAAA,gBAE9DxE,OAAA,CAACV,UAAU;gBAACmF,EAAE,EAAE;kBAAE6B,QAAQ,EAAE,EAAE;kBAAEC,KAAK,EAAE,cAAc;kBAAE3B,EAAE,EAAE;gBAAE;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClElF,OAAA,CAAC3B,UAAU;gBAAC8G,OAAO,EAAC,IAAI;gBAACO,YAAY;gBAAAlB,QAAA,EAAC;cAEtC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAAC3B,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAACoB,KAAK,EAAC,eAAe;gBAAA/B,QAAA,EAAC;cAElD;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA;gBACEmC,EAAE,EAAC,YAAY;gBACfF,IAAI,EAAC,MAAM;gBACXuE,QAAQ;gBACRC,MAAM,EAAC,MAAM;gBACbC,KAAK,EAAE;kBAAEhC,OAAO,EAAE;gBAAO,CAAE;gBAC3BiC,QAAQ,EAAEnF;cAAiB;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EAGP9E,KAAK,CAACsC,MAAM,GAAG,CAAC,iBACf1C,OAAA,CAAC5B,GAAG;cAACqG,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAE,CAAE;cAAApC,QAAA,gBACjBxE,OAAA,CAAC3B,UAAU;gBAAC8G,OAAO,EAAC,WAAW;gBAACO,YAAY;gBAAAlB,QAAA,GAAC,kBAC3B,EAACpE,KAAK,CAACsC,MAAM,EAAC,GAChC;cAAA;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAACf,IAAI;gBAAC4H,KAAK;gBAAArC,QAAA,EACRpE,KAAK,CAAC8B,GAAG,CAAEoB,QAAQ,iBAClBtD,OAAA,CAACd,QAAQ;kBAAmB4H,OAAO;kBAAAtC,QAAA,gBACjCxE,OAAA,CAACN,QAAQ;oBAAC+E,EAAE,EAAE;sBAAEK,EAAE,EAAE,CAAC;sBAAEyB,KAAK,EAAE;oBAAe;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDlF,OAAA,CAACb,YAAY;oBACX4H,OAAO,EAAEzD,QAAQ,CAACtB,IAAI,CAACgF,IAAK;oBAC5BC,SAAS,eACPjH,OAAA,CAAC5B,GAAG;sBAAAoG,QAAA,gBACFxE,OAAA,CAAC3B,UAAU;wBAAC8G,OAAO,EAAC,SAAS;wBAACT,OAAO,EAAC,OAAO;wBAAAF,QAAA,EAC1CR,cAAc,CAACV,QAAQ,CAACtB,IAAI,CAACkF,IAAI;sBAAC;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACblF,OAAA,CAAC5B,GAAG;wBAACqG,EAAE,EAAE;0BAAEC,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEwC,GAAG,EAAE,CAAC;0BAAEP,EAAE,EAAE;wBAAI,CAAE;wBAAApC,QAAA,gBAClExE,OAAA,CAAClB,IAAI;0BACHsI,KAAK,EAAE9D,QAAQ,CAACb,MAAO;0BACvByE,IAAI,EAAC,OAAO;0BACZX,KAAK,EAAExC,kBAAkB,CAACT,QAAQ,CAACb,MAAM;wBAAS;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC,EACD5B,QAAQ,CAACb,MAAM,KAAK,WAAW,iBAC9BzC,OAAA,CAACnB,cAAc;0BACbsG,OAAO,EAAC,aAAa;0BACrBlC,KAAK,EAAEK,QAAQ,CAACd,QAAS;0BACzBiC,EAAE,EAAE;4BAAE4C,QAAQ,EAAE,CAAC;4BAAEC,MAAM,EAAE;0BAAE;wBAAE;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CACF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EACL5B,QAAQ,CAAClC,KAAK,iBACbpB,OAAA,CAAC3B,UAAU;wBAAC8G,OAAO,EAAC,SAAS;wBAACoB,KAAK,EAAC,OAAO;wBAAA/B,QAAA,EACxClB,QAAQ,CAAClC;sBAAK;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFlF,OAAA,CAACZ,uBAAuB;oBAAAoF,QAAA,eACtBxE,OAAA,CAACjB,UAAU;sBACTwI,IAAI,EAAC,KAAK;sBACV1C,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACU,QAAQ,CAACnB,EAAE,CAAE;sBAC7CqF,QAAQ,EAAElE,QAAQ,CAACb,MAAM,KAAK,WAAY;sBAAA+B,QAAA,eAE1CxE,OAAA,CAACR,UAAU;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACU,CAAC;gBAAA,GAvCb5B,QAAQ,CAACnB,EAAE;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwChB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlF,OAAA,CAACrB,IAAI;QAAC6G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eAClBxE,OAAA,CAAC1B,IAAI;UAAAkG,QAAA,eACHxE,OAAA,CAACzB,WAAW;YAAAiG,QAAA,gBACVxE,OAAA,CAAC3B,UAAU;cAAC8G,OAAO,EAAC,IAAI;cAACO,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEblF,OAAA,CAACrB,IAAI;cAAC2G,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAf,QAAA,gBACzBxE,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eACXxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,SAAS;kBACfnE,KAAK,EAAE3C,QAAQ,CAACE,KAAM;kBACtBmG,QAAQ,EAAE5D,iBAAiB,CAAC,OAAO,CAAE;kBACrC2E,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eACXxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,WAAW;kBACjBnE,KAAK,EAAE3C,QAAQ,CAACG,OAAQ;kBACxBkG,QAAQ,EAAE5D,iBAAiB,CAAC,SAAS,CAAE;kBACvC4E,WAAW,EAAC,iCAAiC;kBAC7CD,QAAQ;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAACoC,EAAE,EAAE,CAAE;gBAAApD,QAAA,eAClBxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,MAAM;kBACZnE,KAAK,EAAE3C,QAAQ,CAACI,IAAK;kBACrBiG,QAAQ,EAAE5D,iBAAiB,CAAC,MAAM;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAACoC,EAAE,EAAE,CAAE;gBAAApD,QAAA,eAClBxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,kBAAkB;kBACxBnF,IAAI,EAAC,QAAQ;kBACbgB,KAAK,EAAE3C,QAAQ,CAACM,gBAAgB,IAAI,EAAG;kBACvC+F,QAAQ,EAAE5D,iBAAiB,CAAC,kBAAkB;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAACoC,EAAE,EAAE,CAAE;gBAAApD,QAAA,eAClBxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,WAAW;kBACjBnE,KAAK,EAAE3C,QAAQ,CAACK,SAAU;kBAC1BgG,QAAQ,EAAE5D,iBAAiB,CAAC,WAAW;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAACoC,EAAE,EAAE,CAAE;gBAAApD,QAAA,eAClBxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,SAAS;kBACfnE,KAAK,EAAE3C,QAAQ,CAACQ,OAAQ;kBACxB6F,QAAQ,EAAE5D,iBAAiB,CAAC,SAAS;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eACXxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,MAAM;kBACZnE,KAAK,EAAE3C,QAAQ,CAACU,IAAK;kBACrB2F,QAAQ,EAAE5D,iBAAiB,CAAC,MAAM,CAAE;kBACpC4E,WAAW,EAAC;gBAA4D;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPlF,OAAA,CAACrB,IAAI;gBAAC6G,EAAE,EAAE,EAAG;gBAAAhB,QAAA,eACXxE,OAAA,CAACxB,SAAS;kBACRiJ,SAAS;kBACTL,KAAK,EAAC,aAAa;kBACnBnE,KAAK,EAAE3C,QAAQ,CAACS,WAAY;kBAC5B4F,QAAQ,EAAE5D,iBAAiB,CAAC,aAAa,CAAE;kBAC3C8E,SAAS;kBACTC,IAAI,EAAE,CAAE;kBACRH,WAAW,EAAC;gBAAuC;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEPlF,OAAA,CAAC5B,GAAG;cAACqG,EAAE,EAAE;gBAAEmC,EAAE,EAAE,CAAC;gBAAElC,OAAO,EAAE,MAAM;gBAAEyC,GAAG,EAAE;cAAE,CAAE;cAAA3C,QAAA,gBAC1CxE,OAAA,CAACvB,MAAM;gBACL0G,OAAO,EAAC,WAAW;gBACnBN,OAAO,EAAExB,YAAa;gBACtBmE,QAAQ,EAAEtG,SAAS,IAAId,KAAK,CAACsC,MAAM,KAAK,CAAE;gBAC1CqF,SAAS,eAAE/H,OAAA,CAACV,UAAU;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BuC,SAAS;gBAAAjD,QAAA,EAERtD,SAAS,GAAG,cAAc,GAAG;cAAa;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACTlF,OAAA,CAACvB,MAAM;gBACL0G,OAAO,EAAC,UAAU;gBAClBN,OAAO,EAAEA,CAAA,KAAM1E,QAAQ,CAAC,cAAc,CAAE;gBACxCqH,QAAQ,EAAEtG,SAAU;gBAAAsD,QAAA,EACrB;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChF,EAAA,CArYID,UAAoB;EAAA,QACPJ,WAAW;AAAA;AAAAmI,EAAA,GADxB/H,UAAoB;AAuY1B,eAAeA,UAAU;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}