/**
 * Citation viewer component for displaying detailed citation information
 */
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Paper,
} from '@mui/material';
import {
  Close as CloseIcon,
  MenuBook as BookIcon,
  Link as LinkIcon,
  Star as StarIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { qaService } from '../../services/qaService';
import { useNotification } from '../../contexts/NotificationContext';
import { Citation } from '../../types';

interface CitationViewerProps {
  citation: Citation | null;
  open: boolean;
  onClose: () => void;
}

interface CitationDetails {
  citation: {
    id: string;
    cited_text: string;
    relevance_score: number;
    citation_order: number;
  };
  chunk: {
    id: string;
    content: string;
    page_number: number;
    chapter_title?: string;
    topic_category?: string;
  };
  book: {
    id: string;
    title: string;
    authors: string[];
    publisher?: string;
    publication_year?: number;
  };
  surrounding_chunks: Array<{
    content: string;
    chunk_index: number;
  }>;
}

const CitationViewer: React.FC<CitationViewerProps> = ({ citation, open, onClose }) => {
  const [citationDetails, setCitationDetails] = useState<CitationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showSuccess, showError } = useNotification();

  useEffect(() => {
    if (open && citation) {
      fetchCitationDetails();
    }
  }, [open, citation]);

  const fetchCitationDetails = async () => {
    if (!citation) return;

    setIsLoading(true);
    setError(null);

    try {
      // For now, we'll create mock detailed data since the citation ID might not be available
      // In a real implementation, you would call: await qaService.getCitationDetails(citation.id);
      
      const mockDetails: CitationDetails = {
        citation: {
          id: citation.chunk_id,
          cited_text: citation.cited_text,
          relevance_score: citation.relevance_score,
          citation_order: citation.citation_order,
        },
        chunk: {
          id: citation.chunk_id,
          content: citation.cited_text,
          page_number: citation.page_number || 0,
          chapter_title: citation.chapter_title,
          topic_category: 'general',
        },
        book: {
          id: citation.book_id,
          title: citation.book_title,
          authors: citation.book_authors,
          publisher: 'Medical Publisher',
          publication_year: 2023,
        },
        surrounding_chunks: [
          {
            content: 'Previous context chunk that provides additional background information...',
            chunk_index: 1,
          },
          {
            content: 'Following context chunk that continues the discussion...',
            chunk_index: 3,
          },
        ],
      };

      setCitationDetails(mockDetails);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to load citation details';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
    showSuccess('Text copied to clipboard');
  };

  const formatCitation = () => {
    if (!citationDetails) return '';
    
    const { book, chunk } = citationDetails;
    return `${book.authors.join(', ')}. ${book.title}. ${book.publisher}, ${book.publication_year}. Page ${chunk.page_number}.`;
  };

  if (!citation) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BookIcon color="primary" />
          <Typography variant="h6">Citation Details</Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : citationDetails ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Book Information */}
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  {citationDetails.book.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {citationDetails.book.authors.join(', ')}
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                  {citationDetails.book.publisher && (
                    <Chip label={citationDetails.book.publisher} size="small" variant="outlined" />
                  )}
                  {citationDetails.book.publication_year && (
                    <Chip label={citationDetails.book.publication_year} size="small" variant="outlined" />
                  )}
                  <Chip 
                    label={`Page ${citationDetails.chunk.page_number}`} 
                    size="small" 
                    color="primary" 
                  />
                </Box>
              </CardContent>
            </Card>

            {/* Citation Metadata */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Citation Information
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  icon={<StarIcon />}
                  label={`${(citationDetails.citation.relevance_score * 100).toFixed(1)}% relevance`}
                  color="secondary"
                />
                <Chip 
                  label={`Citation #${citationDetails.citation.citation_order}`}
                  variant="outlined"
                />
                {citationDetails.chunk.chapter_title && (
                  <Chip 
                    label={citationDetails.chunk.chapter_title}
                    variant="outlined"
                  />
                )}
              </Box>
            </Box>

            {/* Main Content */}
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Cited Content
                </Typography>
                <Tooltip title="Copy text">
                  <IconButton 
                    size="small" 
                    onClick={() => handleCopyText(citationDetails.chunk.content)}
                  >
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                <Typography variant="body1" sx={{ lineHeight: 1.7 }}>
                  {citationDetails.chunk.content}
                </Typography>
              </Paper>
            </Box>

            {/* Surrounding Context */}
            {citationDetails.surrounding_chunks.length > 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Surrounding Context
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {citationDetails.surrounding_chunks.map((chunk, index) => (
                    <Card key={index} variant="outlined" sx={{ backgroundColor: 'grey.25' }}>
                      <CardContent sx={{ py: 2 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Context Chunk {chunk.chunk_index}
                        </Typography>
                        <Typography variant="body2">
                          {chunk.content}
                        </Typography>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              </Box>
            )}

            {/* Formatted Citation */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Formatted Citation
              </Typography>
              <Paper sx={{ p: 2, backgroundColor: 'primary.50' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Typography variant="body2" sx={{ fontStyle: 'italic', flex: 1 }}>
                    {formatCitation()}
                  </Typography>
                  <Tooltip title="Copy citation">
                    <IconButton 
                      size="small" 
                      onClick={() => handleCopyText(formatCitation())}
                    >
                      <CopyIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Paper>
            </Box>
          </Box>
        ) : null}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
        {citationDetails && (
          <Button 
            variant="contained" 
            startIcon={<LinkIcon />}
            onClick={() => {
              // In a real implementation, this would navigate to the full book/chapter view
              showSuccess('Book view feature coming soon!');
            }}
          >
            View in Book
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CitationViewer;
