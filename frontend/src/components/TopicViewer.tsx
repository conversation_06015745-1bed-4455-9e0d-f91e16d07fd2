/**
 * TopicViewer Component - Production-quality offline topic search with citation viewer
 * 
 * Features:
 * - Semantic search using local embeddings
 * - Results grouped by medical section types (Diagnosis, Treatment, etc.)
 * - Expandable citation accordions
 * - Full text viewing with metadata
 * - Error handling and loading states
 * - Responsive design
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  IconButton,
  Tooltip,
  Badge,
  Stack,
  Container
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Book as BookIcon,
  LocalHospital as MedicalIcon,
  Visibility as ViewIcon,
  VisibilityOff as HideIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Types for the topic search functionality
interface TopicSearchResult {
  text: string;
  score: number;
  metadata: {
    section_type: string;
    book_title: string;
    chapter: string;
    page_number: number;
    author: string;
    word_count: number;
    char_count: number;
  };
}

interface TopicSearchResponse {
  query: string;
  total_results: number;
  grouped_results: Record<string, TopicSearchResult[]>;
  message: string;
  suggestions: string[];
}

// Styled components for better visual hierarchy
const SearchContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
}));

const ResultsContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

const SectionAccordion = styled(Accordion)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  '&:before': {
    display: 'none',
  },
  boxShadow: theme.shadows[2],
}));

const CitationCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  border: `1px solid ${theme.palette.divider}`,
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
    transition: 'all 0.2s ease-in-out',
  },
}));

const ScoreChip = styled(Chip)<{ score: number }>(({ theme, score }) => ({
  backgroundColor: score > 0.8 ? theme.palette.success.main : 
                   score > 0.6 ? theme.palette.warning.main : 
                   theme.palette.error.main,
  color: 'white',
  fontWeight: 'bold',
}));

// Section type icons and colors
const sectionConfig = {
  'Diagnosis': { icon: '🔍', color: '#1976d2', description: 'Diagnostic criteria and methods' },
  'Treatment': { icon: '💊', color: '#2e7d32', description: 'Treatment protocols and medications' },
  'Prognosis': { icon: '📈', color: '#ed6c02', description: 'Outcomes and prognosis information' },
  'Anatomy': { icon: '🫀', color: '#9c27b0', description: 'Anatomical structures and systems' },
  'Pathophysiology': { icon: '🧬', color: '#d32f2f', description: 'Disease mechanisms and pathways' },
  'Other': { icon: '📚', color: '#757575', description: 'General medical information' },
};

const TopicViewer: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<TopicSearchResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [expandedTexts, setExpandedTexts] = useState<Set<string>>(new Set());

  // Handle search submission
  const handleSearch = useCallback(async () => {
    if (!query.trim()) {
      setError('Please enter a search query');
      return;
    }

    setLoading(true);
    setError(null);
    setResults(null);

    try {
      const response = await fetch('/api/v1/search/topic', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          query: query.trim(),
          limit: 20,
          score_threshold: 0.6,
        }),
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('Authentication required. Please log in to search.');
        }
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data: TopicSearchResponse = await response.json();
      setResults(data);

      // Auto-expand sections with results
      const sectionsWithResults = Object.keys(data.grouped_results);
      setExpandedSections(new Set(sectionsWithResults.slice(0, 2))); // Expand first 2 sections

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [query]);

  // Handle section accordion toggle
  const handleSectionToggle = useCallback((section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  }, []);

  // Handle text expansion toggle
  const handleTextToggle = useCallback((resultId: string) => {
    setExpandedTexts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(resultId)) {
        newSet.delete(resultId);
      } else {
        newSet.add(resultId);
      }
      return newSet;
    });
  }, []);

  // Truncate text for preview
  const truncateText = (text: string, maxLength: number = 200): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // Handle Enter key press
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 4 }}>
        {/* Header */}
        <Typography variant="h4" component="h1" gutterBottom align="center" sx={{ mb: 4 }}>
          <MedicalIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
          Medical Topic Search
        </Typography>

        {/* Search Interface */}
        <SearchContainer elevation={3}>
          <Typography variant="h6" gutterBottom>
            Search Medical Literature
          </Typography>
          <Typography variant="body2" sx={{ mb: 3, opacity: 0.9 }}>
            Search through medical textbooks using AI-powered semantic search. 
            Results are grouped by medical sections for easy navigation.
          </Typography>
          
          <Stack direction="row" spacing={2} alignItems="center">
            <TextField
              fullWidth
              variant="outlined"
              placeholder="e.g., diabetes mellitus diagnosis, kawasaki disease treatment..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '& fieldset': { borderColor: 'rgba(255, 255, 255, 0.3)' },
                  '&:hover fieldset': { borderColor: 'rgba(255, 255, 255, 0.5)' },
                  '&.Mui-focused fieldset': { borderColor: 'white' },
                },
                '& .MuiInputBase-input': { color: 'white' },
                '& .MuiInputBase-input::placeholder': { color: 'rgba(255, 255, 255, 0.7)' },
              }}
            />
            <Button
              variant="contained"
              onClick={handleSearch}
              disabled={loading || !query.trim()}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
              sx={{
                minWidth: 120,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.3)' },
              }}
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </Stack>
        </SearchContainer>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Results Display */}
        {results && (
          <ResultsContainer>
            {/* Results Summary */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Search Results for "{results.query}"
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {results.message}
              </Typography>
              
              {results.suggestions.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Suggestions:
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {results.suggestions.map((suggestion, index) => (
                      <Chip
                        key={index}
                        label={suggestion}
                        size="small"
                        variant="outlined"
                        onClick={() => setQuery(suggestion)}
                        sx={{ mb: 1 }}
                      />
                    ))}
                  </Stack>
                </Box>
              )}
            </Paper>

            {/* Grouped Results by Section */}
            {Object.entries(results.grouped_results).map(([sectionType, sectionResults]) => {
              const config = sectionConfig[sectionType as keyof typeof sectionConfig] || sectionConfig.Other;
              const isExpanded = expandedSections.has(sectionType);

              return (
                <SectionAccordion
                  key={sectionType}
                  expanded={isExpanded}
                  onChange={() => handleSectionToggle(sectionType)}
                >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Stack direction="row" alignItems="center" spacing={2} sx={{ width: '100%' }}>
                      <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ marginRight: 8, fontSize: '1.2em' }}>{config.icon}</span>
                        {sectionType}
                      </Typography>
                      <Badge badgeContent={sectionResults.length} color="primary" />
                      <Tooltip title={config.description}>
                        <InfoIcon sx={{ color: 'text.secondary', fontSize: 16 }} />
                      </Tooltip>
                    </Stack>
                  </AccordionSummary>
                  
                  <AccordionDetails>
                    {sectionResults.map((result, index) => {
                      const resultId = `${sectionType}-${index}`;
                      const isTextExpanded = expandedTexts.has(resultId);
                      const displayText = isTextExpanded ? result.text : truncateText(result.text);

                      return (
                        <CitationCard key={index} elevation={1}>
                          <CardContent>
                            {/* Result Header */}
                            <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={{ mb: 2 }}>
                              <Stack direction="row" spacing={1} alignItems="center">
                                <BookIcon sx={{ color: config.color }} />
                                <Typography variant="subtitle1" fontWeight="bold">
                                  {result.metadata.book_title}
                                </Typography>
                                <ScoreChip
                                  score={result.score}
                                  label={`${(result.score * 100).toFixed(1)}%`}
                                  size="small"
                                />
                              </Stack>
                              
                              <Tooltip title={isTextExpanded ? "Show less" : "Show full text"}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleTextToggle(resultId)}
                                >
                                  {isTextExpanded ? <HideIcon /> : <ViewIcon />}
                                </IconButton>
                              </Tooltip>
                            </Stack>

                            {/* Citation Metadata */}
                            <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                              <Chip label={`Chapter: ${result.metadata.chapter}`} size="small" variant="outlined" />
                              <Chip label={`Page: ${result.metadata.page_number}`} size="small" variant="outlined" />
                              <Chip label={`Author: ${result.metadata.author}`} size="small" variant="outlined" />
                            </Stack>

                            <Divider sx={{ my: 2 }} />

                            {/* Result Text */}
                            <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                              {displayText}
                            </Typography>

                            {/* Text Stats */}
                            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                              <Typography variant="caption" color="text.secondary">
                                {result.metadata.word_count} words
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {result.metadata.char_count} characters
                              </Typography>
                            </Stack>
                          </CardContent>
                        </CitationCard>
                      );
                    })}
                  </AccordionDetails>
                </SectionAccordion>
              );
            })}

            {/* No Results Message */}
            {Object.keys(results.grouped_results).length === 0 && (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No results found
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Try using different keywords or broader medical terms.
                </Typography>
              </Paper>
            )}
          </ResultsContainer>
        )}
      </Box>
    </Container>
  );
};

export default TopicViewer;
