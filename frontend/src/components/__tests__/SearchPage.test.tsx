/**
 * Tests for SearchPage component
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SearchPage from '../../pages/SearchPage';
import { AuthProvider } from '../../contexts/AuthContext';
import { NotificationProvider } from '../../contexts/NotificationContext';
import * as searchService from '../../services/searchService';
import * as qaService from '../../services/qaService';

// Mock services
jest.mock('../../services/searchService');
jest.mock('../../services/qaService');

const mockSearchService = searchService as jest.Mocked<typeof searchService>;
const mockQaService = qaService as jest.Mocked<typeof qaService>;

const theme = createTheme();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <AuthProvider>
        <NotificationProvider>
          {children}
        </NotificationProvider>
      </AuthProvider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('SearchPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders search page with initial state', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    expect(screen.getByText('Medical Literature Search')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search medical literature...')).toBeInTheDocument();
    expect(screen.getByText('Search Literature')).toBeInTheDocument();
    expect(screen.getByText('Ask AI Question')).toBeInTheDocument();
  });

  test('switches between search and Q&A modes', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    const qaButton = screen.getByText('Ask AI Question');
    fireEvent.click(qaButton);

    expect(screen.getByPlaceholderText('Ask a medical question...')).toBeInTheDocument();
    expect(screen.getByText('Ask AI')).toBeInTheDocument();
  });

  test('performs search successfully', async () => {
    const mockSearchResponse = {
      query: 'diabetes',
      total_results: 2,
      search_time_ms: 150,
      results: [
        {
          chunk_id: '1',
          book_id: '1',
          content: 'Diabetes mellitus is a chronic condition...',
          score: 0.89,
          page_number: 156,
          chapter_title: 'Endocrine Disorders',
          topic_category: 'diagnosis',
          medical_topics: ['diabetes', 'endocrine'],
          word_count: 150,
          char_count: 800,
          book_title: "Harrison's Internal Medicine",
          book_authors: ['Dennis Kasper', 'Anthony Fauci'],
          book_publisher: 'McGraw-Hill',
          book_publication_year: 2018
        }
      ]
    };

    mockSearchService.searchService.search.mockResolvedValue(mockSearchResponse);

    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search medical literature...');
    const searchButton = screen.getByText('Search');

    fireEvent.change(searchInput, { target: { value: 'diabetes' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('Search Results (2)')).toBeInTheDocument();
      expect(screen.getByText("Harrison's Internal Medicine")).toBeInTheDocument();
      expect(screen.getByText('Diabetes mellitus is a chronic condition...')).toBeInTheDocument();
    });
  });

  test('performs Q&A successfully', async () => {
    const mockQaResponse = {
      question: 'What causes diabetes?',
      answer: 'Diabetes is caused by various factors including genetics...',
      answer_id: 'qa-1',
      citations: [
        {
          chunk_id: '1',
          book_id: '1',
          book_title: "Harrison's Internal Medicine",
          book_authors: ['Dennis Kasper'],
          page_number: 156,
          chapter_title: 'Endocrine Disorders',
          cited_text: 'Type 1 diabetes is caused by...',
          relevance_score: 0.92,
          citation_order: 1
        }
      ],
      confidence_score: 0.87,
      response_time_ms: 1234,
      model_used: 'gpt-4',
      context_chunks_used: 3
    };

    mockQaService.qaService.askQuestion.mockResolvedValue(mockQaResponse);

    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    // Switch to Q&A mode
    const qaButton = screen.getByText('Ask AI Question');
    fireEvent.click(qaButton);

    const questionInput = screen.getByPlaceholderText('Ask a medical question...');
    const askButton = screen.getByText('Ask AI');

    fireEvent.change(questionInput, { target: { value: 'What causes diabetes?' } });
    fireEvent.click(askButton);

    await waitFor(() => {
      expect(screen.getByText('AI Answer')).toBeInTheDocument();
      expect(screen.getByText('Diabetes is caused by various factors including genetics...')).toBeInTheDocument();
      expect(screen.getByText('Citations (1)')).toBeInTheDocument();
    });
  });

  test('shows and hides filters', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    const showFiltersButton = screen.getByText('Show Filters');
    fireEvent.click(showFiltersButton);

    expect(screen.getByText('Search Filters')).toBeInTheDocument();
    expect(screen.getByText('Topic Categories')).toBeInTheDocument();
    expect(screen.getByText('Relevance Threshold:')).toBeInTheDocument();

    const hideFiltersButton = screen.getByText('Hide Filters');
    fireEvent.click(hideFiltersButton);

    expect(screen.queryByText('Search Filters')).not.toBeInTheDocument();
  });

  test('handles search error', async () => {
    mockSearchService.searchService.search.mockRejectedValue(new Error('Search failed'));

    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search medical literature...');
    const searchButton = screen.getByText('Search');

    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(screen.getByText('Search failed')).toBeInTheDocument();
    });
  });

  test('handles Q&A error', async () => {
    mockQaService.qaService.askQuestion.mockRejectedValue(new Error('Q&A failed'));

    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    // Switch to Q&A mode
    const qaButton = screen.getByText('Ask AI Question');
    fireEvent.click(qaButton);

    const questionInput = screen.getByPlaceholderText('Ask a medical question...');
    const askButton = screen.getByText('Ask AI');

    fireEvent.change(questionInput, { target: { value: 'test question' } });
    fireEvent.click(askButton);

    await waitFor(() => {
      expect(screen.getByText('Q&A failed')).toBeInTheDocument();
    });
  });

  test('clears search input', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search medical literature...') as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: 'test query' } });

    expect(searchInput.value).toBe('test query');

    // Find and click clear button (X icon)
    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);

    expect(searchInput.value).toBe('');
  });

  test('applies filters correctly', async () => {
    const mockSearchResponse = {
      query: 'diabetes',
      total_results: 1,
      search_time_ms: 150,
      results: []
    };

    mockSearchService.searchService.search.mockResolvedValue(mockSearchResponse);

    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    // Show filters
    const showFiltersButton = screen.getByText('Show Filters');
    fireEvent.click(showFiltersButton);

    // Set topic category filter
    const topicSelect = screen.getByLabelText('Topic Categories');
    fireEvent.mouseDown(topicSelect);
    
    const diagnosisOption = screen.getByText('Diagnosis');
    fireEvent.click(diagnosisOption);

    // Perform search
    const searchInput = screen.getByPlaceholderText('Search medical literature...');
    const searchButton = screen.getByText('Search');

    fireEvent.change(searchInput, { target: { value: 'diabetes' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(mockSearchService.searchService.search).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'diabetes',
          filters: expect.objectContaining({
            topic_categories: expect.arrayContaining(['diagnosis'])
          })
        })
      );
    });
  });

  test('clears filters', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    // Show filters
    const showFiltersButton = screen.getByText('Show Filters');
    fireEvent.click(showFiltersButton);

    // Apply some filters first
    const topicSelect = screen.getByLabelText('Topic Categories');
    fireEvent.mouseDown(topicSelect);
    
    const diagnosisOption = screen.getByText('Diagnosis');
    fireEvent.click(diagnosisOption);

    // Clear filters
    const clearFiltersButton = screen.getByText('Clear Filters');
    fireEvent.click(clearFiltersButton);

    // Verify filters are cleared (this would need more specific assertions based on implementation)
    expect(clearFiltersButton).toBeInTheDocument();
  });

  test('displays empty state when no results', () => {
    render(
      <TestWrapper>
        <SearchPage />
      </TestWrapper>
    );

    expect(screen.getByText('Start your medical research')).toBeInTheDocument();
    expect(screen.getByText('Search through medical literature or ask AI-powered questions to get started')).toBeInTheDocument();
  });
});
