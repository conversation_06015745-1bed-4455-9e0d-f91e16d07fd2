/**
 * Topic detail component for displaying comprehensive topic information
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Topic as TopicIcon,
  MenuBook as BookIcon,
  Search as SearchIcon,
  QuestionAnswer as QAIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  Share as ShareIcon,
  Bookmark as BookmarkIcon,
} from '@mui/icons-material';
import { searchService } from '../../services/searchService';
import { useNotification } from '../../contexts/NotificationContext';
import { SearchResult, TopicCategory } from '../../types';

interface TopicDetailProps {
  topic: string;
  category?: TopicCategory;
  onSearch?: (query: string) => void;
  onAskQuestion?: (question: string) => void;
}

interface TopicData {
  name: string;
  category: TopicCategory;
  description: string;
  keyPoints: string[];
  relatedTopics: string[];
  recentResults: SearchResult[];
  statistics: {
    totalChunks: number;
    averageRelevance: number;
    topBooks: Array<{ title: string; count: number }>;
  };
}

const TopicDetail: React.FC<TopicDetailProps> = ({ 
  topic, 
  category = TopicCategory.GENERAL,
  onSearch,
  onAskQuestion 
}) => {
  const [topicData, setTopicData] = useState<TopicData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const { showSuccess, showError } = useNotification();

  useEffect(() => {
    if (topic) {
      fetchTopicData();
    }
  }, [topic]);

  const fetchTopicData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Search for content related to this topic
      const searchResults = await searchService.search({
        query: topic,
        limit: 10,
        score_threshold: 0.7,
      });

      // Create mock topic data (in a real implementation, this would come from a dedicated API)
      const mockTopicData: TopicData = {
        name: topic,
        category,
        description: `Comprehensive information about ${topic} including pathophysiology, diagnosis, treatment, and management approaches.`,
        keyPoints: [
          `Understanding the fundamentals of ${topic}`,
          `Clinical presentation and symptoms`,
          `Diagnostic approaches and criteria`,
          `Treatment options and management`,
          `Prognosis and follow-up care`,
        ],
        relatedTopics: [
          'Related Condition A',
          'Related Condition B',
          'Associated Symptoms',
          'Treatment Protocols',
          'Prevention Strategies',
        ],
        recentResults: searchResults.results.slice(0, 5),
        statistics: {
          totalChunks: searchResults.total_results,
          averageRelevance: searchResults.results.reduce((acc, r) => acc + r.score, 0) / searchResults.results.length,
          topBooks: [
            { title: 'Harrison\'s Principles of Internal Medicine', count: 15 },
            { title: 'Robbins Basic Pathology', count: 12 },
            { title: 'Kumar and Clark\'s Clinical Medicine', count: 8 },
          ],
        },
      };

      setTopicData(mockTopicData);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to load topic data';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = () => {
    const url = `${window.location.origin}/topics/${encodeURIComponent(topic)}`;
    navigator.clipboard.writeText(url);
    showSuccess('Topic link copied to clipboard');
  };

  const handleBookmark = () => {
    // In a real implementation, this would save to user's bookmarks
    showSuccess('Topic bookmarked');
  };

  const suggestedQuestions = [
    `What are the main causes of ${topic}?`,
    `How is ${topic} diagnosed?`,
    `What are the treatment options for ${topic}?`,
    `What is the prognosis for ${topic}?`,
  ];

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!topicData) {
    return null;
  }

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
              {topicData.name}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Chip 
                icon={<TopicIcon />}
                label={topicData.category}
                color="primary"
              />
              <Chip 
                label={`${topicData.statistics.totalChunks} references`}
                variant="outlined"
              />
              <Chip 
                label={`${(topicData.statistics.averageRelevance * 100).toFixed(1)}% avg relevance`}
                variant="outlined"
              />
            </Box>
            <Typography variant="body1" color="text.secondary">
              {topicData.description}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Share topic">
              <IconButton onClick={handleShare}>
                <ShareIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Bookmark topic">
              <IconButton onClick={handleBookmark}>
                <BookmarkIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Quick Actions */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<SearchIcon />}
            onClick={() => onSearch?.(topic)}
          >
            Search Literature
          </Button>
          <Button
            variant="outlined"
            startIcon={<QAIcon />}
            onClick={() => onAskQuestion?.(suggestedQuestions[0])}
          >
            Ask AI Question
          </Button>
        </Box>
      </Paper>

      {/* Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Overview" />
          <Tab label="Key Points" />
          <Tab label="Related Content" />
          <Tab label="Statistics" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Overview Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Recent Literature
              </Typography>
              {topicData.recentResults.length > 0 ? (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {topicData.recentResults.map((result, index) => (
                    <Card key={index} variant="outlined">
                      <CardContent sx={{ py: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle1" color="primary" gutterBottom>
                              {result.book_title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              Page {result.page_number} • {result.book_authors.join(', ')}
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {result.content.substring(0, 200)}...
                            </Typography>
                          </Box>
                          <Chip 
                            icon={<StarIcon />}
                            label={`${(result.score * 100).toFixed(1)}%`}
                            size="small"
                            color="secondary"
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Typography color="text.secondary">
                  No recent literature found for this topic.
                </Typography>
              )}
            </Box>
          )}

          {/* Key Points Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Key Learning Points
              </Typography>
              <List>
                {topicData.keyPoints.map((point, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <StarIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary={point} />
                  </ListItem>
                ))}
              </List>

              <Divider sx={{ my: 3 }} />

              <Typography variant="h6" gutterBottom>
                Suggested Questions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    size="small"
                    sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    onClick={() => onAskQuestion?.(question)}
                  >
                    {question}
                  </Button>
                ))}
              </Box>
            </Box>
          )}

          {/* Related Content Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Related Topics
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                {topicData.relatedTopics.map((relatedTopic, index) => (
                  <Chip
                    key={index}
                    label={relatedTopic}
                    variant="outlined"
                    clickable
                    onClick={() => onSearch?.(relatedTopic)}
                  />
                ))}
              </Box>

              <Typography variant="h6" gutterBottom>
                Top Referenced Books
              </Typography>
              <List>
                {topicData.statistics.topBooks.map((book, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <BookIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={book.title}
                      secondary={`${book.count} references`}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* Statistics Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Topic Statistics
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
                <Card variant="outlined" sx={{ flex: 1, textAlign: 'center', p: 2 }}>
                  <TrendingIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="primary">
                    {topicData.statistics.totalChunks}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total References
                  </Typography>
                </Card>
                <Card variant="outlined" sx={{ flex: 1, textAlign: 'center', p: 2 }}>
                  <StarIcon color="secondary" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="secondary">
                    {(topicData.statistics.averageRelevance * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Relevance
                  </Typography>
                </Card>
                <Card variant="outlined" sx={{ flex: 1, textAlign: 'center', p: 2 }}>
                  <BookIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" color="success.main">
                    {topicData.statistics.topBooks.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Source Books
                  </Typography>
                </Card>
              </Box>
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default TopicDetail;
