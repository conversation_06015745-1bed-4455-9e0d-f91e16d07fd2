/**
 * Custom hooks for API calls with loading states and error handling
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { ApiError } from '../services/apiClient';

export interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
  success: boolean;
}

export interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: ApiError) => void;
  retries?: number;
  retryDelay?: number;
}

/**
 * Hook for making API calls with automatic loading states
 */
export function useApi<T = any>(
  apiCall: () => Promise<T>,
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  const isMountedRef = useRef(true);
  const retryCountRef = useRef(0);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const execute = useCallback(async () => {
    if (!isMountedRef.current) return;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false,
    }));

    try {
      const result = await apiCall();
      
      if (isMountedRef.current) {
        setState({
          data: result,
          loading: false,
          error: null,
          success: true,
        });

        options.onSuccess?.(result);
        retryCountRef.current = 0;
      }
    } catch (error) {
      if (!isMountedRef.current) return;

      const apiError = error instanceof ApiError ? error : new ApiError(
        error instanceof Error ? error.message : 'Unknown error',
        0
      );

      // Retry logic
      const maxRetries = options.retries || 0;
      if (retryCountRef.current < maxRetries && apiError.isServerError()) {
        retryCountRef.current++;
        const delay = options.retryDelay || 1000;
        
        setTimeout(() => {
          if (isMountedRef.current) {
            execute();
          }
        }, delay * retryCountRef.current);
        
        return;
      }

      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      });

      options.onError?.(apiError);
      retryCountRef.current = 0;
    }
  }, [apiCall, options]);

  // Execute immediately if requested
  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [execute, options.immediate]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
    retryCountRef.current = 0;
  }, []);

  return {
    ...state,
    execute,
    reset,
    retry: execute,
  };
}

/**
 * Hook for making API calls that depend on parameters
 */
export function useApiWithParams<T = any, P = any>(
  apiCall: (params: P) => Promise<T>,
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  const isMountedRef = useRef(true);
  const retryCountRef = useRef(0);
  const lastParamsRef = useRef<P | null>(null);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const execute = useCallback(async (params: P) => {
    if (!isMountedRef.current) return;

    lastParamsRef.current = params;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false,
    }));

    try {
      const result = await apiCall(params);
      
      if (isMountedRef.current) {
        setState({
          data: result,
          loading: false,
          error: null,
          success: true,
        });

        options.onSuccess?.(result);
        retryCountRef.current = 0;
      }
    } catch (error) {
      if (!isMountedRef.current) return;

      const apiError = error instanceof ApiError ? error : new ApiError(
        error instanceof Error ? error.message : 'Unknown error',
        0
      );

      // Retry logic
      const maxRetries = options.retries || 0;
      if (retryCountRef.current < maxRetries && apiError.isServerError()) {
        retryCountRef.current++;
        const delay = options.retryDelay || 1000;
        
        setTimeout(() => {
          if (isMountedRef.current && lastParamsRef.current) {
            execute(lastParamsRef.current);
          }
        }, delay * retryCountRef.current);
        
        return;
      }

      setState({
        data: null,
        loading: false,
        error: apiError,
        success: false,
      });

      options.onError?.(apiError);
      retryCountRef.current = 0;
    }
  }, [apiCall, options]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
    retryCountRef.current = 0;
    lastParamsRef.current = null;
  }, []);

  const retry = useCallback(() => {
    if (lastParamsRef.current) {
      execute(lastParamsRef.current);
    }
  }, [execute]);

  return {
    ...state,
    execute,
    reset,
    retry,
  };
}

/**
 * Hook for file uploads with progress tracking
 */
export function useFileUpload<T = any>(
  uploadCall: (file: File, data: Record<string, any>, onProgress: (progress: number) => void) => Promise<T>,
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<UseApiState<T> & { progress: number }>({
    data: null,
    loading: false,
    error: null,
    success: false,
    progress: 0,
  });

  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const upload = useCallback(async (file: File, data: Record<string, any> = {}) => {
    if (!isMountedRef.current) return;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false,
      progress: 0,
    }));

    try {
      const result = await uploadCall(file, data, (progress) => {
        if (isMountedRef.current) {
          setState(prev => ({ ...prev, progress }));
        }
      });
      
      if (isMountedRef.current) {
        setState(prev => ({
          ...prev,
          data: result,
          loading: false,
          error: null,
          success: true,
          progress: 100,
        }));

        options.onSuccess?.(result);
      }
    } catch (error) {
      if (!isMountedRef.current) return;

      const apiError = error instanceof ApiError ? error : new ApiError(
        error instanceof Error ? error.message : 'Upload failed',
        0
      );

      setState(prev => ({
        ...prev,
        data: null,
        loading: false,
        error: apiError,
        success: false,
        progress: 0,
      }));

      options.onError?.(apiError);
    }
  }, [uploadCall, options]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
      progress: 0,
    });
  }, []);

  return {
    ...state,
    upload,
    reset,
  };
}

/**
 * Hook for managing multiple API calls
 */
export function useMultipleApi<T extends Record<string, any>>(
  apiCalls: { [K in keyof T]: () => Promise<T[K]> },
  options: UseApiOptions = {}
) {
  const [state, setState] = useState<{
    data: Partial<T>;
    loading: boolean;
    errors: Partial<Record<keyof T, ApiError>>;
    success: boolean;
  }>({
    data: {},
    loading: false,
    errors: {},
    success: false,
  });

  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const execute = useCallback(async () => {
    if (!isMountedRef.current) return;

    setState(prev => ({
      ...prev,
      loading: true,
      errors: {},
      success: false,
    }));

    const results: Partial<T> = {};
    const errors: Partial<Record<keyof T, ApiError>> = {};

    await Promise.allSettled(
      Object.entries(apiCalls).map(async ([key, apiCall]) => {
        try {
          const result = await (apiCall as () => Promise<any>)();
          results[key as keyof T] = result;
        } catch (error) {
          const apiError = error instanceof ApiError ? error : new ApiError(
            error instanceof Error ? error.message : 'Unknown error',
            0
          );
          errors[key as keyof T] = apiError;
        }
      })
    );

    if (isMountedRef.current) {
      const hasErrors = Object.keys(errors).length > 0;
      
      setState({
        data: results,
        loading: false,
        errors,
        success: !hasErrors,
      });

      if (!hasErrors) {
        options.onSuccess?.(results);
      } else {
        options.onError?.(Object.values(errors)[0] as ApiError);
      }
    }
  }, [apiCalls, options]);

  const reset = useCallback(() => {
    setState({
      data: {},
      loading: false,
      errors: {},
      success: false,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}
