/**
 * Home page component
 */
import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  Search as SearchIcon,
  QuestionAnswer as QAIcon,
  MenuBook as BookIcon,
  TrendingUp as TrendingIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const quickActions = [
    {
      title: 'Search Medical Literature',
      description: 'Find relevant information from medical textbooks and journals',
      icon: <SearchIcon sx={{ fontSize: 40 }} />,
      action: () => navigate('/search'),
      color: 'primary',
    },
    {
      title: 'Ask Medical Questions',
      description: 'Get AI-powered answers with citations from medical sources',
      icon: <QAIcon sx={{ fontSize: 40 }} />,
      action: () => navigate('/qa'),
      color: 'secondary',
    },
    {
      title: 'Browse Books',
      description: 'Explore the medical literature database',
      icon: <BookIcon sx={{ fontSize: 40 }} />,
      action: () => navigate('/books'),
      color: 'success',
    },
  ];

  const recentTopics = [
    'Cardiovascular Disease',
    'Diabetes Management',
    'Infectious Diseases',
    'Pharmacology',
    'Pathophysiology',
    'Clinical Diagnosis',
  ];

  const stats = [
    { label: 'Medical Books', value: '150+' },
    { label: 'Searchable Chunks', value: '50K+' },
    { label: 'Topics Covered', value: '200+' },
    { label: 'Active Users', value: '1K+' },
  ];

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
          Welcome back, {user?.full_name?.split(' ')[0]}!
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
          Your comprehensive medical preparation platform
        </Typography>
        
        {user?.institution && (
          <Chip 
            label={user.institution} 
            color="primary" 
            variant="outlined" 
            sx={{ mr: 1 }}
          />
        )}
        {user?.specialization && (
          <Chip 
            label={user.specialization} 
            color="secondary" 
            variant="outlined" 
          />
        )}
      </Box>

      {/* Quick Actions */}
      <Box sx={{ display: 'flex', gap: 3, mb: 4, flexWrap: 'wrap' }}>
        {quickActions.map((action, index) => (
          <Box key={index} sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(33.333% - 16px)' } }}>
            <Card
              sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
              onClick={action.action}
            >
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                <Box sx={{ color: `${action.color}.main`, mb: 2 }}>
                  {action.icon}
                </Box>
                <Typography variant="h6" component="h2" gutterBottom>
                  {action.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {action.description}
                </Typography>
                <Button
                  variant="contained"
                  color={action.color as any}
                  size="small"
                >
                  Get Started
                </Button>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>

      <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
        {/* Platform Statistics */}
        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 12px)' } }}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingIcon color="primary" />
              Platform Statistics
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              {stats.map((stat, index) => (
                <Box key={index} sx={{ flex: '1 1 calc(50% - 8px)', textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stat.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Box>

        {/* Popular Topics */}
        <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 12px)' } }}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Popular Medical Topics
            </Typography>
            <List dense>
              {recentTopics.map((topic, index) => (
                <ListItem
                  key={index}
                  sx={{
                    cursor: 'pointer',
                    borderRadius: 1,
                    '&:hover': { backgroundColor: 'action.hover' },
                  }}
                  onClick={() => navigate(`/search?q=${encodeURIComponent(topic)}`)}
                >
                  <ListItemText
                    primary={topic}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                </ListItem>
              ))}
            </List>
            <Button
              variant="outlined"
              size="small"
              fullWidth
              sx={{ mt: 2 }}
              onClick={() => navigate('/search')}
            >
              Explore All Topics
            </Button>
          </Paper>
        </Box>
      </Box>

      {/* Getting Started Tips */}
      <Paper sx={{ p: 3, mt: 3, backgroundColor: 'primary.main', color: 'primary.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          💡 Getting Started Tips
        </Typography>
        <Typography variant="body2" sx={{ mb: 2 }}>
          • Use specific medical terms in your searches for better results
        </Typography>
        <Typography variant="body2" sx={{ mb: 2 }}>
          • Ask detailed questions to get comprehensive AI-generated answers
        </Typography>
        <Typography variant="body2">
          • Check citations to verify information from original medical sources
        </Typography>
      </Paper>
    </Box>
  );
};

export default HomePage;
