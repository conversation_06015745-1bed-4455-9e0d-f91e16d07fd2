/**
 * Search page component
 */
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Slider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  QuestionAnswer as QAIcon,
  MenuBook as BookIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { searchService } from '../services/searchService';
import { qaService } from '../services/qaService';
import { useNotification } from '../contexts/NotificationContext';
import CitationViewer from '../components/Citations/CitationViewer';
import { SearchRequest, SearchResponse, QARequest, QAResponse, TopicCategory, Citation } from '../types';

const SearchPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { showError, showSuccess } = useNotification();

  // Search state
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Q&A state
  const [qaResponse, setQaResponse] = useState<QAResponse | null>(null);
  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false);
  const [qaError, setQaError] = useState<string | null>(null);

  // Filter state
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    topicCategories: [] as TopicCategory[],
    scoreThreshold: 0.7,
    limit: 20,
  });

  // Search mode
  const [searchMode, setSearchMode] = useState<'search' | 'qa'>('search');

  // Citation viewer state
  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);
  const [citationViewerOpen, setCitationViewerOpen] = useState(false);

  useEffect(() => {
    const initialQuery = searchParams.get('q');
    if (initialQuery && initialQuery !== query) {
      setQuery(initialQuery);
      handleSearch(initialQuery);
    }
  }, [searchParams]);

  const handleSearch = async (searchQuery?: string) => {
    const queryToSearch = searchQuery || query;
    if (!queryToSearch.trim()) {
      showError('Please enter a search query');
      return;
    }

    setIsSearching(true);
    setSearchError(null);
    setSearchResults(null);

    try {
      const searchRequest: SearchRequest = {
        query: queryToSearch,
        limit: filters.limit,
        score_threshold: filters.scoreThreshold,
        filters: {
          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined,
        },
      };

      const results = await searchService.search(searchRequest);
      setSearchResults(results);

      // Update URL
      setSearchParams({ q: queryToSearch });

      showSuccess(`Found ${results.total_results} results in ${results.search_time_ms}ms`);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Search failed';
      setSearchError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsSearching(false);
    }
  };

  const handleAskQuestion = async () => {
    if (!query.trim()) {
      showError('Please enter a question');
      return;
    }

    setIsGeneratingAnswer(true);
    setQaError(null);
    setQaResponse(null);

    try {
      const qaRequest: QARequest = {
        question: query,
        context_limit: 5,
        include_citations: true,
        filters: {
          topic_categories: filters.topicCategories.length > 0 ? filters.topicCategories : undefined,
        },
      };

      const response = await qaService.askQuestion(qaRequest);
      setQaResponse(response);

      showSuccess(`Generated answer with ${response.citations.length} citations in ${response.response_time_ms}ms`);
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to generate answer';
      setQaError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsGeneratingAnswer(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchMode === 'search') {
      handleSearch();
    } else {
      handleAskQuestion();
    }
  };

  const clearFilters = () => {
    setFilters({
      topicCategories: [],
      scoreThreshold: 0.7,
      limit: 20,
    });
  };

  const handleCitationClick = (citation: Citation) => {
    setSelectedCitation(citation);
    setCitationViewerOpen(true);
  };

  const handleCloseCitationViewer = () => {
    setCitationViewerOpen(false);
    setSelectedCitation(null);
  };

  const topicCategories = Object.values(TopicCategory);

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
          Medical Literature Search
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Search through medical textbooks or ask AI-powered questions
        </Typography>
      </Box>

      {/* Search Form */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box component="form" onSubmit={handleSubmit}>
          {/* Search Mode Toggle */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Button
              variant={searchMode === 'search' ? 'contained' : 'outlined'}
              startIcon={<SearchIcon />}
              onClick={() => setSearchMode('search')}
            >
              Search Literature
            </Button>
            <Button
              variant={searchMode === 'qa' ? 'contained' : 'outlined'}
              startIcon={<QAIcon />}
              onClick={() => setSearchMode('qa')}
            >
              Ask AI Question
            </Button>
          </Box>

          {/* Search Input */}
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              fullWidth
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={
                searchMode === 'search'
                  ? 'Search medical literature...'
                  : 'Ask a medical question...'
              }
              variant="outlined"
              slotProps={{
                input: {
                  endAdornment: query && (
                    <IconButton onClick={() => setQuery('')} size="small">
                      <ClearIcon />
                    </IconButton>
                  ),
                }
              }}
            />
            <Button
              type="submit"
              variant="contained"
              size="large"
              disabled={isSearching || isGeneratingAnswer}
              sx={{ minWidth: 120 }}
            >
              {isSearching || isGeneratingAnswer ? (
                <CircularProgress size={24} color="inherit" />
              ) : searchMode === 'search' ? (
                'Search'
              ) : (
                'Ask AI'
              )}
            </Button>
          </Box>

          {/* Filters Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
              variant="text"
            >
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </Button>
            {(filters.topicCategories.length > 0 || filters.scoreThreshold !== 0.7 || filters.limit !== 20) && (
              <Button onClick={clearFilters} size="small" color="secondary">
                Clear Filters
              </Button>
            )}
          </Box>
        </Box>

        {/* Filters */}
        {showFilters && (
          <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="h6" gutterBottom>
              Search Filters
            </Typography>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              {/* Topic Categories */}
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Topic Categories</InputLabel>
                <Select
                  multiple
                  value={filters.topicCategories}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    topicCategories: e.target.value as TopicCategory[]
                  }))}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {topicCategories.map((category) => (
                    <MenuItem key={category} value={category}>
                      <Checkbox checked={filters.topicCategories.indexOf(category) > -1} />
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Score Threshold */}
              <Box sx={{ minWidth: 200 }}>
                <Typography gutterBottom>
                  Relevance Threshold: {filters.scoreThreshold}
                </Typography>
                <Slider
                  value={filters.scoreThreshold}
                  onChange={(_, value) => setFilters(prev => ({
                    ...prev,
                    scoreThreshold: value as number
                  }))}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>

              {/* Result Limit */}
              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel>Results Limit</InputLabel>
                <Select
                  value={filters.limit}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    limit: e.target.value as number
                  }))}
                >
                  <MenuItem value={10}>10</MenuItem>
                  <MenuItem value={20}>20</MenuItem>
                  <MenuItem value={50}>50</MenuItem>
                  <MenuItem value={100}>100</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Error Display */}
      {(searchError || qaError) && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {searchError || qaError}
        </Alert>
      )}

      {/* Q&A Response */}
      {qaResponse && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <QAIcon color="primary" />
            AI Answer
            <Chip
              label={`${qaResponse.confidence_score?.toFixed(2) || 'N/A'} confidence`}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Typography>

          <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7 }}>
            {qaResponse.answer}
          </Typography>

          {qaResponse.citations.length > 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Citations ({qaResponse.citations.length})
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {qaResponse.citations.map((citation, index) => (
                  <Card
                    key={index}
                    variant="outlined"
                    sx={{
                      cursor: 'pointer',
                      '&:hover': { backgroundColor: 'action.hover' }
                    }}
                    onClick={() => handleCitationClick(citation)}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography variant="subtitle2" color="primary">
                          [{citation.citation_order}] {citation.book_title}
                        </Typography>
                        <Chip
                          label={`${(citation.relevance_score * 100).toFixed(1)}%`}
                          size="small"
                          color="secondary"
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {citation.book_authors.join(', ')} • Page {citation.page_number}
                        {citation.chapter_title && ` • ${citation.chapter_title}`}
                      </Typography>
                      <Typography variant="body2">
                        {citation.cited_text}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </Box>
          )}

          <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Generated in {qaResponse.response_time_ms}ms using {qaResponse.model_used}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {qaResponse.context_chunks_used} context chunks used
            </Typography>
          </Box>
        </Paper>
      )}

      {/* Search Results */}
      {searchResults && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <BookIcon color="primary" />
              Search Results ({searchResults.total_results})
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Found in {searchResults.search_time_ms}ms
            </Typography>
          </Box>

          {searchResults.results.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No results found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Try adjusting your search terms or filters
              </Typography>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {searchResults.results.map((result) => (
                <Card key={result.chunk_id} variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" color="primary" gutterBottom>
                          {result.book_title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {result.book_authors.join(', ')}
                          {result.book_publisher && ` • ${result.book_publisher}`}
                          {result.book_publication_year && ` • ${result.book_publication_year}`}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Page {result.page_number}
                          {result.chapter_title && ` • ${result.chapter_title}`}
                          {result.topic_category && (
                            <Chip
                              label={result.topic_category}
                              size="small"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Tooltip title="Relevance Score">
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <StarIcon color="primary" fontSize="small" />
                            <Typography variant="body2" color="primary" sx={{ fontWeight: 'bold' }}>
                              {(result.score * 100).toFixed(1)}%
                            </Typography>
                          </Box>
                        </Tooltip>
                      </Box>
                    </Box>

                    <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                      {result.content}
                    </Typography>

                    {result.medical_topics.length > 0 && (
                      <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {result.medical_topics.slice(0, 5).map((topic, topicIndex) => (
                          <Chip
                            key={topicIndex}
                            label={topic}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                        {result.medical_topics.length > 5 && (
                          <Chip
                            label={`+${result.medical_topics.length - 5} more`}
                            size="small"
                            variant="outlined"
                            color="secondary"
                          />
                        )}
                      </Box>
                    )}

                    <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {result.word_count} words • {result.char_count} characters
                      </Typography>
                      <Button size="small" variant="outlined">
                        View Context
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </Paper>
      )}

      {/* Empty State */}
      {!searchResults && !qaResponse && !isSearching && !isGeneratingAnswer && (
        <Paper sx={{ p: 6, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Start your medical research
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Search through medical literature or ask AI-powered questions to get started
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button variant="outlined" startIcon={<SearchIcon />}>
              Try searching for "diabetes"
            </Button>
            <Button variant="outlined" startIcon={<QAIcon />}>
              Ask "What causes hypertension?"
            </Button>
          </Box>
        </Paper>
      )}

      {/* Citation Viewer Dialog */}
      <CitationViewer
        citation={selectedCitation}
        open={citationViewerOpen}
        onClose={handleCloseCitationViewer}
      />
    </Box>
  );
};

export default SearchPage;
