import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  NetworkCheck as NetworkIcon,
  Computer as ComputerIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { adminService } from '../../services/adminService';

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  database: {
    status: 'connected' | 'disconnected';
    response_time: number;
  };
  vector_db: {
    status: 'connected' | 'disconnected';
    response_time: number;
  };
  storage: {
    available_space: number;
    total_space: number;
    usage_percentage: number;
  };
  services: {
    embedding_service: boolean;
    llm_service: boolean;
    pdf_service: boolean;
  };
}

interface ProcessingStatus {
  book_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

const SystemMonitoring: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchSystemData = async () => {
    try {
      setLoading(true);
      const [healthData, processingData] = await Promise.all([
        adminService.getSystemHealth(),
        adminService.getProcessingStatus(),
      ]);
      
      setSystemHealth(healthData);
      setProcessingStatus(processingData);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      setError('Failed to load system data');
      console.error('System monitoring error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemData();
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'healthy':
        return <CheckCircleIcon sx={{ color: 'success.main' }} />;
      case 'warning':
        return <WarningIcon sx={{ color: 'warning.main' }} />;
      case 'disconnected':
      case 'critical':
        return <ErrorIcon sx={{ color: 'error.main' }} />;
      default:
        return <WarningIcon sx={{ color: 'grey.500' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'healthy':
      case 'completed':
        return 'success';
      case 'warning':
      case 'processing':
        return 'warning';
      case 'disconnected':
      case 'critical':
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatResponseTime = (ms: number) => {
    return `${ms}ms`;
  };

  const ServiceStatusCard: React.FC<{
    title: string;
    status: boolean;
    icon: React.ReactNode;
    description: string;
  }> = ({ title, status, icon, description }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Avatar sx={{ mr: 2, backgroundColor: status ? 'success.main' : 'error.main' }}>
            {icon}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6">{title}</Typography>
            <Typography variant="body2" color="textSecondary">
              {description}
            </Typography>
          </Box>
          <Chip
            label={status ? 'Online' : 'Offline'}
            color={status ? 'success' : 'error'}
            size="small"
          />
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          System Monitoring
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body2" color="textSecondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
          <IconButton onClick={fetchSystemData} color="primary">
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {/* System Health Alert */}
      {systemHealth && systemHealth.status !== 'healthy' && (
        <Alert
          severity={systemHealth.status === 'warning' ? 'warning' : 'error'}
          sx={{ mb: 3 }}
        >
          System status: {systemHealth.status}. Please check the components below for details.
        </Alert>
      )}

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* System Overview */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Database Connection
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {getStatusIcon(systemHealth?.database.status || 'disconnected')}
              <Box sx={{ ml: 2, flexGrow: 1 }}>
                <Typography variant="body1">
                  PostgreSQL Database
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Response time: {formatResponseTime(systemHealth?.database.response_time || 0)}
                </Typography>
              </Box>
              <Chip
                label={systemHealth?.database.status || 'Unknown'}
                color={getStatusColor(systemHealth?.database.status || 'disconnected') as any}
                size="small"
              />
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Vector Database
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {getStatusIcon(systemHealth?.vector_db.status || 'disconnected')}
              <Box sx={{ ml: 2, flexGrow: 1 }}>
                <Typography variant="body1">
                  Qdrant Vector DB
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Response time: {formatResponseTime(systemHealth?.vector_db.response_time || 0)}
                </Typography>
              </Box>
              <Chip
                label={systemHealth?.vector_db.status || 'Unknown'}
                color={getStatusColor(systemHealth?.vector_db.status || 'disconnected') as any}
                size="small"
              />
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Storage Information */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Storage Usage
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  Used: {formatBytes((systemHealth?.storage.total_space || 0) - (systemHealth?.storage.available_space || 0))}
                </Typography>
                <Typography variant="body2">
                  Available: {formatBytes(systemHealth?.storage.available_space || 0)}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={systemHealth?.storage.usage_percentage || 0}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  '& .MuiLinearProgress-bar': {
                    backgroundColor:
                      (systemHealth?.storage.usage_percentage || 0) > 80
                        ? 'error.main'
                        : (systemHealth?.storage.usage_percentage || 0) > 60
                          ? 'warning.main'
                          : 'success.main',
                  },
                }}
              />
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                {systemHealth?.storage.usage_percentage || 0}% used of {formatBytes(systemHealth?.storage.total_space || 0)}
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              System Services
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  {getStatusIcon(systemHealth?.services.embedding_service ? 'connected' : 'disconnected')}
                </ListItemIcon>
                <ListItemText
                  primary="Embedding Service"
                  secondary="Text embedding generation"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {getStatusIcon(systemHealth?.services.llm_service ? 'connected' : 'disconnected')}
                </ListItemIcon>
                <ListItemText
                  primary="LLM Service"
                  secondary="Language model processing"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  {getStatusIcon(systemHealth?.services.pdf_service ? 'connected' : 'disconnected')}
                </ListItemIcon>
                <ListItemText
                  primary="PDF Service"
                  secondary="Document processing"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </Box>

      {/* Processing Status */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Processing Queue
          </Typography>
          {processingStatus.length === 0 ? (
            <Typography variant="body2" color="textSecondary">
              No active processing tasks
            </Typography>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Book ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Message</TableCell>
                    <TableCell>Started</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {processingStatus.map((item) => (
                    <TableRow key={item.book_id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {item.book_id.slice(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={item.status}
                          color={getStatusColor(item.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={item.progress}
                            sx={{ flexGrow: 1, height: 6 }}
                          />
                          <Typography variant="body2">
                            {item.progress}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {item.message}
                        </Typography>
                        {item.error_message && (
                          <Typography variant="caption" color="error">
                            {item.error_message}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(item.started_at).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Service Status Cards */}
      <Typography variant="h6" gutterBottom>
        Service Status
      </Typography>
      <Grid container spacing={2}>
        <Grid xs={12} sm={6} md={3}>
          <ServiceStatusCard
            title="Embedding Service"
            status={systemHealth?.services.embedding_service || false}
            icon={<NetworkIcon />}
            description="Text vectorization"
          />
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <ServiceStatusCard
            title="LLM Service"
            status={systemHealth?.services.llm_service || false}
            icon={<ComputerIcon />}
            description="AI question answering"
          />
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <ServiceStatusCard
            title="PDF Service"
            status={systemHealth?.services.pdf_service || false}
            icon={<StorageIcon />}
            description="Document processing"
          />
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <ServiceStatusCard
            title="Security"
            status={true}
            icon={<SecurityIcon />}
            description="Authentication & authorization"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemMonitoring;
