import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  InsertDriveFile as FileIcon,
  ArrowBack as BackIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { adminService } from '../../services/adminService';

interface BookUploadData {
  title: string;
  authors: string;
  isbn?: string;
  publisher?: string;
  publication_year?: number;
  edition?: string;
  description?: string;
  tags?: string;
  language: string;
}

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

const BookUpload: React.FC = () => {
  const navigate = useNavigate();
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [formData, setFormData] = useState<BookUploadData>({
    title: '',
    authors: '',
    isbn: '',
    publisher: '',
    publication_year: undefined,
    edition: '',
    description: '',
    tags: '',
    language: 'en',
  });
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const newFiles: UploadFile[] = selectedFiles
      .filter(file => file.type === 'application/pdf')
      .map(file => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: 'pending',
      }));

    if (selectedFiles.length > newFiles.length) {
      setError('Only PDF files are allowed');
    } else {
      setError(null);
    }

    setFiles(prev => [...prev, ...newFiles]);
  };

  const handleFileRemove = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const handleInputChange = (field: keyof BookUploadData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: field === 'publication_year' ? (value ? parseInt(value) : undefined) : value,
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      setError('Title is required');
      return false;
    }
    if (!formData.authors.trim()) {
      setError('Authors are required');
      return false;
    }
    if (files.length === 0) {
      setError('At least one PDF file is required');
      return false;
    }
    return true;
  };

  const handleUpload = async () => {
    if (!validateForm()) return;

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      for (const fileItem of files) {
        if (fileItem.status === 'completed') continue;

        // Update file status to uploading
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
        ));

        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setFiles(prev => prev.map(f => 
              f.id === fileItem.id && f.progress < 90 
                ? { ...f, progress: f.progress + 10 } 
                : f
            ));
          }, 200);

          const result = await adminService.uploadBook(fileItem.file, formData);

          clearInterval(progressInterval);

          // Update file status to completed
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'completed', progress: 100 } 
              : f
          ));

          setSuccess(`Book "${formData.title}" uploaded successfully!`);
        } catch (err: any) {
          // Update file status to error
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id 
              ? { ...f, status: 'error', error: err.message } 
              : f
          ));
          throw err;
        }
      }

      // Reset form after successful upload
      setTimeout(() => {
        setFormData({
          title: '',
          authors: '',
          isbn: '',
          publisher: '',
          publication_year: undefined,
          edition: '',
          description: '',
          tags: '',
          language: 'en',
        });
        setFiles([]);
      }, 2000);

    } catch (err: any) {
      setError(err.message || 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const getFileStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'uploading':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => navigate('/admin/books')} sx={{ mr: 1 }}>
          <BackIcon />
        </IconButton>
        <Typography variant="h4">
          Upload Medical Book
        </Typography>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3
      }}>
        {/* File Upload Section */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Upload PDF Files
            </Typography>

            {/* File Drop Zone */}
            <Paper
              sx={{
                border: '2px dashed',
                borderColor: 'primary.main',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
              onClick={() => document.getElementById('file-input')?.click()}
            >
              <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Drop PDF files here or click to browse
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Only PDF files are supported
              </Typography>
              <input
                id="file-input"
                type="file"
                multiple
                accept=".pdf"
                style={{ display: 'none' }}
                onChange={handleFileSelect}
              />
            </Paper>

              {/* File List */}
              {files.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Selected Files ({files.length})
                  </Typography>
                  <List dense>
                    {files.map((fileItem) => (
                      <ListItem key={fileItem.id} divider>
                        <FileIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <ListItemText
                          primary={fileItem.file.name}
                          secondary={
                            <>
                              <Typography variant="caption" component="div">
                                {formatFileSize(fileItem.file.size)}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                                <Chip
                                  label={fileItem.status}
                                  size="small"
                                  color={getFileStatusColor(fileItem.status) as any}
                                />
                                {fileItem.status === 'uploading' && (
                                  <LinearProgress
                                    variant="determinate"
                                    value={fileItem.progress}
                                    sx={{ flexGrow: 1, height: 4 }}
                                  />
                                )}
                              </Box>
                              {fileItem.error && (
                                <Typography variant="caption" color="error" component="div">
                                  {fileItem.error}
                                </Typography>
                              )}
                            </>
                          }
                          secondaryTypographyProps={{ component: 'div' }}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            onClick={() => handleFileRemove(fileItem.id)}
                            disabled={fileItem.status === 'uploading'}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>

        {/* Book Information Form */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Book Information
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                fullWidth
                label="Title *"
                value={formData.title}
                onChange={handleInputChange('title')}
                required
              />
              <TextField
                fullWidth
                label="Authors *"
                value={formData.authors}
                onChange={handleInputChange('authors')}
                placeholder="Comma-separated list of authors"
                required
              />
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>
                <TextField
                  fullWidth
                  label="ISBN"
                  value={formData.isbn}
                  onChange={handleInputChange('isbn')}
                />
                <TextField
                  fullWidth
                  label="Publication Year"
                  type="number"
                  value={formData.publication_year || ''}
                  onChange={handleInputChange('publication_year')}
                />
              </Box>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' }, gap: 2 }}>
                <TextField
                  fullWidth
                  label="Publisher"
                  value={formData.publisher}
                  onChange={handleInputChange('publisher')}
                />
                <TextField
                  fullWidth
                  label="Edition"
                  value={formData.edition}
                  onChange={handleInputChange('edition')}
                />
              </Box>
              <TextField
                fullWidth
                label="Tags"
                value={formData.tags}
                onChange={handleInputChange('tags')}
                placeholder="Comma-separated tags (e.g., anatomy, cardiology, textbook)"
              />
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={handleInputChange('description')}
                multiline
                rows={3}
                placeholder="Brief description of the book content"
              />
            </Box>

            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={handleUpload}
                disabled={uploading || files.length === 0}
                startIcon={<UploadIcon />}
                fullWidth
              >
                {uploading ? 'Uploading...' : 'Upload Book'}
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/admin/books')}
                disabled={uploading}
              >
                Cancel
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default BookUpload;
