import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  Search as SearchIcon,
  QuestionAnswer as QAIcon,
  Book as BookIcon,
  People as PeopleIcon,
} from '@mui/icons-material';

interface AnalyticsData {
  period: string;
  totalSearches: number;
  totalQuestions: number;
  totalUsers: number;
  totalBooks: number;
  searchTrend: number;
  questionTrend: number;
  userTrend: number;
  popularBooks: Array<{
    title: string;
    authors: string;
    searchCount: number;
  }>;
  popularQueries: Array<{
    query: string;
    count: number;
  }>;
  userActivity: Array<{
    date: string;
    searches: number;
    questions: number;
  }>;
}

const Analytics: React.FC = () => {
  const [period, setPeriod] = useState('week');
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData | null>(null);

  useEffect(() => {
    // Simulate loading analytics data
    setLoading(true);
    setTimeout(() => {
      setData({
        period,
        totalSearches: 3421,
        totalQuestions: 892,
        totalUsers: 1247,
        totalBooks: 156,
        searchTrend: 12.5,
        questionTrend: 8.3,
        userTrend: 15.2,
        popularBooks: [
          { title: "Gray's Anatomy", authors: "Henry Gray", searchCount: 245 },
          { title: "Harrison's Principles", authors: "Dennis Kasper", searchCount: 198 },
          { title: "Robbins Basic Pathology", authors: "Vinay Kumar", searchCount: 167 },
          { title: "Netter's Atlas", authors: "Frank Netter", searchCount: 134 },
          { title: "First Aid USMLE", authors: "Tao Le", searchCount: 112 },
        ],
        popularQueries: [
          { query: "cardiac anatomy", count: 89 },
          { query: "diabetes pathophysiology", count: 76 },
          { query: "respiratory system", count: 65 },
          { query: "neurological disorders", count: 54 },
          { query: "pharmacology basics", count: 43 },
        ],
        userActivity: [
          { date: "2024-01-01", searches: 45, questions: 12 },
          { date: "2024-01-02", searches: 52, questions: 15 },
          { date: "2024-01-03", searches: 38, questions: 9 },
          { date: "2024-01-04", searches: 61, questions: 18 },
          { date: "2024-01-05", searches: 47, questions: 13 },
        ],
      });
      setLoading(false);
    }, 1000);
  }, [period]);

  const MetricCard: React.FC<{
    title: string;
    value: number;
    trend: number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, trend, icon, color }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              backgroundColor: color,
              borderRadius: '50%',
              p: 1,
              mr: 2,
              color: 'white',
            }}
          >
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value.toLocaleString()}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TrendingUp sx={{ color: 'success.main', mr: 0.5, fontSize: 16 }} />
          <Typography
            variant="body2"
            sx={{ color: 'success.main', fontWeight: 'medium' }}
          >
            +{trend}% from last {period}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Analytics
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Analytics Dashboard
        </Typography>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Period</InputLabel>
          <Select
            value={period}
            label="Period"
            onChange={(e) => setPeriod(e.target.value)}
          >
            <MenuItem value="day">Today</MenuItem>
            <MenuItem value="week">This Week</MenuItem>
            <MenuItem value="month">This Month</MenuItem>
            <MenuItem value="year">This Year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Metrics Cards */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <MetricCard
          title="Total Searches"
          value={data?.totalSearches || 0}
          trend={data?.searchTrend || 0}
          icon={<SearchIcon />}
          color="#1976d2"
        />
        <MetricCard
          title="Q&A Sessions"
          value={data?.totalQuestions || 0}
          trend={data?.questionTrend || 0}
          icon={<QAIcon />}
          color="#388e3c"
        />
        <MetricCard
          title="Active Users"
          value={data?.totalUsers || 0}
          trend={data?.userTrend || 0}
          icon={<PeopleIcon />}
          color="#f57c00"
        />
        <MetricCard
          title="Total Books"
          value={data?.totalBooks || 0}
          trend={5.2}
          icon={<BookIcon />}
          color="#7b1fa2"
        />
      </Box>

      {/* Popular Content */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3
      }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Most Searched Books
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Book</TableCell>
                    <TableCell align="right">Searches</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data?.popularBooks.map((book, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {book.title}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {book.authors}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Chip
                          label={book.searchCount}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Popular Search Queries
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Query</TableCell>
                    <TableCell align="right">Count</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data?.popularQueries.map((query, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          "{query.query}"
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Chip
                          label={query.count}
                          size="small"
                          color="secondary"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default Analytics;
