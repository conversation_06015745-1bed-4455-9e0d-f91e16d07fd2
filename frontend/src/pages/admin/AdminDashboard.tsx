import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Button,
  Alert,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Book as BookIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Storage as StorageIcon,
  CloudUpload as UploadIcon,
  Search as SearchIcon,
  QuestionAnswer as QAIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { adminService } from '../../services/adminService';

interface SystemStats {
  totalBooks: number;
  totalUsers: number;
  totalSearches: number;
  totalQuestions: number;
  storageUsed: number;
  storageLimit: number;
  activeProcessing: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  recentActivity: ActivityItem[];
  popularBooks: BookItem[];
  userGrowth: number;
  searchGrowth: number;
}

interface ActivityItem {
  id: string;
  type: 'upload' | 'search' | 'question' | 'user_signup';
  description: string;
  timestamp: string;
  user?: string;
}

interface BookItem {
  id: string;
  title: string;
  authors: string;
  searchCount: number;
  uploadDate: string;
}

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const data = await adminService.getSystemStats();
      setStats(data);
      setError(null);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    // Refresh every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    trend?: number;
    subtitle?: string;
  }> = ({ title, value, icon, color, trend, subtitle }) => (
    <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              backgroundColor: color,
              width: 56,
              height: 56,
              mr: 2,
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
        {trend !== undefined && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {trend > 0 ? (
              <TrendingUp sx={{ color: 'success.main', mr: 0.5 }} />
            ) : (
              <TrendingDown sx={{ color: 'error.main', mr: 0.5 }} />
            )}
            <Typography
              variant="body2"
              sx={{
                color: trend > 0 ? 'success.main' : 'error.main',
                fontWeight: 'medium',
              }}
            >
              {Math.abs(trend)}% from last month
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={fetchStats}>
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Box>
    );
  }

  const storagePercentage = stats ? (stats.storageUsed / stats.storageLimit) * 100 : 0;

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Dashboard Overview
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={fetchStats} color="primary">
            <RefreshIcon />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => navigate('/admin/books/upload')}
          >
            Upload Book
          </Button>
        </Box>
      </Box>

      {/* System Health Alert */}
      {stats?.systemHealth !== 'healthy' && (
        <Alert
          severity={stats?.systemHealth === 'warning' ? 'warning' : 'error'}
          sx={{ mb: 3 }}
          icon={<WarningIcon />}
        >
          System health status: {stats?.systemHealth}. Please check system monitoring.
        </Alert>
      )}

      {/* Stats Cards */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <StatCard
          title="Total Books"
          value={stats?.totalBooks || 0}
          icon={<BookIcon />}
          color="#1976d2"
          subtitle="Medical textbooks"
        />
        <StatCard
          title="Total Users"
          value={stats?.totalUsers || 0}
          icon={<PeopleIcon />}
          color="#388e3c"
          trend={stats?.userGrowth}
        />
        <StatCard
          title="Searches Today"
          value={stats?.totalSearches || 0}
          icon={<SearchIcon />}
          color="#f57c00"
          trend={stats?.searchGrowth}
        />
        <StatCard
          title="Q&A Sessions"
          value={stats?.totalQuestions || 0}
          icon={<QAIcon />}
          color="#7b1fa2"
          subtitle="This month"
        />
      </Box>

      {/* Storage and Processing */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3,
        mb: 4
      }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Storage Usage
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <StorageIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="body1">
                {((stats?.storageUsed || 0) / 1024 / 1024 / 1024).toFixed(2)} GB / {((stats?.storageLimit || 0) / 1024 / 1024 / 1024).toFixed(2)} GB
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={storagePercentage}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: storagePercentage > 80 ? 'error.main' : 'primary.main',
                },
              }}
            />
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              {storagePercentage.toFixed(1)}% used
            </Typography>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Processing Status
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="body1">
                {stats?.activeProcessing || 0} books currently processing
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label={`${stats?.activeProcessing || 0} Active`}
                color={stats?.activeProcessing ? 'warning' : 'success'}
                size="small"
              />
              <Chip
                label="System Healthy"
                color={stats?.systemHealth === 'healthy' ? 'success' : 'error'}
                size="small"
              />
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Recent Activity and Popular Books */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
        gap: 3
      }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Activity</TableCell>
                    <TableCell>Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(stats?.recentActivity || []).slice(0, 5).map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <Typography variant="body2">
                          {activity.description}
                        </Typography>
                        {activity.user && (
                          <Typography variant="caption" color="textSecondary">
                            by {activity.user}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {new Date(activity.timestamp).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Popular Books
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Book</TableCell>
                    <TableCell align="right">Searches</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(stats?.popularBooks || []).slice(0, 5).map((book) => (
                    <TableRow key={book.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {book.title}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {book.authors}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Chip
                          label={book.searchCount}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
