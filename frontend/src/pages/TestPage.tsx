import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>po<PERSON>, Button, Al<PERSON>, <PERSON>, CardContent } from '@mui/material';
import { adminService } from '../services/adminService';
import { apiClient } from '../services/apiClient';

const TestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results: any[] = [];

    // Test 1: Check auth token
    const token = localStorage.getItem('token');
    results.push({
      test: 'Auth Token Check',
      status: token ? 'PASS' : 'FAIL',
      details: token ? `Token exists (${token.length} chars)` : 'No token found'
    });

    // Test 2: Test admin stats API
    try {
      console.log('🧪 Testing admin stats...');
      const stats = await adminService.getSystemStats();
      results.push({
        test: 'Admin Stats API',
        status: 'PASS',
        details: `Books: ${stats.totalBooks}, Users: ${stats.totalUsers}`
      });
    } catch (error: any) {
      results.push({
        test: 'Admin Stats API',
        status: 'FAIL',
        details: error.message
      });
    }

    // Test 3: Test users API
    try {
      console.log('🧪 Testing users API...');
      const users = await adminService.getUsers(1, 5);
      results.push({
        test: 'Users API',
        status: 'PASS',
        details: `Found ${users.total} users, showing ${users.users.length}`
      });
    } catch (error: any) {
      results.push({
        test: 'Users API',
        status: 'FAIL',
        details: error.message
      });
    }

    // Test 4: Test direct API call
    try {
      console.log('🧪 Testing direct API call...');
      const response = await apiClient.get('/admin/stats');
      results.push({
        test: 'Direct API Call',
        status: 'PASS',
        details: `Response received: ${JSON.stringify(response).substring(0, 100)}...`
      });
    } catch (error: any) {
      results.push({
        test: 'Direct API Call',
        status: 'FAIL',
        details: error.message
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Frontend API Test Page
      </Typography>
      
      <Button 
        variant="contained" 
        onClick={runTests} 
        disabled={loading}
        sx={{ mb: 3 }}
      >
        {loading ? 'Running Tests...' : 'Run Tests'}
      </Button>

      {testResults.map((result, index) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" color={result.status === 'PASS' ? 'success.main' : 'error.main'}>
              {result.status === 'PASS' ? '✅' : '❌'} {result.test}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {result.details}
            </Typography>
          </CardContent>
        </Card>
      ))}

      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>How to use this test page:</strong><br/>
          1. Make sure you're logged in as an admin user<br/>
          2. Click "Run Tests" to test all API endpoints<br/>
          3. Check the results to see what's working and what's not<br/>
          4. Open browser console (F12) to see detailed logs
        </Typography>
      </Alert>
    </Box>
  );
};

export default TestPage;
