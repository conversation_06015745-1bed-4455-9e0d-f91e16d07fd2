import { apiClient } from './apiClient';

export interface SystemStats {
  totalBooks: number;
  totalUsers: number;
  totalSearches: number;
  totalQuestions: number;
  storageUsed: number;
  storageLimit: number;
  activeProcessing: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  recentActivity: ActivityItem[];
  popularBooks: BookItem[];
  userGrowth: number;
  searchGrowth: number;
}

export interface ActivityItem {
  id: string;
  type: 'upload' | 'search' | 'question' | 'user_signup';
  description: string;
  timestamp: string;
  user?: string;
}

export interface BookItem {
  id: string;
  title: string;
  authors: string;
  searchCount: number;
  uploadDate: string;
  status: 'processing' | 'completed' | 'failed';
  fileSize: number;
  language: string;
  tags: string[];
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  role: 'USER' | 'ADMIN';
  created_at: string;
  last_login: string;
  is_active: boolean;
}

export interface BookUploadData {
  title: string;
  authors: string;
  isbn?: string;
  publisher?: string;
  publication_year?: number;
  edition?: string;
  description?: string;
  tags?: string;
  language: string;
}

export interface ProcessingStatus {
  book_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  database: {
    status: 'connected' | 'disconnected';
    response_time: number;
  };
  vector_db: {
    status: 'connected' | 'disconnected';
    response_time: number;
  };
  storage: {
    available_space: number;
    total_space: number;
    usage_percentage: number;
  };
  services: {
    embedding_service: boolean;
    llm_service: boolean;
    pdf_service: boolean;
  };
}

class AdminService {
  // System Stats and Health
  async getSystemStats(): Promise<SystemStats> {
    try {
      const response = await apiClient.get('/admin/system/stats');
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        totalBooks: 156,
        totalUsers: 1247,
        totalSearches: 3421,
        totalQuestions: 892,
        storageUsed: 15 * 1024 * 1024 * 1024, // 15GB
        storageLimit: 100 * 1024 * 1024 * 1024, // 100GB
        activeProcessing: 2,
        systemHealth: 'healthy',
        userGrowth: 12.5,
        searchGrowth: 8.3,
        recentActivity: [
          {
            id: '1',
            type: 'upload',
            description: 'New book uploaded: "Medical Anatomy"',
            timestamp: new Date().toISOString(),
            user: '<EMAIL>',
          },
          {
            id: '2',
            type: 'search',
            description: 'Search performed: "cardiac anatomy"',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            user: '<EMAIL>',
          },
        ],
        popularBooks: [
          {
            id: '1',
            title: 'Gray\'s Anatomy',
            authors: 'Henry Gray',
            searchCount: 245,
            uploadDate: '2024-01-15',
            status: 'completed',
            fileSize: 50 * 1024 * 1024,
            language: 'en',
            tags: ['anatomy', 'medical'],
          },
          {
            id: '2',
            title: 'Harrison\'s Principles of Internal Medicine',
            authors: 'Dennis Kasper',
            searchCount: 198,
            uploadDate: '2024-01-20',
            status: 'completed',
            fileSize: 75 * 1024 * 1024,
            language: 'en',
            tags: ['internal medicine'],
          },
        ],
      };
    }
  }

  async getSystemHealth(): Promise<SystemHealth> {
    try {
      const response = await apiClient.get('/admin/system/health');
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        status: 'healthy',
        database: {
          status: 'connected',
          response_time: 45,
        },
        vector_db: {
          status: 'disconnected',
          response_time: 0,
        },
        storage: {
          available_space: 85 * 1024 * 1024 * 1024,
          total_space: 100 * 1024 * 1024 * 1024,
          usage_percentage: 15,
        },
        services: {
          embedding_service: false,
          llm_service: false,
          pdf_service: true,
        },
      };
    }
  }

  // Book Management
  async getBooks(page: number = 1, limit: number = 10, search?: string): Promise<{
    books: BookItem[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
      });
      const response = await apiClient.get(`/admin/books?${params}`);
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        books: [
          {
            id: '1',
            title: 'Gray\'s Anatomy',
            authors: 'Henry Gray',
            searchCount: 245,
            uploadDate: '2024-01-15',
            status: 'completed',
            fileSize: 50 * 1024 * 1024,
            language: 'en',
            tags: ['anatomy', 'medical', 'textbook'],
          },
          {
            id: '2',
            title: 'Harrison\'s Principles of Internal Medicine',
            authors: 'Dennis Kasper',
            searchCount: 198,
            uploadDate: '2024-01-20',
            status: 'processing',
            fileSize: 75 * 1024 * 1024,
            language: 'en',
            tags: ['internal medicine', 'diagnosis'],
          },
        ],
        total: 156,
        page: 1,
        totalPages: 16,
      };
    }
  }

  async uploadBook(file: File, data: BookUploadData): Promise<{ book_id: string; message: string }> {
    const formData = new FormData();
    formData.append('file', file);
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value.toString());
      }
    });

    const response = await apiClient.post('/admin/books/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteBook(bookId: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/admin/books/${bookId}`);
    return response.data;
  }

  async updateBook(bookId: string, data: Partial<BookUploadData>): Promise<BookItem> {
    const response = await apiClient.put(`/admin/books/${bookId}`, data);
    return response.data;
  }

  async getProcessingStatus(): Promise<ProcessingStatus[]> {
    try {
      const response = await apiClient.get('/admin/processing/status');
      return response.data;
    } catch (error) {
      // Return mock data for development
      return [
        {
          book_id: '2',
          status: 'processing',
          progress: 65,
          message: 'Extracting text from PDF...',
          started_at: new Date().toISOString(),
        },
        {
          book_id: '3',
          status: 'pending',
          progress: 0,
          message: 'Waiting in queue...',
          started_at: new Date().toISOString(),
        },
      ];
    }
  }

  async reprocessBook(bookId: string): Promise<{ message: string }> {
    const response = await apiClient.post(`/admin/processing/reprocess/${bookId}`);
    return response.data;
  }

  async cancelProcessing(bookId: string): Promise<{ message: string }> {
    const response = await apiClient.post(`/admin/processing/cancel/${bookId}`);
    return response.data;
  }

  // User Management
  async getUsers(page: number = 1, limit: number = 10, search?: string): Promise<{
    users: User[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
      });
      const response = await apiClient.get(`/admin/users?${params}`);
      return response.data;
    } catch (error) {
      // Return mock data for development
      return {
        users: [
          {
            id: '1',
            email: '<EMAIL>',
            full_name: 'Admin User',
            role: 'ADMIN',
            created_at: '2024-01-01T00:00:00Z',
            last_login: new Date().toISOString(),
            is_active: true,
          },
          {
            id: '2',
            email: '<EMAIL>',
            full_name: 'Test User',
            role: 'USER',
            created_at: '2024-01-15T00:00:00Z',
            last_login: new Date(Date.now() - 86400000).toISOString(),
            is_active: true,
          },
        ],
        total: 1247,
        page: 1,
        totalPages: 125,
      };
    }
  }

  async updateUserRole(userId: string, role: 'USER' | 'ADMIN'): Promise<User> {
    const response = await apiClient.put(`/admin/users/${userId}/role`, { role });
    return response.data;
  }

  async toggleUserStatus(userId: string): Promise<User> {
    const response = await apiClient.put(`/admin/users/${userId}/toggle-status`);
    return response.data;
  }
}

export const adminService = new AdminService();
