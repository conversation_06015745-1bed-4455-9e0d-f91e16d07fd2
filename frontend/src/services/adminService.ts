/**
 * Admin service for API calls
 */
import { apiClient } from './apiClient';
import {
  Book,
  BookUploadRequest,
  SystemStats,
  ProcessingStatusInfo,
  PaginatedResponse,
  ProcessingStatus
} from '../types';

class AdminService {

  async uploadBook(
    file: File,
    bookData: BookUploadRequest,
    onProgress?: (progress: number) => void
  ): Promise<{ book_id: string; title: string; processing_status: ProcessingStatus; message: string }> {
    const uploadData = {
      title: bookData.title,
      authors: bookData.authors.join(', '),
      ...(bookData.isbn && { isbn: bookData.isbn }),
      ...(bookData.publisher && { publisher: bookData.publisher }),
      ...(bookData.publication_year && { publication_year: bookData.publication_year.toString() }),
      ...(bookData.edition && { edition: bookData.edition }),
      ...(bookData.description && { description: bookData.description }),
      ...(bookData.tags && { tags: bookData.tags.join(', ') }),
      ...(bookData.language && { language: bookData.language }),
    };

    return apiClient.uploadFile('/admin/books/upload', file, uploadData, onProgress);
  }

  async getBooks(
    page: number = 1,
    pageSize: number = 20,
    statusFilter?: ProcessingStatus,
    search?: string
  ): Promise<PaginatedResponse<Book>> {
    const params: any = { page, page_size: pageSize };
    if (statusFilter) params.status_filter = statusFilter;
    if (search) params.search = search;

    return apiClient.get<PaginatedResponse<Book>>('/admin/books', { params });
  }

  async getBook(bookId: string): Promise<Book> {
    return apiClient.get<Book>(`/admin/books/${bookId}`);
  }

  async updateBook(bookId: string, bookData: Partial<BookUploadRequest>): Promise<Book> {
    return apiClient.put<Book>(`/admin/books/${bookId}`, bookData);
  }

  async deleteBook(bookId: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`/admin/books/${bookId}`);
  }

  async processBook(bookId: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`/admin/books/${bookId}/process`);
  }

  async reindexBook(bookId: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`/admin/books/${bookId}/reindex`);
  }

  async getProcessingStatus(bookId: string): Promise<ProcessingStatusInfo> {
    return apiClient.get<ProcessingStatusInfo>(`/admin/books/${bookId}/status`);
  }

  async getSystemStats(): Promise<SystemStats> {
    return apiClient.get<SystemStats>('/admin/stats');
  }

  async performBulkAction(
    action: string,
    itemIds: string[],
    confirm: boolean = false
  ): Promise<{
    action: string;
    total_items: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    return apiClient.post('/admin/bulk-actions', {
      action,
      item_ids: itemIds,
      confirm,
    });
  }

  async getSystemHealth(): Promise<any> {
    return apiClient.get('/admin/health');
  }

  async getUsers(
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<any>> {
    const response = await apiClient.get<any[]>('/users', {
      params: { skip: (page - 1) * pageSize, limit: pageSize },
    });
    return {
      items: response,
      total: response.length, // This would need to be updated based on actual API response
      page,
      page_size: pageSize,
    };
  }

  async activateUser(userId: string): Promise<{ message: string }> {
    return apiClient.put<{ message: string }>(`/users/${userId}/activate`);
  }

  async deactivateUser(userId: string): Promise<{ message: string }> {
    return apiClient.put<{ message: string }>(`/users/${userId}/deactivate`);
  }

  async verifyUser(userId: string): Promise<{ message: string }> {
    return apiClient.put<{ message: string }>(`/users/${userId}/verify`);
  }
}

export const adminService = new AdminService();
