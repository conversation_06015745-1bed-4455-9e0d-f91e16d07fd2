/**
 * Base API client with error handling, loading states, and type safety
 */
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ApiResponse } from '../types';

// Extend Axios types to include custom properties
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    skipAuth?: boolean;
    metadata?: { startTime: number };
  }
}

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

export interface RequestOptions extends AxiosRequestConfig {
  skipAuth?: boolean;
  retries?: number;
}

export class ApiClient {
  private api: AxiosInstance;
  private config: ApiClientConfig;

  constructor(config: ApiClientConfig = {}) {
    this.config = {
      baseURL: `${API_URL}/api/v1`,
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      ...config,
    };

    this.api = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add auth token if available and not skipped
        const token = localStorage.getItem('token');
        if (token && !config.skipAuth) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for debugging
        config.metadata = { startTime: Date.now() };

        return config;
      },
      (error) => {
        return Promise.reject(this.handleError(error));
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        // Log response time for debugging
        const duration = Date.now() - response.config.metadata?.startTime;
        console.debug(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`);

        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          // Clear invalid token
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          
          // Redirect to login if not already there
          if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
          }
          
          return Promise.reject(this.handleError(error));
        }

        // Handle retry logic for network errors
        if (this.shouldRetry(error) && !originalRequest._retry) {
          const retries = originalRequest.retries || this.config.retries || 0;
          
          if (retries > 0) {
            originalRequest._retry = true;
            originalRequest.retries = retries - 1;
            
            // Wait before retrying
            await this.delay(this.config.retryDelay || 1000);
            
            return this.api(originalRequest);
          }
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  private shouldRetry(error: any): boolean {
    // Retry on network errors or 5xx server errors
    return (
      !error.response ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT' ||
      (error.response.status >= 500 && error.response.status < 600)
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private handleError(error: any): ApiError {
    const apiError = new ApiError(
      error.response?.data?.detail || error.message || 'An unexpected error occurred',
      error.response?.status || 0,
      error.response?.data || null,
      error.config?.url || '',
      error.config?.method || ''
    );

    // Log error for debugging
    console.error('API Error:', {
      message: apiError.message,
      status: apiError.status,
      url: apiError.url,
      method: apiError.method,
      data: apiError.data,
    });

    return apiError;
  }

  // Generic request methods with type safety
  async get<T = any>(url: string, options: RequestOptions = {}): Promise<T> {
    const response = await this.api.get<T>(url, options);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const response = await this.api.post<T>(url, data, options);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const response = await this.api.put<T>(url, data, options);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<T> {
    const response = await this.api.patch<T>(url, data, options);
    return response.data;
  }

  async delete<T = any>(url: string, options: RequestOptions = {}): Promise<T> {
    const response = await this.api.delete<T>(url, options);
    return response.data;
  }

  // Upload file with progress tracking
  async uploadFile<T = any>(
    url: string,
    file: File,
    data: Record<string, any> = {},
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, value.toString());
      }
    });

    const response = await this.api.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.get('/health', { skipAuth: true });
  }

  // Set auth token
  setAuthToken(token: string) {
    localStorage.setItem('token', token);
  }

  // Clear auth token
  clearAuthToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  // Get current auth token
  getAuthToken(): string | null {
    return localStorage.getItem('token');
  }
}

// Custom error class for API errors
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public data: any = null,
    public url: string = '',
    public method: string = ''
  ) {
    super(message);
    this.name = 'ApiError';
  }

  // Check if error is a specific type
  isNetworkError(): boolean {
    return this.status === 0;
  }

  isServerError(): boolean {
    return this.status >= 500 && this.status < 600;
  }

  isClientError(): boolean {
    return this.status >= 400 && this.status < 500;
  }

  isUnauthorized(): boolean {
    return this.status === 401;
  }

  isForbidden(): boolean {
    return this.status === 403;
  }

  isNotFound(): boolean {
    return this.status === 404;
  }

  isValidationError(): boolean {
    return this.status === 422;
  }
}

// Create default API client instance
export const apiClient = new ApiClient();

// Export types
export type { ApiResponse };
