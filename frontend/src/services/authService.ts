/**
 * Authentication service for API calls
 */
import { apiClient } from './apiClient';
import { User, AuthResponse, LoginRequest, SignupRequest, ApiResponse } from '../types';

class AuthService {
  setToken(token: string) {
    apiClient.setAuthToken(token);
  }

  clearToken() {
    apiClient.clearAuthToken();
  }

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const formData = new FormData();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);

    return apiClient.post<AuthResponse>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      skipAuth: true,
    });
  }

  async signup(userData: SignupRequest): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/auth/signup', userData, { skipAuth: true });
  }

  async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Ignore logout errors
      console.warn('Logout request failed:', error);
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>('/users/me');
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    return apiClient.put<User>('/users/me', userData);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>('/users/me/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  }

  async updatePreferences(preferences: Record<string, any>): Promise<ApiResponse> {
    return apiClient.put<ApiResponse>('/users/me/preferences', preferences);
  }

  // Health check
  async healthCheck(): Promise<any> {
    return apiClient.healthCheck();
  }
}

export const authService = new AuthService();
