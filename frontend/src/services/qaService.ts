/**
 * Q&A service for API calls
 */
import { apiClient } from './apiClient';
import {
  QARequest,
  QAResponse,
  Citation,
  QAHistoryItem,
  PaginatedResponse
} from '../types';

class QAService {

  async askQuestion(qaRequest: QARequest): Promise<QAResponse> {
    return apiClient.post<QAResponse>('/qa/ask', qaRequest);
  }

  async getCitationDetails(citationId: string): Promise<any> {
    return apiClient.get(`/qa/citation/${citationId}`);
  }

  async getQAHistory(limit: number = 50): Promise<PaginatedResponse<QAHistoryItem>> {
    return apiClient.get<PaginatedResponse<QAHistoryItem>>('/qa/history', {
      params: { limit },
    });
  }

  async submitFeedback(
    answerId: string,
    rating: number,
    feedbackText?: string,
    helpfulCitations?: string[]
  ): Promise<any> {
    return apiClient.post('/qa/feedback', {
      answer_id: answerId,
      rating,
      feedback_text: feedbackText,
      helpful_citations: helpfulCitations,
    });
  }

  async getQAHealth(): Promise<any> {
    return apiClient.get('/qa/health');
  }
}

export const qaService = new QAService();
