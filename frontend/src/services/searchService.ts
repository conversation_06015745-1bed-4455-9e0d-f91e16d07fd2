/**
 * Search service for API calls
 */
import { apiClient } from './apiClient';
import {
  SearchRequest,
  SearchResponse,
  SearchResult,
  SearchHistoryItem,
  PaginatedResponse
} from '../types';

class SearchService {

  async search(searchRequest: SearchRequest): Promise<SearchResponse> {
    return apiClient.post<SearchResponse>('/search', searchRequest);
  }

  async getSuggestions(query: string, limit: number = 10): Promise<string[]> {
    const response = await apiClient.get<{ suggestions: string[] }>('/search/suggestions', {
      params: { q: query, limit },
    });
    return response.suggestions;
  }

  async findSimilarChunks(
    chunkId: string,
    limit: number = 10,
    scoreThreshold: number = 0.8,
    excludeSameBook: boolean = false
  ): Promise<SearchResult[]> {
    return apiClient.post<SearchResult[]>('/search/similar', {
      chunk_id: chunkId,
      limit,
      score_threshold: scoreThreshold,
      exclude_same_book: excludeSameBook,
    });
  }

  async getSearchHistory(limit: number = 50): Promise<PaginatedResponse<SearchHistoryItem>> {
    return apiClient.get<PaginatedResponse<SearchHistoryItem>>('/search/history', {
      params: { limit },
    });
  }

  async getSearchAnalytics(): Promise<any> {
    return apiClient.get('/search/analytics');
  }
}

export const searchService = new SearchService();
